// Reports management module

class ReportsManager {
    constructor() {
        this.charts = {};
        this.reportData = {};
        this.currentReport = 'financial';
        
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
    }

    setupEventListeners() {
        // Report type selection
        const reportTypeSelect = document.getElementById('report-type');
        if (reportTypeSelect) {
            reportTypeSelect.addEventListener('change', (e) => {
                this.currentReport = e.target.value;
                this.loadReport();
            });
        }

        // Date range filters
        const dateFromInput = document.getElementById('report-date-from');
        const dateToInput = document.getElementById('report-date-to');
        
        if (dateFromInput && dateToInput) {
            dateFromInput.addEventListener('change', () => this.loadReport());
            dateToInput.addEventListener('change', () => this.loadReport());
        }
    }

    async loadReports() {
        try {
            UIUtils.showLoading();

            // Wait for database module to be loaded
            await this.waitForDatabaseModule();

            // Check if database is available
            if (!window.database || !window.database.db) {
                throw new Error('قاعدة البيانات غير متاحة');
            }

            // Check if Chart.js is available
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded, charts will not be available');
            }

            // Load reports page content if not already loaded
            await this.loadReportsPageContent();

            // Load default report
            await this.loadReport();

        } catch (error) {
            console.error('Error loading reports:', error);
            UIUtils.showNotification(`خطأ في تحميل التقارير: ${error.message}`, 'danger');

            // Show empty state
            this.reportData = {};
            this.charts = {};
        } finally {
            UIUtils.hideLoading();
        }
    }

    async waitForDatabaseModule() {
        console.log('🔍 Waiting for database module (Reports)...');

        // If already loaded, return immediately
        if (window.database && window.databaseModuleLoaded) {
            console.log('✅ Database module already loaded (Reports)');
            return;
        }

        // Wait for database module to load
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Database module loading timeout (Reports)'));
            }, 10000); // 10 second timeout

            const checkDatabase = () => {
                if (window.database && window.databaseModuleLoaded) {
                    clearTimeout(timeout);
                    console.log('✅ Database module loaded successfully (Reports)');
                    resolve();
                } else {
                    setTimeout(checkDatabase, 100);
                }
            };

            // Also listen for the database module loaded event
            window.addEventListener('database-module-loaded', () => {
                clearTimeout(timeout);
                console.log('✅ Database module loaded via event (Reports)');
                resolve();
            }, { once: true });

            checkDatabase();
        });
    }

    async loadReportsPageContent() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        const pageElement = document.getElementById('reports-page');
        if (!pageElement) {
            console.error('Reports page element not found, available elements:',
                Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            throw new Error('عنصر صفحة التقارير غير موجود في HTML');
        }

        if (pageElement.innerHTML.trim()) {
            console.log('Reports page content already loaded');
            return;
        }

        pageElement.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="bi bi-graph-up"></i>
                            التقارير المالية
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-outline-success" onclick="window.ReportsManager.exportCurrentReport()">
                                <i class="bi bi-download"></i>
                                تصدير
                            </button>
                            <button class="btn btn-outline-info" onclick="window.ReportsManager.printCurrentReport()">
                                <i class="bi bi-printer"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Controls -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="report-type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report-type">
                                <option value="financial">التقرير المالي الشامل</option>
                                <option value="monthly">التقرير الشهري</option>
                                <option value="annual">التقرير السنوي</option>
                                <option value="performance">تقرير الأداء المالي</option>
                                <option value="members">تقرير الأعضاء</option>
                                <option value="overdue">تقرير المتأخرات</option>
                                <option value="categories">تقرير حسب الفئات</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="report-date-from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="report-date-from" 
                                   value="${moment().subtract(1, 'month').format('YYYY-MM-DD')}">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="report-date-to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="report-date-to" 
                                   value="${moment().format('YYYY-MM-DD')}">
                        </div>
                        
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-primary w-100" onclick="window.ReportsManager.loadReport()">
                                <i class="bi bi-arrow-clockwise"></i>
                                تحديث التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div id="report-content">
                <!-- Report content will be loaded here -->
            </div>
        `;

        // Set up event listeners after content is loaded
        setTimeout(() => this.setupEventListeners(), 100);
    }

    async loadReport() {
        const reportType = document.getElementById('report-type')?.value || 'financial';
        const dateFrom = document.getElementById('report-date-from')?.value;
        const dateTo = document.getElementById('report-date-to')?.value;

        try {
            UIUtils.showLoading();

            switch (reportType) {
                case 'financial':
                    await this.loadFinancialReport(dateFrom, dateTo);
                    break;
                case 'monthly':
                    await this.loadMonthlyReport(dateFrom, dateTo);
                    break;
                case 'annual':
                    await this.loadAnnualReport();
                    break;
                case 'performance':
                    await this.loadPerformanceReport(dateFrom, dateTo);
                    break;
                case 'members':
                    await this.loadMembersReport();
                    break;
                case 'overdue':
                    await this.loadOverdueReport();
                    break;
                case 'categories':
                    await this.loadCategoriesReport(dateFrom, dateTo);
                    break;
            }

        } catch (error) {
            console.error('Error loading report:', error);
            UIUtils.showNotification('خطأ في تحميل التقرير', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    async loadFinancialReport(dateFrom, dateTo) {
        try {
            // Get detailed financial data
            const revenueData = await database.all(`
                SELECT
                    DATE(payment_date) as date,
                    SUM(amount) as total,
                    COUNT(*) as count,
                    AVG(amount) as average
                FROM payments
                WHERE payment_date BETWEEN ? AND ?
                GROUP BY DATE(payment_date)
                ORDER BY payment_date
            `, [dateFrom, dateTo]);

            // Check if expenses table exists, if not create empty data
            let expenseData = [];
            try {
                expenseData = await database.all(`
                    SELECT
                        DATE(expense_date) as date,
                        SUM(amount) as total,
                        COUNT(*) as count,
                        AVG(amount) as average
                    FROM expenses
                    WHERE expense_date BETWEEN ? AND ?
                    GROUP BY DATE(expense_date)
                    ORDER BY expense_date
                `, [dateFrom, dateTo]);
            } catch (error) {
                console.warn('Expenses table not found, using empty data:', error);
                expenseData = [];
            }

            // Get monthly summary for trend analysis
            const monthlyRevenue = await database.all(`
                SELECT
                    strftime('%Y-%m', payment_date) as month,
                    SUM(amount) as total,
                    COUNT(*) as count,
                    AVG(amount) as average
                FROM payments
                WHERE payment_date BETWEEN ? AND ?
                GROUP BY strftime('%Y-%m', payment_date)
                ORDER BY month
            `, [dateFrom, dateTo]);

            let monthlyExpenses = [];
            try {
                monthlyExpenses = await database.all(`
                    SELECT
                        strftime('%Y-%m', expense_date) as month,
                        SUM(amount) as total,
                        COUNT(*) as count,
                        AVG(amount) as average
                    FROM expenses
                    WHERE expense_date BETWEEN ? AND ?
                    GROUP BY strftime('%Y-%m', expense_date)
                    ORDER BY month
                `, [dateFrom, dateTo]);
            } catch (error) {
                console.warn('Monthly expenses query failed, using empty data:', error);
                monthlyExpenses = [];
            }

            // Calculate totals and metrics with safe parsing
            const totalRevenue = revenueData.reduce((sum, item) => {
                const amount = parseFloat(item.total || 0);
                return sum + (isNaN(amount) ? 0 : amount);
            }, 0);

            const totalExpenses = expenseData.reduce((sum, item) => {
                const amount = parseFloat(item.total || 0);
                return sum + (isNaN(amount) ? 0 : amount);
            }, 0);

            const netIncome = totalRevenue - totalExpenses;

            const totalTransactions = revenueData.reduce((sum, item) => {
                const count = parseInt(item.count || 0);
                return sum + (isNaN(count) ? 0 : count);
            }, 0) + expenseData.reduce((sum, item) => {
                const count = parseInt(item.count || 0);
                return sum + (isNaN(count) ? 0 : count);
            }, 0);

            console.log('📊 Financial totals calculated:');
            console.log('- Total Revenue:', totalRevenue);
            console.log('- Total Expenses:', totalExpenses);
            console.log('- Net Income:', netIncome);
            console.log('- Total Transactions:', totalTransactions);

        // Calculate averages
        const avgDailyRevenue = revenueData.length > 0 ? totalRevenue / revenueData.length : 0;
        const avgDailyExpense = expenseData.length > 0 ? totalExpenses / expenseData.length : 0;
        const avgTransactionValue = totalTransactions > 0 ? (totalRevenue + totalExpenses) / totalTransactions : 0;

            // Get category breakdown with enhanced details
            const revenueByCategory = await database.all(`
                SELECT
                    COALESCE(rc.name, 'غير محدد') as category,
                    SUM(p.amount) as total,
                    COUNT(*) as count,
                    AVG(p.amount) as average,
                    MIN(p.amount) as min_amount,
                    MAX(p.amount) as max_amount
                FROM payments p
                LEFT JOIN revenue_categories rc ON p.category_id = rc.id
                WHERE p.payment_date BETWEEN ? AND ?
                GROUP BY rc.id, rc.name
                ORDER BY total DESC
            `, [dateFrom, dateTo]);

            let expensesByCategory = [];
            try {
                expensesByCategory = await database.all(`
                    SELECT
                        COALESCE(ec.name, 'غير محدد') as category,
                        SUM(e.amount) as total,
                        COUNT(*) as count,
                        AVG(e.amount) as average,
                        MIN(e.amount) as min_amount,
                        MAX(e.amount) as max_amount
                    FROM expenses e
                    LEFT JOIN expense_categories ec ON e.category_id = ec.id
                    WHERE e.expense_date BETWEEN ? AND ?
                    GROUP BY ec.id, ec.name
                    ORDER BY total DESC
                `, [dateFrom, dateTo]);
            } catch (error) {
                console.warn('Expenses by category query failed, using empty data:', error);
                expensesByCategory = [];
            }

        // Get member payment statistics
        const memberStats = await database.all(`
            SELECT
                m.full_name,
                m.membership_id,
                COUNT(p.id) as payment_count,
                SUM(p.amount) as total_paid,
                AVG(p.amount) as avg_payment,
                MAX(p.payment_date) as last_payment_date
            FROM members m
            LEFT JOIN payments p ON m.id = p.member_id
                AND p.payment_date BETWEEN ? AND ?
            WHERE m.status = 'active'
            GROUP BY m.id, m.full_name, m.membership_id
            ORDER BY total_paid DESC
            LIMIT 10
        `, [dateFrom, dateTo]);

            // Calculate financial health indicators
            const profitMargin = totalRevenue > 0 ? ((netIncome / totalRevenue) * 100) : 0;
            const expenseRatio = totalRevenue > 0 ? ((totalExpenses / totalRevenue) * 100) : 0;
            const growthRate = this.calculateGrowthRate(monthlyRevenue);

            this.renderFinancialReport({
                dateFrom,
                dateTo,
                totalRevenue,
                totalExpenses,
                netIncome,
                totalTransactions,
                avgDailyRevenue,
                avgDailyExpense,
                avgTransactionValue,
                profitMargin,
                expenseRatio,
                growthRate,
                revenueData,
                expenseData,
                monthlyRevenue,
                monthlyExpenses,
                revenueByCategory,
                expensesByCategory,
                memberStats
            });

        } catch (error) {
            console.error('Error loading financial report:', error);
            UIUtils.showNotification('خطأ في تحميل التقرير المالي: ' + error.message, 'danger');

            // Show error message in report content
            const reportContent = document.getElementById('report-content');
            if (reportContent) {
                reportContent.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                        <h4 class="mt-3">خطأ في تحميل التقرير</h4>
                        <p>حدث خطأ أثناء تحميل البيانات المالية. يرجى المحاولة مرة أخرى.</p>
                        <button class="btn btn-primary" onclick="window.ReportsManager.loadReport()">
                            <i class="bi bi-arrow-clockwise"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }
    }

    async loadMonthlyReport(dateFrom, dateTo) {
        try {
            console.log('📅 Loading monthly report for:', dateFrom, 'to', dateTo);

            // Get monthly revenue data
            const monthlyRevenue = await database.all(`
                SELECT
                    strftime('%Y-%m', payment_date) as month,
                    SUM(amount) as total,
                    COUNT(*) as count,
                    AVG(amount) as average,
                    MIN(amount) as min_amount,
                    MAX(amount) as max_amount
                FROM payments
                WHERE payment_date BETWEEN ? AND ?
                GROUP BY strftime('%Y-%m', payment_date)
                ORDER BY month
            `, [dateFrom, dateTo]);

            // Get monthly expense data
            let monthlyExpenses = [];
            try {
                monthlyExpenses = await database.all(`
                    SELECT
                        strftime('%Y-%m', expense_date) as month,
                        SUM(amount) as total,
                        COUNT(*) as count,
                        AVG(amount) as average,
                        MIN(amount) as min_amount,
                        MAX(amount) as max_amount
                    FROM expenses
                    WHERE expense_date BETWEEN ? AND ?
                    GROUP BY strftime('%Y-%m', expense_date)
                    ORDER BY month
                `, [dateFrom, dateTo]);
            } catch (error) {
                console.warn('Monthly expenses query failed, using empty data:', error);
                monthlyExpenses = [];
            }

            // Get monthly member statistics
            const monthlyMemberStats = await database.all(`
                SELECT
                    strftime('%Y-%m', p.payment_date) as month,
                    COUNT(DISTINCT p.member_id) as active_members,
                    COUNT(p.id) as total_payments
                FROM payments p
                WHERE p.payment_date BETWEEN ? AND ?
                GROUP BY strftime('%Y-%m', p.payment_date)
                ORDER BY month
            `, [dateFrom, dateTo]);

            // Calculate monthly totals and metrics
            const monthlyData = this.calculateMonthlyMetrics(monthlyRevenue, monthlyExpenses, monthlyMemberStats);

            this.renderMonthlyReport({
                dateFrom,
                dateTo,
                monthlyRevenue,
                monthlyExpenses,
                monthlyMemberStats,
                monthlyData
            });

        } catch (error) {
            console.error('Error loading monthly report:', error);
            UIUtils.showNotification('خطأ في تحميل التقرير الشهري: ' + error.message, 'danger');

            const reportContent = document.getElementById('report-content');
            if (reportContent) {
                reportContent.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                        <h4 class="mt-3">خطأ في تحميل التقرير الشهري</h4>
                        <p>حدث خطأ أثناء تحميل البيانات الشهرية. يرجى المحاولة مرة أخرى.</p>
                        <button class="btn btn-primary" onclick="window.ReportsManager.loadReport()">
                            <i class="bi bi-arrow-clockwise"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }
    }

    calculateMonthlyMetrics(monthlyRevenue, monthlyExpenses, monthlyMemberStats) {
        // Merge all months
        const allMonths = [...new Set([
            ...monthlyRevenue.map(item => item.month),
            ...monthlyExpenses.map(item => item.month),
            ...monthlyMemberStats.map(item => item.month)
        ])].sort();

        return allMonths.map(month => {
            const revenueItem = monthlyRevenue.find(r => r.month === month);
            const expenseItem = monthlyExpenses.find(e => e.month === month);
            const memberItem = monthlyMemberStats.find(m => m.month === month);

            const revenue = parseFloat(revenueItem?.total || 0);
            const expenses = parseFloat(expenseItem?.total || 0);
            const netIncome = revenue - expenses;
            const activeMembers = parseInt(memberItem?.active_members || 0);
            const totalPayments = parseInt(memberItem?.total_payments || 0);

            return {
                month,
                revenue,
                expenses,
                netIncome,
                activeMembers,
                totalPayments,
                revenueCount: parseInt(revenueItem?.count || 0),
                expenseCount: parseInt(expenseItem?.count || 0),
                avgRevenue: parseFloat(revenueItem?.average || 0),
                avgExpense: parseFloat(expenseItem?.average || 0),
                profitMargin: revenue > 0 ? ((netIncome / revenue) * 100) : 0
            };
        });
    }

    renderMonthlyReport(data) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const { monthlyData } = data;

        // Calculate summary statistics
        const totalRevenue = monthlyData.reduce((sum, item) => sum + item.revenue, 0);
        const totalExpenses = monthlyData.reduce((sum, item) => sum + item.expenses, 0);
        const totalNetIncome = totalRevenue - totalExpenses;
        const avgMonthlyRevenue = monthlyData.length > 0 ? totalRevenue / monthlyData.length : 0;
        const avgMonthlyExpenses = monthlyData.length > 0 ? totalExpenses / monthlyData.length : 0;

        reportContent.innerHTML = `
            <!-- Monthly Report Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>
                                <i class="bi bi-calendar-month"></i>
                                التقرير الشهري
                            </h3>
                            <p class="mb-0">من ${DateUtils.formatDate(data.dateFrom)} إلى ${DateUtils.formatDate(data.dateTo)}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${NumberUtils.formatCurrency(totalRevenue)}</h4>
                            <p class="mb-0">إجمالي الإيرادات</p>
                            <small>متوسط شهري: ${NumberUtils.formatCurrency(avgMonthlyRevenue)}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4>${NumberUtils.formatCurrency(totalExpenses)}</h4>
                            <p class="mb-0">إجمالي المصروفات</p>
                            <small>متوسط شهري: ${NumberUtils.formatCurrency(avgMonthlyExpenses)}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card ${totalNetIncome >= 0 ? 'bg-primary' : 'bg-warning'} text-white">
                        <div class="card-body text-center">
                            <h4>${NumberUtils.formatCurrency(totalNetIncome)}</h4>
                            <p class="mb-0">صافي الدخل</p>
                            <small>الحالة: ${totalNetIncome >= 0 ? 'مربحة' : 'خاسرة'}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>${monthlyData.length}</h4>
                            <p class="mb-0">عدد الأشهر</p>
                            <small>في الفترة المحددة</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Data Table -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-table"></i>
                                البيانات الشهرية التفصيلية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>الشهر</th>
                                            <th class="text-success">الإيرادات</th>
                                            <th class="text-danger">المصروفات</th>
                                            <th class="text-primary">صافي الدخل</th>
                                            <th>هامش الربح</th>
                                            <th>الأعضاء النشطون</th>
                                            <th>عدد المدفوعات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${monthlyData.map(item => {
                                            const [year, monthNum] = item.month.split('-');
                                            const monthNames = [
                                                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                                            ];
                                            const monthName = `${monthNames[parseInt(monthNum) - 1]} ${year}`;

                                            return `
                                                <tr>
                                                    <td><strong>${monthName}</strong></td>
                                                    <td class="text-success">${NumberUtils.formatCurrency(item.revenue)}</td>
                                                    <td class="text-danger">${NumberUtils.formatCurrency(item.expenses)}</td>
                                                    <td class="${item.netIncome >= 0 ? 'text-success' : 'text-danger'}">
                                                        ${NumberUtils.formatCurrency(item.netIncome)}
                                                    </td>
                                                    <td>
                                                        <span class="badge ${item.profitMargin >= 0 ? 'bg-success' : 'bg-danger'}">
                                                            ${item.profitMargin.toFixed(1)}%
                                                        </span>
                                                    </td>
                                                    <td>${item.activeMembers}</td>
                                                    <td>${item.totalPayments}</td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Trend Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up"></i>
                                الاتجاه الشهري
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthly-report-chart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Render chart
        setTimeout(() => {
            this.renderMonthlyReportChart(monthlyData);
        }, 500);

        // Store data for export
        this.reportData = data;

        console.log('✅ Monthly report rendered successfully');
    }

    renderMonthlyReportChart(monthlyData) {
        const ctx = document.getElementById('monthly-report-chart');
        if (!ctx) {
            console.warn('Monthly report chart canvas not found');
            return;
        }

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, showing data table instead');
            ctx.parentElement.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    الرسم البياني غير متاح، البيانات معروضة في الجدول أعلاه
                </div>
            `;
            return;
        }

        // Destroy existing chart
        if (this.charts.monthlyReport) {
            this.charts.monthlyReport.destroy();
        }

        // Prepare data
        const labels = monthlyData.map(item => {
            const [year, monthNum] = item.month.split('-');
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            return `${monthNames[parseInt(monthNum) - 1]} ${year}`;
        });

        const revenueData = monthlyData.map(item => item.revenue);
        const expenseData = monthlyData.map(item => item.expenses);
        const netIncomeData = monthlyData.map(item => item.netIncome);

        this.charts.monthlyReport = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'الإيرادات',
                        data: revenueData,
                        backgroundColor: 'rgba(25, 135, 84, 0.8)',
                        borderColor: '#198754',
                        borderWidth: 1
                    },
                    {
                        label: 'المصروفات',
                        data: expenseData,
                        backgroundColor: 'rgba(220, 53, 69, 0.8)',
                        borderColor: '#dc3545',
                        borderWidth: 1
                    },
                    {
                        label: 'صافي الدخل',
                        data: netIncomeData,
                        type: 'line',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderColor: '#0d6efd',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return NumberUtils.formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = NumberUtils.formatCurrency(context.parsed.y);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    calculateGrowthRate(monthlyData) {
        if (monthlyData.length < 2) return 0;

        const firstMonth = parseFloat(monthlyData[0].total);
        const lastMonth = parseFloat(monthlyData[monthlyData.length - 1].total);

        if (firstMonth === 0) return 0;

        return ((lastMonth - firstMonth) / firstMonth) * 100;
    }

    renderDataTable(container, revenueData, expenseData, title) {
        // Create a data table when charts are not available
        const allDates = [...new Set([
            ...revenueData.map(item => item.date),
            ...expenseData.map(item => item.date)
        ])].sort();

        let html = `
            <div class="chart-fallback">
                <h6 class="text-center mb-3">
                    <i class="bi bi-table"></i>
                    ${title}
                </h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th class="text-success">الإيرادات</th>
                                <th class="text-danger">المصروفات</th>
                                <th class="text-primary">صافي الدخل</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        allDates.forEach(date => {
            const revenueItem = revenueData.find(r => r.date === date);
            const expenseItem = expenseData.find(e => e.date === date);

            const revenue = revenueItem ? parseFloat(revenueItem.total) : 0;
            const expense = expenseItem ? parseFloat(expenseItem.total) : 0;
            const netIncome = revenue - expense;

            html += `
                <tr>
                    <td>${DateUtils.formatDate(date)}</td>
                    <td class="text-success">${NumberUtils.formatCurrency(revenue)}</td>
                    <td class="text-danger">${NumberUtils.formatCurrency(expense)}</td>
                    <td class="${netIncome >= 0 ? 'text-success' : 'text-danger'}">${NumberUtils.formatCurrency(netIncome)}</td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="bi bi-info-circle"></i>
                    يتم عرض البيانات في شكل جدول لأن مكتبة الرسوم البيانية غير متاحة حالياً
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    renderFinancialReport(data) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) {
            console.error('Report content element not found');
            return;
        }

        // Validate data before rendering
        console.log('📊 Rendering financial report with data:', {
            totalRevenue: data.totalRevenue,
            totalExpenses: data.totalExpenses,
            netIncome: data.netIncome,
            revenueDataLength: data.revenueData?.length || 0,
            expenseDataLength: data.expenseData?.length || 0
        });

        // Ensure all values are numbers
        const safeData = {
            ...data,
            totalRevenue: parseFloat(data.totalRevenue || 0),
            totalExpenses: parseFloat(data.totalExpenses || 0),
            netIncome: parseFloat(data.netIncome || 0),
            totalTransactions: parseInt(data.totalTransactions || 0),
            avgDailyRevenue: parseFloat(data.avgDailyRevenue || 0),
            avgDailyExpense: parseFloat(data.avgDailyExpense || 0),
            avgTransactionValue: parseFloat(data.avgTransactionValue || 0),
            profitMargin: parseFloat(data.profitMargin || 0),
            expenseRatio: parseFloat(data.expenseRatio || 0),
            growthRate: parseFloat(data.growthRate || 0)
        };

        reportContent.innerHTML = `
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(safeData.totalRevenue)}</h3>
                            <p class="mb-0">إجمالي الإيرادات</p>
                            <small>متوسط يومي: ${NumberUtils.formatCurrency(safeData.avgDailyRevenue)}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(safeData.totalExpenses)}</h3>
                            <p class="mb-0">إجمالي المصروفات</p>
                            <small>متوسط يومي: ${NumberUtils.formatCurrency(safeData.avgDailyExpense)}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card ${safeData.netIncome >= 0 ? 'bg-primary' : 'bg-warning'} text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(safeData.netIncome)}</h3>
                            <p class="mb-0">صافي الدخل</p>
                            <small>هامش الربح: ${safeData.profitMargin.toFixed(1)}%</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatNumber(safeData.totalTransactions)}</h3>
                            <p class="mb-0">إجمالي المعاملات</p>
                            <small>متوسط القيمة: ${NumberUtils.formatCurrency(safeData.avgTransactionValue)}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Health Indicators -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5 class="card-title">نسبة المصروفات</h5>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar ${safeData.expenseRatio > 80 ? 'bg-danger' : safeData.expenseRatio > 60 ? 'bg-warning' : 'bg-success'}"
                                     style="width: ${Math.min(safeData.expenseRatio, 100)}%">
                                    ${safeData.expenseRatio.toFixed(1)}%
                                </div>
                            </div>
                            <small class="text-muted">من إجمالي الإيرادات</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5 class="card-title">معدل النمو</h5>
                            <h3 class="${safeData.growthRate >= 0 ? 'text-success' : 'text-danger'}">
                                ${safeData.growthRate >= 0 ? '+' : ''}${safeData.growthRate.toFixed(1)}%
                            </h3>
                            <small class="text-muted">مقارنة بالفترة السابقة</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5 class="card-title">الحالة المالية</h5>
                            <h4 class="${safeData.netIncome >= 0 ? 'text-success' : 'text-danger'}">
                                ${safeData.netIncome >= 0 ? 'مربحة' : 'خاسرة'}
                            </h4>
                            <small class="text-muted">
                                ${safeData.netIncome >= 0 ? 'الإيرادات تفوق المصروفات' : 'المصروفات تفوق الإيرادات'}
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up"></i>
                                الإيرادات والمصروفات اليومية
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="daily-financial-chart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-pie-chart"></i>
                                نسبة الإيرادات للمصروفات
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenue-expense-pie-chart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Trend Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-bar-chart-line"></i>
                                الاتجاه الشهري للإيرادات والمصروفات
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthly-trend-chart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Members Section -->
            ${data.memberStats && data.memberStats.length > 0 ? `
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-trophy"></i>
                                أعلى الأعضاء دفعاً في الفترة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الترتيب</th>
                                            <th>اسم العضو</th>
                                            <th>رقم العضوية</th>
                                            <th>عدد الدفعات</th>
                                            <th>إجمالي المدفوع</th>
                                            <th>متوسط الدفعة</th>
                                            <th>آخر دفعة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.memberStats.map((member, index) => `
                                            <tr>
                                                <td>
                                                    <span class="badge ${index < 3 ? 'bg-warning' : 'bg-secondary'}">
                                                        ${index + 1}
                                                    </span>
                                                </td>
                                                <td><strong>${member.full_name}</strong></td>
                                                <td>${member.membership_id}</td>
                                                <td>${member.payment_count || 0}</td>
                                                <td>${NumberUtils.formatCurrency(member.total_paid || 0)}</td>
                                                <td>${NumberUtils.formatCurrency(member.avg_payment || 0)}</td>
                                                <td>${member.last_payment_date ? DateUtils.formatDate(member.last_payment_date) : '-'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}

            <!-- Category Breakdown -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الإيرادات حسب الفئة</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الفئة</th>
                                            <th>المبلغ</th>
                                            <th>العدد</th>
                                            <th>النسبة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.revenueByCategory.map(item => `
                                            <tr>
                                                <td>${item.category}</td>
                                                <td>${NumberUtils.formatCurrency(item.total)}</td>
                                                <td>${item.count}</td>
                                                <td>${((item.total / data.totalRevenue) * 100).toFixed(1)}%</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">المصروفات حسب الفئة</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الفئة</th>
                                            <th>المبلغ</th>
                                            <th>العدد</th>
                                            <th>النسبة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.expensesByCategory.map(item => `
                                            <tr>
                                                <td>${item.category}</td>
                                                <td>${NumberUtils.formatCurrency(item.total)}</td>
                                                <td>${item.count}</td>
                                                <td>${((item.total / data.totalExpenses) * 100).toFixed(1)}%</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Render charts
        setTimeout(() => {
            try {
                this.renderDailyFinancialChart(safeData.revenueData || [], safeData.expenseData || []);
                this.renderRevenueExpensePieChart(safeData.totalRevenue, safeData.totalExpenses);
                this.renderMonthlyTrendChart(safeData.monthlyRevenue || [], safeData.monthlyExpenses || []);
            } catch (error) {
                console.error('Error rendering charts:', error);
                // Charts will show fallback content automatically
            }
        }, 500); // Increased timeout to allow Chart.js to load

        // Store data for export
        this.reportData = safeData;

        console.log('✅ Financial report rendered successfully');
    }

    renderDailyFinancialChart(revenueData, expenseData) {
        const ctx = document.getElementById('daily-financial-chart');
        if (!ctx) {
            console.warn('Daily financial chart canvas not found');
            return;
        }

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, showing data table instead');
            this.renderDataTable(ctx.parentElement, revenueData, expenseData, 'الإيرادات والمصروفات اليومية');
            return;
        }

        // Destroy existing chart
        if (this.charts.dailyFinancial) {
            this.charts.dailyFinancial.destroy();
        }

        // Prepare data
        const allDates = [...new Set([
            ...revenueData.map(item => item.date),
            ...expenseData.map(item => item.date)
        ])].sort();

        const revenueAmounts = allDates.map(date => {
            const item = revenueData.find(r => r.date === date);
            return item ? parseFloat(item.total) : 0;
        });

        const expenseAmounts = allDates.map(date => {
            const item = expenseData.find(e => e.date === date);
            return item ? parseFloat(item.total) : 0;
        });

        this.charts.dailyFinancial = new Chart(ctx, {
            type: 'line',
            data: {
                labels: allDates.map(date => DateUtils.formatDate(date)),
                datasets: [
                    {
                        label: 'الإيرادات',
                        data: revenueAmounts,
                        borderColor: '#198754',
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'المصروفات',
                        data: expenseAmounts,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            font: { family: 'Noto Sans Arabic' }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' +
                                       NumberUtils.formatCurrency(context.parsed.y);
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return NumberUtils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }

    renderRevenueExpensePieChart(totalRevenue, totalExpenses) {
        const ctx = document.getElementById('revenue-expense-pie-chart');
        if (!ctx) {
            console.warn('Revenue expense pie chart canvas not found');
            return;
        }

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, skipping pie chart rendering');
            ctx.parentElement.innerHTML = '<div class="alert alert-warning">مكتبة الرسوم البيانية غير متاحة</div>';
            return;
        }

        // Destroy existing chart
        if (this.charts.revenueExpensePie) {
            this.charts.revenueExpensePie.destroy();
        }

        this.charts.revenueExpensePie = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['الإيرادات', 'المصروفات'],
                datasets: [{
                    data: [totalRevenue, totalExpenses],
                    backgroundColor: ['#198754', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: { family: 'Noto Sans Arabic' }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' +
                                       NumberUtils.formatCurrency(context.parsed) +
                                       ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    renderMonthlyTrendChart(monthlyRevenue, monthlyExpenses) {
        const ctx = document.getElementById('monthly-trend-chart');
        if (!ctx) {
            console.warn('Monthly trend chart canvas not found');
            return;
        }

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available');
            ctx.parentElement.innerHTML = '<div class="alert alert-warning">مكتبة الرسوم البيانية غير متاحة</div>';
            return;
        }

        // Ensure data is available
        if (!monthlyRevenue) monthlyRevenue = [];
        if (!monthlyExpenses) monthlyExpenses = [];

        // Destroy existing chart
        if (this.charts.monthlyTrend) {
            this.charts.monthlyTrend.destroy();
        }

        // Merge monthly data
        const allMonths = [...new Set([
            ...monthlyRevenue.map(item => item.month),
            ...monthlyExpenses.map(item => item.month)
        ])].sort();

        const revenueAmounts = allMonths.map(month => {
            const item = monthlyRevenue.find(r => r.month === month);
            return item ? parseFloat(item.total) : 0;
        });

        const expenseAmounts = allMonths.map(month => {
            const item = monthlyExpenses.find(e => e.month === month);
            return item ? parseFloat(item.total) : 0;
        });

        const netIncomes = allMonths.map((month, index) => {
            return revenueAmounts[index] - expenseAmounts[index];
        });

        // Format month labels
        const monthLabels = allMonths.map(month => {
            const [year, monthNum] = month.split('-');
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            return `${monthNames[parseInt(monthNum) - 1]} ${year}`;
        });

        this.charts.monthlyTrend = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: monthLabels,
                datasets: [
                    {
                        label: 'الإيرادات',
                        data: revenueAmounts,
                        backgroundColor: 'rgba(25, 135, 84, 0.8)',
                        borderColor: '#198754',
                        borderWidth: 1
                    },
                    {
                        label: 'المصروفات',
                        data: expenseAmounts,
                        backgroundColor: 'rgba(220, 53, 69, 0.8)',
                        borderColor: '#dc3545',
                        borderWidth: 1
                    },
                    {
                        label: 'صافي الدخل',
                        data: netIncomes,
                        type: 'line',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderColor: '#0d6efd',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return NumberUtils.formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = NumberUtils.formatCurrency(context.parsed.y);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    async loadAnnualReport() {
        try {
            console.log('📅 Loading annual report...');

            // Get current year data
            const currentYear = new Date().getFullYear();
            const startDate = `${currentYear}-01-01`;
            const endDate = `${currentYear}-12-31`;

            // Get yearly data by month
            const yearlyRevenue = await database.all(`
                SELECT
                    strftime('%Y-%m', payment_date) as month,
                    SUM(amount) as total,
                    COUNT(*) as count,
                    AVG(amount) as average
                FROM payments
                WHERE payment_date BETWEEN ? AND ?
                GROUP BY strftime('%Y-%m', payment_date)
                ORDER BY month
            `, [startDate, endDate]);

            // Get yearly expenses by month
            let yearlyExpenses = [];
            try {
                yearlyExpenses = await database.all(`
                    SELECT
                        strftime('%Y-%m', expense_date) as month,
                        SUM(amount) as total,
                        COUNT(*) as count,
                        AVG(amount) as average
                    FROM expenses
                    WHERE expense_date BETWEEN ? AND ?
                    GROUP BY strftime('%Y-%m', expense_date)
                    ORDER BY month
                `, [startDate, endDate]);
            } catch (error) {
                console.warn('Yearly expenses query failed, using empty data:', error);
                yearlyExpenses = [];
            }

            // Get quarterly data
            const quarterlyData = this.calculateQuarterlyData(yearlyRevenue, yearlyExpenses);

            // Calculate annual totals
            const totalRevenue = yearlyRevenue.reduce((sum, item) => sum + parseFloat(item.total || 0), 0);
            const totalExpenses = yearlyExpenses.reduce((sum, item) => sum + parseFloat(item.total || 0), 0);
            const netIncome = totalRevenue - totalExpenses;

            this.renderAnnualReport({
                currentYear,
                yearlyRevenue,
                yearlyExpenses,
                quarterlyData,
                totalRevenue,
                totalExpenses,
                netIncome
            });

        } catch (error) {
            console.error('Error loading annual report:', error);
            UIUtils.showNotification('خطأ في تحميل التقرير السنوي: ' + error.message, 'danger');

            const reportContent = document.getElementById('report-content');
            if (reportContent) {
                reportContent.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                        <h4 class="mt-3">خطأ في تحميل التقرير السنوي</h4>
                        <p>حدث خطأ أثناء تحميل البيانات السنوية. يرجى المحاولة مرة أخرى.</p>
                        <button class="btn btn-primary" onclick="window.ReportsManager.loadReport()">
                            <i class="bi bi-arrow-clockwise"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }
    }

    calculateQuarterlyData(yearlyRevenue, yearlyExpenses) {
        const quarters = [
            { name: 'الربع الأول', months: ['01', '02', '03'] },
            { name: 'الربع الثاني', months: ['04', '05', '06'] },
            { name: 'الربع الثالث', months: ['07', '08', '09'] },
            { name: 'الربع الرابع', months: ['10', '11', '12'] }
        ];

        return quarters.map(quarter => {
            const revenue = yearlyRevenue
                .filter(item => quarter.months.includes(item.month.split('-')[1]))
                .reduce((sum, item) => sum + parseFloat(item.total || 0), 0);

            const expenses = yearlyExpenses
                .filter(item => quarter.months.includes(item.month.split('-')[1]))
                .reduce((sum, item) => sum + parseFloat(item.total || 0), 0);

            return {
                name: quarter.name,
                revenue,
                expenses,
                netIncome: revenue - expenses,
                profitMargin: revenue > 0 ? ((revenue - expenses) / revenue) * 100 : 0
            };
        });
    }

    renderAnnualReport(data) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const { currentYear, quarterlyData, totalRevenue, totalExpenses, netIncome } = data;

        reportContent.innerHTML = `
            <!-- Annual Report Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>
                                <i class="bi bi-calendar-year"></i>
                                التقرير السنوي ${currentYear}
                            </h3>
                            <p class="mb-0">ملخص الأداء المالي للعام الحالي</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Annual Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${NumberUtils.formatCurrency(totalRevenue)}</h4>
                            <p class="mb-0">إجمالي الإيرادات</p>
                            <small>للعام ${currentYear}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4>${NumberUtils.formatCurrency(totalExpenses)}</h4>
                            <p class="mb-0">إجمالي المصروفات</p>
                            <small>للعام ${currentYear}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card ${netIncome >= 0 ? 'bg-primary' : 'bg-warning'} text-white">
                        <div class="card-body text-center">
                            <h4>${NumberUtils.formatCurrency(netIncome)}</h4>
                            <p class="mb-0">صافي الدخل</p>
                            <small>للعام ${currentYear}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>${totalRevenue > 0 ? ((netIncome / totalRevenue) * 100).toFixed(1) : 0}%</h4>
                            <p class="mb-0">هامش الربح</p>
                            <small>للعام ${currentYear}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quarterly Data -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-bar-chart"></i>
                                البيانات الربعية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                ${quarterlyData.map((quarter, index) => `
                                    <div class="col-md-3">
                                        <div class="card border-${index % 2 === 0 ? 'primary' : 'info'}">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">${quarter.name}</h6>
                                                <div class="mb-2">
                                                    <small class="text-success">الإيرادات</small>
                                                    <div class="fw-bold">${NumberUtils.formatCurrency(quarter.revenue)}</div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-danger">المصروفات</small>
                                                    <div class="fw-bold">${NumberUtils.formatCurrency(quarter.expenses)}</div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-primary">صافي الدخل</small>
                                                    <div class="fw-bold ${quarter.netIncome >= 0 ? 'text-success' : 'text-danger'}">
                                                        ${NumberUtils.formatCurrency(quarter.netIncome)}
                                                    </div>
                                                </div>
                                                <div>
                                                    <span class="badge ${quarter.profitMargin >= 0 ? 'bg-success' : 'bg-danger'}">
                                                        ${quarter.profitMargin.toFixed(1)}%
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Annual Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up"></i>
                                الاتجاه السنوي
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="annual-report-chart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Render chart
        setTimeout(() => {
            this.renderAnnualReportChart(data.yearlyRevenue, data.yearlyExpenses);
        }, 500);

        // Store data for export
        this.reportData = data;

        console.log('✅ Annual report rendered successfully');
    }

    renderAnnualReportChart(yearlyRevenue, yearlyExpenses) {
        const ctx = document.getElementById('annual-report-chart');
        if (!ctx) {
            console.warn('Annual report chart canvas not found');
            return;
        }

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, showing message instead');
            ctx.parentElement.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    الرسم البياني غير متاح، البيانات معروضة في الجداول أعلاه
                </div>
            `;
            return;
        }

        // Destroy existing chart
        if (this.charts.annualReport) {
            this.charts.annualReport.destroy();
        }

        // Prepare data for all 12 months
        const months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        const revenueData = months.map(month => {
            const item = yearlyRevenue.find(r => r.month.endsWith('-' + month));
            return item ? parseFloat(item.total) : 0;
        });

        const expenseData = months.map(month => {
            const item = yearlyExpenses.find(e => e.month.endsWith('-' + month));
            return item ? parseFloat(item.total) : 0;
        });

        const netIncomeData = months.map((month, index) => {
            return revenueData[index] - expenseData[index];
        });

        this.charts.annualReport = new Chart(ctx, {
            type: 'line',
            data: {
                labels: monthNames,
                datasets: [
                    {
                        label: 'الإيرادات',
                        data: revenueData,
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        borderColor: '#198754',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'المصروفات',
                        data: expenseData,
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        borderColor: '#dc3545',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'صافي الدخل',
                        data: netIncomeData,
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderColor: '#0d6efd',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return NumberUtils.formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = NumberUtils.formatCurrency(context.parsed.y);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    async loadPerformanceReport(dateFrom, dateTo) {
        try {
            // Get current period data
            const currentData = await this.getFinancialData(dateFrom, dateTo);

            // Calculate previous period (same duration)
            const startDate = moment(dateFrom);
            const endDate = moment(dateTo);
            const duration = endDate.diff(startDate, 'days');

            const prevEndDate = startDate.clone().subtract(1, 'day');
            const prevStartDate = prevEndDate.clone().subtract(duration, 'days');

            const previousData = await this.getFinancialData(
                prevStartDate.format('YYYY-MM-DD'),
                prevEndDate.format('YYYY-MM-DD')
            );

            // Calculate year-over-year comparison
            const yearAgoStartDate = startDate.clone().subtract(1, 'year');
            const yearAgoEndDate = endDate.clone().subtract(1, 'year');

            const yearAgoData = await this.getFinancialData(
                yearAgoStartDate.format('YYYY-MM-DD'),
                yearAgoEndDate.format('YYYY-MM-DD')
            );

            // Calculate performance metrics
            const metrics = this.calculatePerformanceMetrics(currentData, previousData, yearAgoData);

            this.renderPerformanceReport({
                current: currentData,
                previous: previousData,
                yearAgo: yearAgoData,
                metrics,
                dateFrom,
                dateTo
            });

        } catch (error) {
            console.error('Error loading performance report:', error);
            UIUtils.showNotification('خطأ في تحميل تقرير الأداء: ' + error.message, 'danger');

            // Show error message in report content
            const reportContent = document.getElementById('report-content');
            if (reportContent) {
                reportContent.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                        <h4 class="mt-3">خطأ في تحميل تقرير الأداء</h4>
                        <p>حدث خطأ أثناء تحميل بيانات الأداء المالي. يرجى المحاولة مرة أخرى.</p>
                        <button class="btn btn-primary" onclick="window.ReportsManager.loadReport()">
                            <i class="bi bi-arrow-clockwise"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }
    }

    async getFinancialData(startDate, endDate) {
        try {
            const revenue = await database.get(`
                SELECT COALESCE(SUM(amount), 0) as total, COUNT(*) as count
                FROM payments
                WHERE payment_date BETWEEN ? AND ?
            `, [startDate, endDate]);

            let expenses = { total: 0, count: 0 };
            try {
                expenses = await database.get(`
                    SELECT COALESCE(SUM(amount), 0) as total, COUNT(*) as count
                    FROM expenses
                    WHERE expense_date BETWEEN ? AND ?
                `, [startDate, endDate]);
            } catch (error) {
                console.warn('Expenses table not found for performance report, using zero values');
                expenses = { total: 0, count: 0 };
            }

            return {
                revenue: parseFloat(revenue.total || 0),
                expenses: parseFloat(expenses.total || 0),
                netIncome: parseFloat(revenue.total || 0) - parseFloat(expenses.total || 0),
                revenueCount: parseInt(revenue.count || 0),
                expenseCount: parseInt(expenses.count || 0),
                totalTransactions: parseInt(revenue.count || 0) + parseInt(expenses.count || 0)
            };
        } catch (error) {
            console.error('Error getting financial data:', error);
            return {
                revenue: 0,
                expenses: 0,
                netIncome: 0,
                revenueCount: 0,
                expenseCount: 0,
                totalTransactions: 0
            };
        }
    }

    calculatePerformanceMetrics(current, previous, yearAgo) {
        const calculateChange = (current, previous) => {
            if (previous === 0) return current > 0 ? 100 : 0;
            return ((current - previous) / previous) * 100;
        };

        return {
            revenueChange: calculateChange(current.revenue, previous.revenue),
            expenseChange: calculateChange(current.expenses, previous.expenses),
            netIncomeChange: calculateChange(current.netIncome, previous.netIncome),
            transactionChange: calculateChange(current.totalTransactions, previous.totalTransactions),

            revenueYearChange: calculateChange(current.revenue, yearAgo.revenue),
            expenseYearChange: calculateChange(current.expenses, yearAgo.expenses),
            netIncomeYearChange: calculateChange(current.netIncome, yearAgo.netIncome),

            profitMargin: current.revenue > 0 ? (current.netIncome / current.revenue) * 100 : 0,
            previousProfitMargin: previous.revenue > 0 ? (previous.netIncome / previous.revenue) * 100 : 0,

            avgRevenuePerTransaction: current.revenueCount > 0 ? current.revenue / current.revenueCount : 0,
            avgExpensePerTransaction: current.expenseCount > 0 ? current.expenses / current.expenseCount : 0
        };
    }

    renderPerformanceReport(data) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const { current, previous, yearAgo, metrics } = data;

        reportContent.innerHTML = `
            <!-- Performance Overview -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>تقرير الأداء المالي</h3>
                            <p class="mb-0">مقارنة الأداء للفترة من ${DateUtils.formatDate(data.dateFrom)} إلى ${DateUtils.formatDate(data.dateTo)}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current vs Previous Period -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5>الإيرادات</h5>
                            <h3 class="text-success">${NumberUtils.formatCurrency(current.revenue)}</h3>
                            <div class="d-flex justify-content-center align-items-center">
                                <span class="badge ${metrics.revenueChange >= 0 ? 'bg-success' : 'bg-danger'} me-2">
                                    ${metrics.revenueChange >= 0 ? '+' : ''}${metrics.revenueChange.toFixed(1)}%
                                </span>
                                <small class="text-muted">مقارنة بالفترة السابقة</small>
                            </div>
                            <small class="text-muted d-block mt-1">
                                الفترة السابقة: ${NumberUtils.formatCurrency(previous.revenue)}
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5>المصروفات</h5>
                            <h3 class="text-danger">${NumberUtils.formatCurrency(current.expenses)}</h3>
                            <div class="d-flex justify-content-center align-items-center">
                                <span class="badge ${metrics.expenseChange <= 0 ? 'bg-success' : 'bg-danger'} me-2">
                                    ${metrics.expenseChange >= 0 ? '+' : ''}${metrics.expenseChange.toFixed(1)}%
                                </span>
                                <small class="text-muted">مقارنة بالفترة السابقة</small>
                            </div>
                            <small class="text-muted d-block mt-1">
                                الفترة السابقة: ${NumberUtils.formatCurrency(previous.expenses)}
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5>صافي الدخل</h5>
                            <h3 class="${current.netIncome >= 0 ? 'text-success' : 'text-danger'}">${NumberUtils.formatCurrency(current.netIncome)}</h3>
                            <div class="d-flex justify-content-center align-items-center">
                                <span class="badge ${metrics.netIncomeChange >= 0 ? 'bg-success' : 'bg-danger'} me-2">
                                    ${metrics.netIncomeChange >= 0 ? '+' : ''}${metrics.netIncomeChange.toFixed(1)}%
                                </span>
                                <small class="text-muted">مقارنة بالفترة السابقة</small>
                            </div>
                            <small class="text-muted d-block mt-1">
                                الفترة السابقة: ${NumberUtils.formatCurrency(previous.netIncome)}
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Year-over-Year Comparison -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-calendar-year"></i>
                                مقارنة سنوية (نفس الفترة العام الماضي)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>الإيرادات</h6>
                                        <div class="d-flex justify-content-center align-items-center">
                                            <span class="badge ${metrics.revenueYearChange >= 0 ? 'bg-success' : 'bg-danger'} me-2">
                                                ${metrics.revenueYearChange >= 0 ? '+' : ''}${metrics.revenueYearChange.toFixed(1)}%
                                            </span>
                                        </div>
                                        <small class="text-muted">العام الماضي: ${NumberUtils.formatCurrency(yearAgo.revenue)}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>المصروفات</h6>
                                        <div class="d-flex justify-content-center align-items-center">
                                            <span class="badge ${metrics.expenseYearChange <= 0 ? 'bg-success' : 'bg-danger'} me-2">
                                                ${metrics.expenseYearChange >= 0 ? '+' : ''}${metrics.expenseYearChange.toFixed(1)}%
                                            </span>
                                        </div>
                                        <small class="text-muted">العام الماضي: ${NumberUtils.formatCurrency(yearAgo.expenses)}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>صافي الدخل</h6>
                                        <div class="d-flex justify-content-center align-items-center">
                                            <span class="badge ${metrics.netIncomeYearChange >= 0 ? 'bg-success' : 'bg-danger'} me-2">
                                                ${metrics.netIncomeYearChange >= 0 ? '+' : ''}${metrics.netIncomeYearChange.toFixed(1)}%
                                            </span>
                                        </div>
                                        <small class="text-muted">العام الماضي: ${NumberUtils.formatCurrency(yearAgo.netIncome)}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>هامش الربح</h6>
                                        <h5 class="${metrics.profitMargin >= 0 ? 'text-success' : 'text-danger'}">
                                            ${metrics.profitMargin.toFixed(1)}%
                                        </h5>
                                        <small class="text-muted">السابق: ${metrics.previousProfitMargin.toFixed(1)}%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transaction Analysis -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">تحليل المعاملات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <h5>${current.totalTransactions}</h5>
                                        <small class="text-muted">إجمالي المعاملات</small>
                                        <div class="mt-1">
                                            <span class="badge ${metrics.transactionChange >= 0 ? 'bg-success' : 'bg-danger'}">
                                                ${metrics.transactionChange >= 0 ? '+' : ''}${metrics.transactionChange.toFixed(1)}%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <h5>${NumberUtils.formatCurrency(metrics.avgRevenuePerTransaction)}</h5>
                                        <small class="text-muted">متوسط قيمة الإيراد</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">مؤشرات الأداء</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>كفاءة التحصيل</span>
                                    <span class="fw-bold">${current.revenueCount > 0 ? ((current.revenue / current.revenueCount) / 25).toFixed(1) : 0}x</span>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>نسبة النمو الشهري</span>
                                    <span class="fw-bold ${metrics.revenueChange >= 0 ? 'text-success' : 'text-danger'}">
                                        ${metrics.revenueChange.toFixed(1)}%
                                    </span>
                                </div>
                            </div>
                            <div>
                                <div class="d-flex justify-content-between">
                                    <span>الاستقرار المالي</span>
                                    <span class="fw-bold ${current.netIncome >= 0 ? 'text-success' : 'text-danger'}">
                                        ${current.netIncome >= 0 ? 'مستقر' : 'غير مستقر'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Store data for export
        this.reportData = data;
    }

    async loadMembersReport() {
        try {
            console.log('👥 Loading members report...');

            // Get members statistics
            const membersStats = await database.get(`
                SELECT
                    COUNT(*) as total_members,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_members,
                    COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_members,
                    COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_members
                FROM members
            `);

            // Get members with payment details
            const membersWithPayments = await database.all(`
                SELECT
                    m.*,
                    COALESCE(SUM(p.amount), 0) as total_paid,
                    COUNT(p.id) as payment_count,
                    MAX(p.payment_date) as last_payment_date,
                    MIN(p.payment_date) as first_payment_date
                FROM members m
                LEFT JOIN payments p ON m.id = p.member_id
                GROUP BY m.id
                ORDER BY total_paid DESC
            `);

            // Get monthly subscription from settings
            const subscriptionSetting = await database.get(
                'SELECT value FROM settings WHERE key = "monthly_subscription"'
            );
            const monthlySubscription = parseFloat(subscriptionSetting?.value || 25);

            // Calculate member categories
            const memberCategories = this.categorizeMembersByPayment(membersWithPayments, monthlySubscription);

            // Get recent joiners (last 30 days)
            const recentJoiners = await database.all(`
                SELECT * FROM members
                WHERE join_date >= date('now', '-30 days')
                ORDER BY join_date DESC
            `);

            this.renderMembersReport({
                membersStats,
                membersWithPayments,
                memberCategories,
                recentJoiners,
                monthlySubscription
            });

        } catch (error) {
            console.error('Error loading members report:', error);
            UIUtils.showNotification('خطأ في تحميل تقرير الأعضاء: ' + error.message, 'danger');

            const reportContent = document.getElementById('report-content');
            if (reportContent) {
                reportContent.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                        <h4 class="mt-3">خطأ في تحميل تقرير الأعضاء</h4>
                        <p>حدث خطأ أثناء تحميل بيانات الأعضاء. يرجى المحاولة مرة أخرى.</p>
                        <button class="btn btn-primary" onclick="window.ReportsManager.loadReport()">
                            <i class="bi bi-arrow-clockwise"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }
    }

    categorizeMembersByPayment(members, monthlySubscription) {
        const categories = {
            excellent: [], // دفع أكثر من المطلوب
            good: [],      // دفع المطلوب تماماً
            partial: [],   // دفع جزئي
            none: []       // لم يدفع
        };

        members.forEach(member => {
            const monthsSinceJoining = moment().diff(moment(member.join_date), 'months') + 1;
            const expectedTotal = monthsSinceJoining * monthlySubscription;
            const paidPercentage = expectedTotal > 0 ? (member.total_paid / expectedTotal) * 100 : 0;

            if (paidPercentage >= 100) {
                categories.excellent.push({
                    ...member,
                    expected_total: expectedTotal,
                    paid_percentage: paidPercentage
                });
            } else if (paidPercentage >= 80) {
                categories.good.push({
                    ...member,
                    expected_total: expectedTotal,
                    paid_percentage: paidPercentage
                });
            } else if (paidPercentage > 0) {
                categories.partial.push({
                    ...member,
                    expected_total: expectedTotal,
                    paid_percentage: paidPercentage
                });
            } else {
                categories.none.push({
                    ...member,
                    expected_total: expectedTotal,
                    paid_percentage: paidPercentage
                });
            }
        });

        return categories;
    }

    renderMembersReport(data) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const { membersStats, memberCategories, recentJoiners, monthlySubscription } = data;

        reportContent.innerHTML = `
            <!-- Members Report Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>
                                <i class="bi bi-people"></i>
                                تقرير الأعضاء
                            </h3>
                            <p class="mb-0">إحصائيات شاملة عن أعضاء الديوان</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Members Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>${membersStats.total_members}</h4>
                            <p class="mb-0">إجمالي الأعضاء</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${membersStats.active_members}</h4>
                            <p class="mb-0">الأعضاء النشطون</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4>${membersStats.inactive_members}</h4>
                            <p class="mb-0">الأعضاء غير النشطين</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4>${membersStats.suspended_members}</h4>
                            <p class="mb-0">الأعضاء المعلقون</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Categories -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="bi bi-star-fill"></i>
                                ممتازون (${memberCategories.excellent.length})
                            </h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">دفعوا 100% أو أكثر</small>
                            ${memberCategories.excellent.slice(0, 3).map(member => `
                                <div class="d-flex justify-content-between align-items-center border-bottom py-1">
                                    <small>${member.full_name}</small>
                                    <span class="badge bg-success">${member.paid_percentage.toFixed(0)}%</span>
                                </div>
                            `).join('')}
                            ${memberCategories.excellent.length > 3 ? `<small class="text-muted">و ${memberCategories.excellent.length - 3} آخرين...</small>` : ''}
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="bi bi-check-circle"></i>
                                جيدون (${memberCategories.good.length})
                            </h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">دفعوا 80-99%</small>
                            ${memberCategories.good.slice(0, 3).map(member => `
                                <div class="d-flex justify-content-between align-items-center border-bottom py-1">
                                    <small>${member.full_name}</small>
                                    <span class="badge bg-primary">${member.paid_percentage.toFixed(0)}%</span>
                                </div>
                            `).join('')}
                            ${memberCategories.good.length > 3 ? `<small class="text-muted">و ${memberCategories.good.length - 3} آخرين...</small>` : ''}
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="bi bi-exclamation-triangle"></i>
                                جزئي (${memberCategories.partial.length})
                            </h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">دفعوا 1-79%</small>
                            ${memberCategories.partial.slice(0, 3).map(member => `
                                <div class="d-flex justify-content-between align-items-center border-bottom py-1">
                                    <small>${member.full_name}</small>
                                    <span class="badge bg-warning text-dark">${member.paid_percentage.toFixed(0)}%</span>
                                </div>
                            `).join('')}
                            ${memberCategories.partial.length > 3 ? `<small class="text-muted">و ${memberCategories.partial.length - 3} آخرين...</small>` : ''}
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">
                                <i class="bi bi-x-circle"></i>
                                لم يدفعوا (${memberCategories.none.length})
                            </h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">لم يدفعوا شيئاً</small>
                            ${memberCategories.none.slice(0, 3).map(member => `
                                <div class="d-flex justify-content-between align-items-center border-bottom py-1">
                                    <small>${member.full_name}</small>
                                    <span class="badge bg-danger">0%</span>
                                </div>
                            `).join('')}
                            ${memberCategories.none.length > 3 ? `<small class="text-muted">و ${memberCategories.none.length - 3} آخرين...</small>` : ''}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Joiners -->
            ${recentJoiners.length > 0 ? `
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-person-plus"></i>
                                الأعضاء الجدد (آخر 30 يوم)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>رقم العضوية</th>
                                            <th>تاريخ الانضمام</th>
                                            <th>الحالة</th>
                                            <th>الهاتف</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${recentJoiners.map(member => `
                                            <tr>
                                                <td>${member.full_name}</td>
                                                <td>${member.membership_id}</td>
                                                <td>${DateUtils.formatDate(member.join_date)}</td>
                                                <td>
                                                    <span class="badge ${member.status === 'active' ? 'bg-success' : 'bg-warning'}">
                                                        ${member.status === 'active' ? 'نشط' : 'غير نشط'}
                                                    </span>
                                                </td>
                                                <td>${member.phone || '-'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}
        `;

        // Store data for export
        this.reportData = data;

        console.log('✅ Members report rendered successfully');
    }

    async loadOverdueReport() {
        // Get subscription amount from settings
        const subscriptionSetting = await database.get(
            'SELECT value FROM settings WHERE key = "monthly_subscription"'
        );
        const monthlySubscription = parseFloat(subscriptionSetting?.value || 100);

        // Get all active members with their payment history
        const members = await database.all(`
            SELECT m.*,
                   COALESCE(SUM(p.amount), 0) as total_paid,
                   COUNT(p.id) as payment_count,
                   MAX(p.payment_date) as last_payment_date
            FROM members m
            LEFT JOIN payments p ON m.id = p.member_id
            WHERE m.status = 'active'
            GROUP BY m.id
            ORDER BY m.full_name
        `);

        // Calculate overdue amounts
        const overdueMembers = [];
        for (const member of members) {
            const monthsSinceJoining = moment().diff(moment(member.join_date), 'months') + 1;
            const expectedTotal = monthsSinceJoining * monthlySubscription;
            const overdueAmount = Math.max(0, expectedTotal - member.total_paid);

            if (overdueAmount > 0) {
                overdueMembers.push({
                    ...member,
                    months_since_joining: monthsSinceJoining,
                    expected_total: expectedTotal,
                    overdue_amount: overdueAmount,
                    overdue_months: Math.ceil(overdueAmount / monthlySubscription)
                });
            }
        }

        this.renderOverdueReport(overdueMembers, monthlySubscription);
    }

    renderOverdueReport(overdueMembers, monthlySubscription) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const totalOverdue = overdueMembers.reduce((sum, member) => sum + member.overdue_amount, 0);

        reportContent.innerHTML = `
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>${overdueMembers.length}</h3>
                            <p class="mb-0">أعضاء متأخرون</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(totalOverdue)}</h3>
                            <p class="mb-0">إجمالي المتأخرات</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(monthlySubscription)}</h3>
                            <p class="mb-0">الاشتراك الشهري</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">تفاصيل الأعضاء المتأخرين</h5>
                </div>
                <div class="card-body">
                    ${overdueMembers.length > 0 ? `
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>رقم العضوية</th>
                                        <th>تاريخ الانضمام</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>المبلغ المطلوب</th>
                                        <th>المتأخرات</th>
                                        <th>الأشهر المتأخرة</th>
                                        <th>آخر دفعة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${overdueMembers.map(member => `
                                        <tr>
                                            <td><strong>${member.full_name}</strong></td>
                                            <td>${member.membership_id}</td>
                                            <td>${DateUtils.formatDate(member.join_date)}</td>
                                            <td>${NumberUtils.formatCurrency(member.total_paid)}</td>
                                            <td>${NumberUtils.formatCurrency(member.expected_total)}</td>
                                            <td><strong class="text-danger">${NumberUtils.formatCurrency(member.overdue_amount)}</strong></td>
                                            <td><span class="badge bg-warning">${member.overdue_months}</span></td>
                                            <td>${member.last_payment_date ? DateUtils.formatDate(member.last_payment_date) : 'لا يوجد'}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.MembersManager?.viewMember(${member.id})" title="عرض العضو">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.PaymentsManager?.showAddPaymentModal(${member.id})" title="تسجيل دفعة">
                                                        <i class="bi bi-plus"></i>
                                                    </button>
                                                    <button class="btn btn-outline-info" onclick="window.ReportsManager.sendOverdueReminder(${member.id})" title="إرسال تذكير">
                                                        <i class="bi bi-bell"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    ` : `
                        <div class="text-center text-success py-5">
                            <i class="bi bi-check-circle fs-1"></i>
                            <h4 class="mt-3">ممتاز! لا يوجد أعضاء متأخرون</h4>
                            <p>جميع الأعضاء النشطين محدثون في مدفوعاتهم</p>
                        </div>
                    `}
                </div>
            </div>
        `;

        // Store data for export
        this.reportData = { overdueMembers, monthlySubscription, totalOverdue };
    }

    async sendOverdueReminder(memberId) {
        // This will be implemented with WhatsApp integration
        UIUtils.showNotification('سيتم تطوير إرسال التذكيرات مع تكامل الواتساب', 'info');
    }

    exportCurrentReport() {
        if (!this.reportData) {
            UIUtils.showNotification('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        const reportType = document.getElementById('report-type')?.value || 'financial';

        switch (reportType) {
            case 'financial':
                this.exportFinancialReport();
                break;
            case 'overdue':
                this.exportOverdueReport();
                break;
            default:
                UIUtils.showNotification('نوع التقرير غير مدعوم للتصدير حالياً', 'warning');
        }
    }

    exportFinancialReport() {
        const data = this.reportData;
        const exportData = [
            {
                'نوع البيان': 'إجمالي الإيرادات',
                'المبلغ': data.totalRevenue,
                'الفترة': `${data.dateFrom} إلى ${data.dateTo}`
            },
            {
                'نوع البيان': 'إجمالي المصروفات',
                'المبلغ': data.totalExpenses,
                'الفترة': `${data.dateFrom} إلى ${data.dateTo}`
            },
            {
                'نوع البيان': 'صافي الدخل',
                'المبلغ': data.netIncome,
                'الفترة': `${data.dateFrom} إلى ${data.dateTo}`
            }
        ];

        UIUtils.exportToCSV(exportData, `financial-report-${moment().format('YYYY-MM-DD')}.csv`);
    }

    exportOverdueReport() {
        const data = this.reportData;
        const exportData = data.overdueMembers.map(member => ({
            'الاسم': member.full_name,
            'رقم العضوية': member.membership_id,
            'تاريخ الانضمام': DateUtils.formatDate(member.join_date),
            'المبلغ المدفوع': member.total_paid,
            'المبلغ المطلوب': member.expected_total,
            'المتأخرات': member.overdue_amount,
            'الأشهر المتأخرة': member.overdue_months,
            'آخر دفعة': member.last_payment_date ? DateUtils.formatDate(member.last_payment_date) : 'لا يوجد'
        }));

        UIUtils.exportToCSV(exportData, `overdue-report-${moment().format('YYYY-MM-DD')}.csv`);
    }

    printCurrentReport() {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const reportType = document.getElementById('report-type')?.value || 'financial';
        const reportTitle = document.getElementById('report-type')?.selectedOptions[0]?.text || 'تقرير مالي';

        UIUtils.printContent('report-content', reportTitle);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.ReportsManager = new ReportsManager();
        console.log('ReportsManager initialized successfully');

        // Try to load reports if database is ready
        setTimeout(() => {
            if (window.database && window.database.isReady) {
                console.log('Database is ready, loading reports...');
                window.ReportsManager.loadReports().catch(error => {
                    console.log('Initial reports load failed, will retry when database is ready');
                });
            }
        }, 1000);

    } catch (error) {
        console.error('Error initializing ReportsManager:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة نظام التقارير', 'danger');
        }
    }
});

// Add database ready listener
window.addEventListener('database-ready', () => {
    console.log('Database ready, ReportsManager can now load data');
    if (window.ReportsManager) {
        setTimeout(() => {
            window.ReportsManager.loadReports().catch(error => {
                console.error('Error loading reports after database ready:', error);
            });
        }, 500);
    }
});
