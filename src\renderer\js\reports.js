// Reports management module

class ReportsManager {
    constructor() {
        this.charts = {};
        this.reportData = {};
        this.currentReport = 'financial';
        
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
    }

    setupEventListeners() {
        // Report type selection
        const reportTypeSelect = document.getElementById('report-type');
        if (reportTypeSelect) {
            reportTypeSelect.addEventListener('change', (e) => {
                this.currentReport = e.target.value;
                this.loadReport();
            });
        }

        // Date range filters
        const dateFromInput = document.getElementById('report-date-from');
        const dateToInput = document.getElementById('report-date-to');
        
        if (dateFromInput && dateToInput) {
            dateFromInput.addEventListener('change', () => this.loadReport());
            dateToInput.addEventListener('change', () => this.loadReport());
        }
    }

    async loadReports() {
        try {
            UIUtils.showLoading();

            // Wait for database module to be loaded
            await this.waitForDatabaseModule();

            // Check if database is available
            if (!window.database || !window.database.db) {
                throw new Error('قاعدة البيانات غير متاحة');
            }

            // Check if Chart.js is available
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded, charts will not be available');
            }

            // Load reports page content if not already loaded
            await this.loadReportsPageContent();

            // Load default report
            await this.loadReport();

        } catch (error) {
            console.error('Error loading reports:', error);
            UIUtils.showNotification(`خطأ في تحميل التقارير: ${error.message}`, 'danger');

            // Show empty state
            this.reportData = {};
            this.charts = {};
        } finally {
            UIUtils.hideLoading();
        }
    }

    async waitForDatabaseModule() {
        console.log('🔍 Waiting for database module (Reports)...');

        // If already loaded, return immediately
        if (window.database && window.databaseModuleLoaded) {
            console.log('✅ Database module already loaded (Reports)');
            return;
        }

        // Wait for database module to load
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Database module loading timeout (Reports)'));
            }, 10000); // 10 second timeout

            const checkDatabase = () => {
                if (window.database && window.databaseModuleLoaded) {
                    clearTimeout(timeout);
                    console.log('✅ Database module loaded successfully (Reports)');
                    resolve();
                } else {
                    setTimeout(checkDatabase, 100);
                }
            };

            // Also listen for the database module loaded event
            window.addEventListener('database-module-loaded', () => {
                clearTimeout(timeout);
                console.log('✅ Database module loaded via event (Reports)');
                resolve();
            }, { once: true });

            checkDatabase();
        });
    }

    async loadReportsPageContent() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        const pageElement = document.getElementById('reports-page');
        if (!pageElement) {
            console.error('Reports page element not found, available elements:',
                Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            throw new Error('عنصر صفحة التقارير غير موجود في HTML');
        }

        if (pageElement.innerHTML.trim()) {
            console.log('Reports page content already loaded');
            return;
        }

        pageElement.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="bi bi-graph-up"></i>
                            التقارير المالية
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-outline-success" onclick="window.ReportsManager.exportCurrentReport()">
                                <i class="bi bi-download"></i>
                                تصدير
                            </button>
                            <button class="btn btn-outline-info" onclick="window.ReportsManager.printCurrentReport()">
                                <i class="bi bi-printer"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Controls -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="report-type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report-type">
                                <option value="financial">التقرير المالي الشامل</option>
                                <option value="monthly">التقرير الشهري</option>
                                <option value="annual">التقرير السنوي</option>
                                <option value="members">تقرير الأعضاء</option>
                                <option value="overdue">تقرير المتأخرات</option>
                                <option value="categories">تقرير حسب الفئات</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="report-date-from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="report-date-from" 
                                   value="${moment().subtract(1, 'month').format('YYYY-MM-DD')}">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="report-date-to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="report-date-to" 
                                   value="${moment().format('YYYY-MM-DD')}">
                        </div>
                        
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-primary w-100" onclick="window.ReportsManager.loadReport()">
                                <i class="bi bi-arrow-clockwise"></i>
                                تحديث التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div id="report-content">
                <!-- Report content will be loaded here -->
            </div>
        `;

        // Set up event listeners after content is loaded
        setTimeout(() => this.setupEventListeners(), 100);
    }

    async loadReport() {
        const reportType = document.getElementById('report-type')?.value || 'financial';
        const dateFrom = document.getElementById('report-date-from')?.value;
        const dateTo = document.getElementById('report-date-to')?.value;

        try {
            UIUtils.showLoading();

            switch (reportType) {
                case 'financial':
                    await this.loadFinancialReport(dateFrom, dateTo);
                    break;
                case 'monthly':
                    await this.loadMonthlyReport(dateFrom, dateTo);
                    break;
                case 'annual':
                    await this.loadAnnualReport();
                    break;
                case 'members':
                    await this.loadMembersReport();
                    break;
                case 'overdue':
                    await this.loadOverdueReport();
                    break;
                case 'categories':
                    await this.loadCategoriesReport(dateFrom, dateTo);
                    break;
            }

        } catch (error) {
            console.error('Error loading report:', error);
            UIUtils.showNotification('خطأ في تحميل التقرير', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    async loadFinancialReport(dateFrom, dateTo) {
        // Get financial data
        const revenueData = await database.all(`
            SELECT 
                DATE(payment_date) as date,
                SUM(amount) as total,
                COUNT(*) as count
            FROM payments 
            WHERE payment_date BETWEEN ? AND ?
            GROUP BY DATE(payment_date)
            ORDER BY payment_date
        `, [dateFrom, dateTo]);

        const expenseData = await database.all(`
            SELECT 
                DATE(expense_date) as date,
                SUM(amount) as total,
                COUNT(*) as count
            FROM expenses 
            WHERE expense_date BETWEEN ? AND ?
            GROUP BY DATE(expense_date)
            ORDER BY expense_date
        `, [dateFrom, dateTo]);

        // Calculate totals
        const totalRevenue = revenueData.reduce((sum, item) => sum + parseFloat(item.total), 0);
        const totalExpenses = expenseData.reduce((sum, item) => sum + parseFloat(item.total), 0);
        const netIncome = totalRevenue - totalExpenses;

        // Get category breakdown
        const revenueByCategory = await database.all(`
            SELECT 
                rc.name as category,
                SUM(p.amount) as total,
                COUNT(*) as count
            FROM payments p
            JOIN revenue_categories rc ON p.category_id = rc.id
            WHERE p.payment_date BETWEEN ? AND ?
            GROUP BY rc.id, rc.name
            ORDER BY total DESC
        `, [dateFrom, dateTo]);

        const expensesByCategory = await database.all(`
            SELECT 
                ec.name as category,
                SUM(e.amount) as total,
                COUNT(*) as count
            FROM expenses e
            JOIN expense_categories ec ON e.category_id = ec.id
            WHERE e.expense_date BETWEEN ? AND ?
            GROUP BY ec.id, ec.name
            ORDER BY total DESC
        `, [dateFrom, dateTo]);

        this.renderFinancialReport({
            dateFrom,
            dateTo,
            totalRevenue,
            totalExpenses,
            netIncome,
            revenueData,
            expenseData,
            revenueByCategory,
            expensesByCategory
        });
    }

    renderFinancialReport(data) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        reportContent.innerHTML = `
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(data.totalRevenue)}</h3>
                            <p class="mb-0">إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(data.totalExpenses)}</h3>
                            <p class="mb-0">إجمالي المصروفات</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card ${data.netIncome >= 0 ? 'bg-primary' : 'bg-warning'} text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(data.netIncome)}</h3>
                            <p class="mb-0">صافي الدخل</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatNumber(data.revenueData.length + data.expenseData.length)}</h3>
                            <p class="mb-0">إجمالي المعاملات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الإيرادات والمصروفات اليومية</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="daily-financial-chart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">نسبة الإيرادات للمصروفات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenue-expense-pie-chart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Category Breakdown -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الإيرادات حسب الفئة</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الفئة</th>
                                            <th>المبلغ</th>
                                            <th>العدد</th>
                                            <th>النسبة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.revenueByCategory.map(item => `
                                            <tr>
                                                <td>${item.category}</td>
                                                <td>${NumberUtils.formatCurrency(item.total)}</td>
                                                <td>${item.count}</td>
                                                <td>${((item.total / data.totalRevenue) * 100).toFixed(1)}%</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">المصروفات حسب الفئة</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الفئة</th>
                                            <th>المبلغ</th>
                                            <th>العدد</th>
                                            <th>النسبة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.expensesByCategory.map(item => `
                                            <tr>
                                                <td>${item.category}</td>
                                                <td>${NumberUtils.formatCurrency(item.total)}</td>
                                                <td>${item.count}</td>
                                                <td>${((item.total / data.totalExpenses) * 100).toFixed(1)}%</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Render charts
        setTimeout(() => {
            this.renderDailyFinancialChart(data.revenueData, data.expenseData);
            this.renderRevenueExpensePieChart(data.totalRevenue, data.totalExpenses);
        }, 100);

        // Store data for export
        this.reportData = data;
    }

    renderDailyFinancialChart(revenueData, expenseData) {
        const ctx = document.getElementById('daily-financial-chart');
        if (!ctx) {
            console.warn('Daily financial chart canvas not found');
            return;
        }

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, skipping chart rendering');
            ctx.parentElement.innerHTML = '<div class="alert alert-warning">مكتبة الرسوم البيانية غير متاحة</div>';
            return;
        }

        // Destroy existing chart
        if (this.charts.dailyFinancial) {
            this.charts.dailyFinancial.destroy();
        }

        // Prepare data
        const allDates = [...new Set([
            ...revenueData.map(item => item.date),
            ...expenseData.map(item => item.date)
        ])].sort();

        const revenueAmounts = allDates.map(date => {
            const item = revenueData.find(r => r.date === date);
            return item ? parseFloat(item.total) : 0;
        });

        const expenseAmounts = allDates.map(date => {
            const item = expenseData.find(e => e.date === date);
            return item ? parseFloat(item.total) : 0;
        });

        this.charts.dailyFinancial = new Chart(ctx, {
            type: 'line',
            data: {
                labels: allDates.map(date => DateUtils.formatDate(date)),
                datasets: [
                    {
                        label: 'الإيرادات',
                        data: revenueAmounts,
                        borderColor: '#198754',
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'المصروفات',
                        data: expenseAmounts,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            font: { family: 'Noto Sans Arabic' }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' +
                                       NumberUtils.formatCurrency(context.parsed.y);
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return NumberUtils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }

    renderRevenueExpensePieChart(totalRevenue, totalExpenses) {
        const ctx = document.getElementById('revenue-expense-pie-chart');
        if (!ctx) {
            console.warn('Revenue expense pie chart canvas not found');
            return;
        }

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, skipping pie chart rendering');
            ctx.parentElement.innerHTML = '<div class="alert alert-warning">مكتبة الرسوم البيانية غير متاحة</div>';
            return;
        }

        // Destroy existing chart
        if (this.charts.revenueExpensePie) {
            this.charts.revenueExpensePie.destroy();
        }

        this.charts.revenueExpensePie = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['الإيرادات', 'المصروفات'],
                datasets: [{
                    data: [totalRevenue, totalExpenses],
                    backgroundColor: ['#198754', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: { family: 'Noto Sans Arabic' }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' +
                                       NumberUtils.formatCurrency(context.parsed) +
                                       ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    async loadOverdueReport() {
        // Get subscription amount from settings
        const subscriptionSetting = await database.get(
            'SELECT value FROM settings WHERE key = "monthly_subscription"'
        );
        const monthlySubscription = parseFloat(subscriptionSetting?.value || 100);

        // Get all active members with their payment history
        const members = await database.all(`
            SELECT m.*,
                   COALESCE(SUM(p.amount), 0) as total_paid,
                   COUNT(p.id) as payment_count,
                   MAX(p.payment_date) as last_payment_date
            FROM members m
            LEFT JOIN payments p ON m.id = p.member_id
            WHERE m.status = 'active'
            GROUP BY m.id
            ORDER BY m.full_name
        `);

        // Calculate overdue amounts
        const overdueMembers = [];
        for (const member of members) {
            const monthsSinceJoining = moment().diff(moment(member.join_date), 'months') + 1;
            const expectedTotal = monthsSinceJoining * monthlySubscription;
            const overdueAmount = Math.max(0, expectedTotal - member.total_paid);

            if (overdueAmount > 0) {
                overdueMembers.push({
                    ...member,
                    months_since_joining: monthsSinceJoining,
                    expected_total: expectedTotal,
                    overdue_amount: overdueAmount,
                    overdue_months: Math.ceil(overdueAmount / monthlySubscription)
                });
            }
        }

        this.renderOverdueReport(overdueMembers, monthlySubscription);
    }

    renderOverdueReport(overdueMembers, monthlySubscription) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const totalOverdue = overdueMembers.reduce((sum, member) => sum + member.overdue_amount, 0);

        reportContent.innerHTML = `
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>${overdueMembers.length}</h3>
                            <p class="mb-0">أعضاء متأخرون</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(totalOverdue)}</h3>
                            <p class="mb-0">إجمالي المتأخرات</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>${NumberUtils.formatCurrency(monthlySubscription)}</h3>
                            <p class="mb-0">الاشتراك الشهري</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">تفاصيل الأعضاء المتأخرين</h5>
                </div>
                <div class="card-body">
                    ${overdueMembers.length > 0 ? `
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>رقم العضوية</th>
                                        <th>تاريخ الانضمام</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>المبلغ المطلوب</th>
                                        <th>المتأخرات</th>
                                        <th>الأشهر المتأخرة</th>
                                        <th>آخر دفعة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${overdueMembers.map(member => `
                                        <tr>
                                            <td><strong>${member.full_name}</strong></td>
                                            <td>${member.membership_id}</td>
                                            <td>${DateUtils.formatDate(member.join_date)}</td>
                                            <td>${NumberUtils.formatCurrency(member.total_paid)}</td>
                                            <td>${NumberUtils.formatCurrency(member.expected_total)}</td>
                                            <td><strong class="text-danger">${NumberUtils.formatCurrency(member.overdue_amount)}</strong></td>
                                            <td><span class="badge bg-warning">${member.overdue_months}</span></td>
                                            <td>${member.last_payment_date ? DateUtils.formatDate(member.last_payment_date) : 'لا يوجد'}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.MembersManager?.viewMember(${member.id})" title="عرض العضو">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.PaymentsManager?.showAddPaymentModal(${member.id})" title="تسجيل دفعة">
                                                        <i class="bi bi-plus"></i>
                                                    </button>
                                                    <button class="btn btn-outline-info" onclick="window.ReportsManager.sendOverdueReminder(${member.id})" title="إرسال تذكير">
                                                        <i class="bi bi-bell"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    ` : `
                        <div class="text-center text-success py-5">
                            <i class="bi bi-check-circle fs-1"></i>
                            <h4 class="mt-3">ممتاز! لا يوجد أعضاء متأخرون</h4>
                            <p>جميع الأعضاء النشطين محدثون في مدفوعاتهم</p>
                        </div>
                    `}
                </div>
            </div>
        `;

        // Store data for export
        this.reportData = { overdueMembers, monthlySubscription, totalOverdue };
    }

    async sendOverdueReminder(memberId) {
        // This will be implemented with WhatsApp integration
        UIUtils.showNotification('سيتم تطوير إرسال التذكيرات مع تكامل الواتساب', 'info');
    }

    exportCurrentReport() {
        if (!this.reportData) {
            UIUtils.showNotification('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        const reportType = document.getElementById('report-type')?.value || 'financial';

        switch (reportType) {
            case 'financial':
                this.exportFinancialReport();
                break;
            case 'overdue':
                this.exportOverdueReport();
                break;
            default:
                UIUtils.showNotification('نوع التقرير غير مدعوم للتصدير حالياً', 'warning');
        }
    }

    exportFinancialReport() {
        const data = this.reportData;
        const exportData = [
            {
                'نوع البيان': 'إجمالي الإيرادات',
                'المبلغ': data.totalRevenue,
                'الفترة': `${data.dateFrom} إلى ${data.dateTo}`
            },
            {
                'نوع البيان': 'إجمالي المصروفات',
                'المبلغ': data.totalExpenses,
                'الفترة': `${data.dateFrom} إلى ${data.dateTo}`
            },
            {
                'نوع البيان': 'صافي الدخل',
                'المبلغ': data.netIncome,
                'الفترة': `${data.dateFrom} إلى ${data.dateTo}`
            }
        ];

        UIUtils.exportToCSV(exportData, `financial-report-${moment().format('YYYY-MM-DD')}.csv`);
    }

    exportOverdueReport() {
        const data = this.reportData;
        const exportData = data.overdueMembers.map(member => ({
            'الاسم': member.full_name,
            'رقم العضوية': member.membership_id,
            'تاريخ الانضمام': DateUtils.formatDate(member.join_date),
            'المبلغ المدفوع': member.total_paid,
            'المبلغ المطلوب': member.expected_total,
            'المتأخرات': member.overdue_amount,
            'الأشهر المتأخرة': member.overdue_months,
            'آخر دفعة': member.last_payment_date ? DateUtils.formatDate(member.last_payment_date) : 'لا يوجد'
        }));

        UIUtils.exportToCSV(exportData, `overdue-report-${moment().format('YYYY-MM-DD')}.csv`);
    }

    printCurrentReport() {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const reportType = document.getElementById('report-type')?.value || 'financial';
        const reportTitle = document.getElementById('report-type')?.selectedOptions[0]?.text || 'تقرير مالي';

        UIUtils.printContent('report-content', reportTitle);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.ReportsManager = new ReportsManager();
        console.log('ReportsManager initialized successfully');

        // Try to load reports if database is ready
        setTimeout(() => {
            if (window.database && window.database.isReady) {
                console.log('Database is ready, loading reports...');
                window.ReportsManager.loadReports().catch(error => {
                    console.log('Initial reports load failed, will retry when database is ready');
                });
            }
        }, 1000);

    } catch (error) {
        console.error('Error initializing ReportsManager:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة نظام التقارير', 'danger');
        }
    }
});

// Add database ready listener
window.addEventListener('database-ready', () => {
    console.log('Database ready, ReportsManager can now load data');
    if (window.ReportsManager) {
        setTimeout(() => {
            window.ReportsManager.loadReports().catch(error => {
                console.error('Error loading reports after database ready:', error);
            });
        }, 500);
    }
});
