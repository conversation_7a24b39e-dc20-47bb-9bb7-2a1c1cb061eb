// Expense management module

class ExpensesManager {
    constructor() {
        this.expenses = [];
        this.filteredExpenses = [];
        this.expenseCategories = [];
        this.currentPage = 1;
        this.itemsPerPage = 15;
        this.searchTerm = '';
        this.categoryFilter = 'all';
        this.dateFromFilter = '';
        this.dateToFilter = '';
        this.sortBy = 'expense_date';
        this.sortOrder = 'desc';
        
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('expense-search');
        if (searchInput) {
            searchInput.addEventListener('input', UIUtils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.filterExpenses();
            }, 300));
        }

        // Filters
        const categoryFilter = document.getElementById('expense-category-filter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.categoryFilter = e.target.value;
                this.filterExpenses();
            });
        }

        const dateFromFilter = document.getElementById('expense-date-from-filter');
        if (dateFromFilter) {
            dateFromFilter.addEventListener('change', (e) => {
                this.dateFromFilter = e.target.value;
                this.filterExpenses();
            });
        }

        const dateToFilter = document.getElementById('expense-date-to-filter');
        if (dateToFilter) {
            dateToFilter.addEventListener('change', (e) => {
                this.dateToFilter = e.target.value;
                this.filterExpenses();
            });
        }

        // Sort functionality
        const sortSelect = document.getElementById('sort-expenses');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                this.sortBy = sortBy;
                this.sortOrder = sortOrder;
                this.sortExpenses();
            });
        }

        // Items per page
        const itemsPerPageSelect = document.getElementById('expenses-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', (e) => {
                this.itemsPerPage = parseInt(e.target.value);
                this.currentPage = 1;
                this.renderExpenses();
            });
        }
    }

    async loadExpenses() {
        try {
            UIUtils.showLoading();

            // Wait for database module to be loaded
            await this.waitForDatabaseModule();

            // Check if database is available
            if (!window.database || !window.database.db) {
                throw new Error('قاعدة البيانات غير متاحة');
            }

            // Load expenses page content if not already loaded
            await this.loadExpensesPageContent();

            // Fetch data from database with error handling
            try {
                await Promise.all([
                    this.fetchExpenses(),
                    this.fetchExpenseCategories()
                ]);
            } catch (dbError) {
                console.error('Database fetch error:', dbError);
                // Try individual fetches to identify the problem
                try {
                    await this.fetchExpenses();
                    console.log('✅ Expenses fetched successfully');
                } catch (e) {
                    console.error('❌ Error fetching expenses:', e);
                    this.expenses = [];
                }

                try {
                    await this.fetchExpenseCategories();
                    console.log('✅ Expense categories fetched successfully');
                } catch (e) {
                    console.error('❌ Error fetching expense categories:', e);
                    this.expenseCategories = [];
                }
            }

            this.filteredExpenses = [...this.expenses];
            this.renderExpenses();
            this.updateExpenseStats();
            this.populateFilters();

        } catch (error) {
            console.error('Error loading expenses:', error);
            UIUtils.showNotification(`خطأ في تحميل بيانات المصروفات: ${error.message}`, 'danger');

            // Show empty state
            this.expenses = [];
            this.filteredExpenses = [];
            this.expenseCategories = [];
            this.renderExpenses();
            this.updateExpenseStats();
        } finally {
            UIUtils.hideLoading();
        }
    }

    async waitForDatabaseModule() {
        console.log('🔍 Waiting for database module (Expenses)...');

        // If already loaded, return immediately
        if (window.database && window.databaseModuleLoaded) {
            console.log('✅ Database module already loaded (Expenses)');
            return;
        }

        // Wait for database module to load
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Database module loading timeout (Expenses)'));
            }, 10000); // 10 second timeout

            const checkDatabase = () => {
                if (window.database && window.databaseModuleLoaded) {
                    clearTimeout(timeout);
                    console.log('✅ Database module loaded successfully (Expenses)');
                    resolve();
                } else {
                    setTimeout(checkDatabase, 100);
                }
            };

            // Also listen for the database module loaded event
            window.addEventListener('database-module-loaded', () => {
                clearTimeout(timeout);
                console.log('✅ Database module loaded via event (Expenses)');
                resolve();
            }, { once: true });

            checkDatabase();
        });
    }

    async fetchExpenses() {
        this.expenses = await database.all(`
            SELECT e.*, 
                   ec.name as category_name
            FROM expenses e
            JOIN expense_categories ec ON e.category_id = ec.id
            ORDER BY e.expense_date DESC, e.created_at DESC
        `);
    }

    async fetchExpenseCategories() {
        this.expenseCategories = await database.all(`
            SELECT id, name, description
            FROM expense_categories
            WHERE is_active = 1
            ORDER BY name
        `);
    }

    async loadExpensesPageContent() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        const pageElement = document.getElementById('expenses-page');
        if (!pageElement) {
            console.error('Expenses page element not found, available elements:',
                Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            throw new Error('عنصر صفحة المصروفات غير موجود في HTML');
        }

        if (pageElement.innerHTML.trim()) {
            console.log('Expenses page content already loaded');
            return;
        }

        pageElement.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="bi bi-receipt"></i>
                            إدارة المصروفات
                        </h2>
                        <button class="btn btn-primary" onclick="window.ExpensesManager.showAddExpenseModal()">
                            <i class="bi bi-plus-circle"></i>
                            تسجيل مصروف جديد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-expenses">0 ر.س</h4>
                                    <p class="mb-0">إجمالي المصروفات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-cash-stack fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="monthly-expenses">0 ر.س</h4>
                                    <p class="mb-0">مصروفات الشهر</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-calendar-month fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-expenses-count">0</h4>
                                    <p class="mb-0">عدد المصروفات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-receipt fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="avg-expense">0 ر.س</h4>
                                    <p class="mb-0">متوسط المصروف</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-graph-down fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="expense-search" class="form-label">البحث</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="expense-search" 
                                       placeholder="البحث في الوصف أو الملاحظات...">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="expense-category-filter" class="form-label">فئة المصروف</label>
                            <select class="form-select" id="expense-category-filter">
                                <option value="all">جميع الفئات</option>
                                <!-- Categories will be populated here -->
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="expense-date-from-filter" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="expense-date-from-filter">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="expense-date-to-filter" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="expense-date-to-filter">
                        </div>
                        
                        <div class="col-md-2 d-flex align-items-end">
                            <button class="btn btn-outline-secondary me-2" onclick="window.ExpensesManager.clearFilters()" title="مسح الفلاتر">
                                <i class="bi bi-x-circle"></i>
                            </button>
                            <button class="btn btn-outline-primary" onclick="window.ExpensesManager.showCategoriesModal()" title="إدارة الفئات">
                                <i class="bi bi-tags"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <label for="sort-expenses" class="form-label">ترتيب حسب</label>
                            <select class="form-select" id="sort-expenses">
                                <option value="expense_date-desc">التاريخ (الأحدث)</option>
                                <option value="expense_date-asc">التاريخ (الأقدم)</option>
                                <option value="amount-desc">المبلغ (الأعلى)</option>
                                <option value="amount-asc">المبلغ (الأقل)</option>
                                <option value="description-asc">الوصف (أ-ي)</option>
                                <option value="description-desc">الوصف (ي-أ)</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="expenses-per-page" class="form-label">عدد العناصر</label>
                            <select class="form-select" id="expenses-per-page">
                                <option value="15">15</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        
                        <div class="col-md-7 d-flex align-items-end justify-content-end">
                            <button class="btn btn-outline-success me-2" onclick="window.ExpensesManager.exportExpenses()">
                                <i class="bi bi-download"></i>
                                تصدير
                            </button>
                            <button class="btn btn-outline-info" onclick="window.ExpensesManager.printExpenses()">
                                <i class="bi bi-printer"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expenses Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>المبلغ</th>
                                    <th>الفئة</th>
                                    <th>المرفقات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="expenses-table-body">
                                <!-- Expenses will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center" id="expenses-pagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        `;

        // Set up event listeners after content is loaded
        setTimeout(() => this.setupEventListeners(), 100);
    }

    populateFilters() {
        // Populate category filter
        const categoryFilter = document.getElementById('expense-category-filter');
        if (categoryFilter) {
            categoryFilter.innerHTML = '<option value="all">جميع الفئات</option>';
            this.expenseCategories.forEach(category => {
                categoryFilter.innerHTML += `
                    <option value="${category.id}">${category.name}</option>
                `;
            });
        }
    }

    filterExpenses() {
        this.filteredExpenses = this.expenses.filter(expense => {
            const matchesSearch = !this.searchTerm ||
                expense.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                (expense.notes && expense.notes.toLowerCase().includes(this.searchTerm.toLowerCase()));

            const matchesCategory = this.categoryFilter === 'all' || expense.category_id == this.categoryFilter;

            const matchesDateFrom = !this.dateFromFilter || expense.expense_date >= this.dateFromFilter;
            const matchesDateTo = !this.dateToFilter || expense.expense_date <= this.dateToFilter;

            return matchesSearch && matchesCategory && matchesDateFrom && matchesDateTo;
        });

        this.currentPage = 1;
        this.renderExpenses();
    }

    sortExpenses() {
        this.filteredExpenses.sort((a, b) => {
            let aValue = a[this.sortBy];
            let bValue = b[this.sortBy];

            // Handle different data types
            if (this.sortBy.includes('date')) {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            } else if (this.sortBy === 'amount') {
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            } else if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        this.renderExpenses();
    }

    renderExpenses() {
        const tbody = document.getElementById('expenses-table-body');
        if (!tbody) return;

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedExpenses = this.filteredExpenses.slice(startIndex, endIndex);

        if (paginatedExpenses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="bi bi-receipt fs-1 d-block mb-2"></i>
                        لا توجد مصروفات مطابقة للبحث
                    </td>
                </tr>
            `;
        } else {
            tbody.innerHTML = paginatedExpenses.map(expense => `
                <tr>
                    <td>${DateUtils.formatDate(expense.expense_date)}</td>
                    <td>
                        <strong>${expense.description}</strong>
                        ${expense.notes ? `<br><small class="text-muted">${expense.notes}</small>` : ''}
                    </td>
                    <td><strong class="text-danger">${NumberUtils.formatCurrency(expense.amount)}</strong></td>
                    <td><span class="badge bg-secondary">${expense.category_name}</span></td>
                    <td>
                        ${expense.receipt_path ?
                            `<button class="btn btn-sm btn-outline-info" onclick="window.ExpensesManager.viewAttachment('${expense.receipt_path}')" title="عرض المرفق">
                                <i class="bi bi-paperclip"></i>
                            </button>` :
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.ExpensesManager.viewExpense(${expense.id})" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.ExpensesManager.editExpense(${expense.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="window.ExpensesManager.deleteExpense(${expense.id})" title="حذف">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        this.renderPagination();
    }

    renderPagination() {
        const pagination = document.getElementById('expenses-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredExpenses.length / this.itemsPerPage);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.ExpensesManager.goToPage(${this.currentPage - 1})">السابق</a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="window.ExpensesManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.ExpensesManager.goToPage(${this.currentPage + 1})">التالي</a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredExpenses.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderExpenses();
        }
    }

    updateExpenseStats() {
        const totalExpenses = this.expenses.reduce((sum, e) => sum + parseFloat(e.amount), 0);
        const totalCount = this.expenses.length;
        const avgExpense = totalCount > 0 ? totalExpenses / totalCount : 0;

        // Monthly expenses
        const currentMonth = moment().format('YYYY-MM');
        const monthlyExpenses = this.expenses
            .filter(e => moment(e.expense_date).format('YYYY-MM') === currentMonth)
            .reduce((sum, e) => sum + parseFloat(e.amount), 0);

        document.getElementById('total-expenses').textContent = NumberUtils.formatCurrency(totalExpenses);
        document.getElementById('monthly-expenses').textContent = NumberUtils.formatCurrency(monthlyExpenses);
        document.getElementById('total-expenses-count').textContent = NumberUtils.formatNumber(totalCount);
        document.getElementById('avg-expense').textContent = NumberUtils.formatCurrency(avgExpense);
    }

    clearFilters() {
        document.getElementById('expense-search').value = '';
        document.getElementById('expense-category-filter').value = 'all';
        document.getElementById('expense-date-from-filter').value = '';
        document.getElementById('expense-date-to-filter').value = '';

        this.searchTerm = '';
        this.categoryFilter = 'all';
        this.dateFromFilter = '';
        this.dateToFilter = '';

        this.filterExpenses();
    }

    // Expense CRUD operations
    showAddExpenseModal() {
        const modal = this.createExpenseModal('add');
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    async viewExpense(expenseId) {
        try {
            const expense = await database.get(`
                SELECT e.*,
                       ec.name as category_name
                FROM expenses e
                JOIN expense_categories ec ON e.category_id = ec.id
                WHERE e.id = ?
            `, [expenseId]);

            if (!expense) {
                UIUtils.showNotification('المصروف غير موجود', 'warning');
                return;
            }

            this.showExpenseDetailsModal(expense);
        } catch (error) {
            console.error('Error viewing expense:', error);
            UIUtils.showNotification('خطأ في عرض تفاصيل المصروف', 'danger');
        }
    }

    async editExpense(expenseId) {
        try {
            const expense = await database.get('SELECT * FROM expenses WHERE id = ?', [expenseId]);
            if (!expense) {
                UIUtils.showNotification('المصروف غير موجود', 'warning');
                return;
            }

            const modal = this.createExpenseModal('edit', expense);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        } catch (error) {
            console.error('Error editing expense:', error);
            UIUtils.showNotification('خطأ في تحميل بيانات المصروف للتعديل', 'danger');
        }
    }

    async deleteExpense(expenseId) {
        try {
            const expense = await database.get(`
                SELECT e.amount, e.description
                FROM expenses e
                WHERE e.id = ?
            `, [expenseId]);

            if (!expense) {
                UIUtils.showNotification('المصروف غير موجود', 'warning');
                return;
            }

            const confirmed = await UIUtils.showConfirmDialog(
                'تأكيد الحذف',
                `هل أنت متأكد من حذف المصروف؟\nالوصف: ${expense.description}\nالمبلغ: ${NumberUtils.formatCurrency(expense.amount)}`,
                'حذف',
                'إلغاء'
            );

            if (confirmed) {
                await database.run('DELETE FROM expenses WHERE id = ?', [expenseId]);
                UIUtils.showNotification('تم حذف المصروف بنجاح', 'success');
                await this.loadExpenses();
            }
        } catch (error) {
            console.error('Error deleting expense:', error);
            UIUtils.showNotification('خطأ في حذف المصروف', 'danger');
        }
    }

    createExpenseModal(mode, expense = null) {
        const isEdit = mode === 'edit';
        const modalId = `expense-modal-${Date.now()}`;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-receipt-${isEdit ? 'cutoff' : 'plus'}"></i>
                            ${isEdit ? 'تعديل المصروف' : 'تسجيل مصروف جديد'}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="expense-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="expense-category" class="form-label">فئة المصروف *</label>
                                        <select class="form-select" id="expense-category" required>
                                            <option value="">اختر فئة المصروف...</option>
                                            ${this.expenseCategories.map(category => `
                                                <option value="${category.id}"
                                                    ${expense?.category_id == category.id ? 'selected' : ''}>
                                                    ${category.name}
                                                </option>
                                            `).join('')}
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="expense-amount" class="form-label">المبلغ *</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="expense-amount"
                                                   value="${expense?.amount || ''}"
                                                   step="0.01" min="0" required>
                                            <span class="input-group-text">ر.س</span>
                                        </div>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="expense-date" class="form-label">تاريخ المصروف *</label>
                                        <input type="date" class="form-control" id="expense-date"
                                               value="${expense?.expense_date || moment().format('YYYY-MM-DD')}" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="expense-description" class="form-label">وصف المصروف *</label>
                                <input type="text" class="form-control" id="expense-description"
                                       value="${expense?.description || ''}"
                                       placeholder="وصف مختصر للمصروف..." required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="mb-3">
                                <label for="expense-notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="expense-notes" rows="3"
                                          placeholder="أي ملاحظات إضافية...">${expense?.notes || ''}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="expense-receipt" class="form-label">إرفاق إيصال أو فاتورة</label>
                                <input type="file" class="form-control" id="expense-receipt"
                                       accept="image/*,.pdf,.doc,.docx">
                                <div class="form-text">يمكن إرفاق صور أو ملفات PDF أو Word</div>
                                ${expense?.receipt_path ? `
                                    <div class="mt-2">
                                        <small class="text-muted">المرفق الحالي: </small>
                                        <button type="button" class="btn btn-sm btn-outline-info"
                                                onclick="window.ExpensesManager.viewAttachment('${expense.receipt_path}')">
                                            <i class="bi bi-paperclip"></i>
                                            عرض المرفق
                                        </button>
                                    </div>
                                ` : ''}
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="window.ExpensesManager.saveExpense('${mode}', ${expense?.id || 'null'}, '${modalId}')">
                            <i class="bi bi-check-lg"></i>
                            ${isEdit ? 'حفظ التعديلات' : 'تسجيل المصروف'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });

        return modal;
    }

    async saveExpense(mode, expenseId, modalId) {
        try {
            // Get form values
            const expenseData = {
                category_id: document.getElementById('expense-category').value,
                amount: document.getElementById('expense-amount').value,
                expense_date: document.getElementById('expense-date').value,
                description: document.getElementById('expense-description').value.trim(),
                notes: document.getElementById('expense-notes').value.trim()
            };

            // Handle file upload
            const receiptFile = document.getElementById('expense-receipt').files[0];
            let receiptPath = null;

            if (receiptFile) {
                receiptPath = await this.saveReceiptFile(receiptFile);
            }

            // Validate form
            const validation = this.validateExpenseForm(expenseData);
            if (!validation.isValid) {
                this.showFormErrors(validation.errors);
                return;
            }

            UIUtils.showLoading();

            if (mode === 'add') {
                await database.run(`
                    INSERT INTO expenses (category_id, amount, expense_date, description, notes, receipt_path)
                    VALUES (?, ?, ?, ?, ?, ?)
                `, [
                    expenseData.category_id,
                    expenseData.amount,
                    expenseData.expense_date,
                    expenseData.description,
                    expenseData.notes || null,
                    receiptPath
                ]);

                UIUtils.showNotification('تم تسجيل المصروف بنجاح', 'success');
            } else {
                // For edit, only update receipt_path if a new file was uploaded
                const updateFields = [
                    expenseData.category_id,
                    expenseData.amount,
                    expenseData.expense_date,
                    expenseData.description,
                    expenseData.notes || null
                ];

                let sql = `
                    UPDATE expenses
                    SET category_id = ?, amount = ?, expense_date = ?, description = ?, notes = ?
                `;

                if (receiptPath) {
                    sql += `, receipt_path = ?`;
                    updateFields.push(receiptPath);
                }

                sql += ` WHERE id = ?`;
                updateFields.push(expenseId);

                await database.run(sql, updateFields);

                UIUtils.showNotification('تم تحديث المصروف بنجاح', 'success');
            }

            // Close modal and refresh data
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            modal.hide();

            await this.loadExpenses();

        } catch (error) {
            console.error('Error saving expense:', error);
            UIUtils.showNotification('خطأ في حفظ المصروف', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    async saveReceiptFile(file) {
        try {
            const { app } = require('electron').remote || require('@electron/remote');
            const path = require('path');
            const fs = require('fs');

            // Create receipts directory if it doesn't exist
            const receiptsDir = path.join(app.getPath('userData'), 'receipts');
            if (!fs.existsSync(receiptsDir)) {
                fs.mkdirSync(receiptsDir, { recursive: true });
            }

            // Generate unique filename
            const timestamp = Date.now();
            const extension = path.extname(file.name);
            const filename = `receipt_${timestamp}${extension}`;
            const filePath = path.join(receiptsDir, filename);

            // Read file as buffer and save
            const buffer = await file.arrayBuffer();
            fs.writeFileSync(filePath, Buffer.from(buffer));

            return filePath;
        } catch (error) {
            console.error('Error saving receipt file:', error);
            throw new Error('خطأ في حفظ ملف الإيصال');
        }
    }

    validateExpenseForm(data) {
        const errors = {};
        let isValid = true;

        if (!data.category_id) {
            errors.category_id = 'يجب اختيار فئة المصروف';
            isValid = false;
        }

        if (!data.amount || parseFloat(data.amount) <= 0) {
            errors.amount = 'المبلغ يجب أن يكون أكبر من صفر';
            isValid = false;
        }

        if (!data.expense_date) {
            errors.expense_date = 'تاريخ المصروف مطلوب';
            isValid = false;
        }

        if (!data.description) {
            errors.description = 'وصف المصروف مطلوب';
            isValid = false;
        }

        return { isValid, errors };
    }

    showFormErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

        // Show new errors
        Object.keys(errors).forEach(field => {
            const input = document.getElementById(`expense-${field.replace('_', '-')}`);
            if (input) {
                input.classList.add('is-invalid');
                const feedback = input.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = errors[field];
                }
            }
        });
    }

    showExpenseDetailsModal(expense) {
        const modalId = `expense-details-modal-${Date.now()}`;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-receipt-cutoff"></i>
                            تفاصيل المصروف
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="bi bi-info-circle"></i>
                                            معلومات المصروف
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>المبلغ:</strong></td>
                                                <td><span class="h4 text-danger">${NumberUtils.formatCurrency(expense.amount)}</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>التاريخ:</strong></td>
                                                <td>${DateUtils.formatDate(expense.expense_date)}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>الفئة:</strong></td>
                                                <td><span class="badge bg-secondary">${expense.category_name}</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الوصف:</strong></td>
                                                <td>${expense.description}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>ملاحظات:</strong></td>
                                                <td>${expense.notes || '-'}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="bi bi-paperclip"></i>
                                            المرفقات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        ${expense.receipt_path ? `
                                            <div class="text-center">
                                                <i class="bi bi-file-earmark-text fs-1 text-info"></i>
                                                <p class="mt-2">يوجد مرفق لهذا المصروف</p>
                                                <button class="btn btn-outline-info" onclick="window.ExpensesManager.viewAttachment('${expense.receipt_path}')">
                                                    <i class="bi bi-eye"></i>
                                                    عرض المرفق
                                                </button>
                                            </div>
                                        ` : `
                                            <div class="text-center text-muted">
                                                <i class="bi bi-file-earmark-x fs-1"></i>
                                                <p class="mt-2">لا يوجد مرفقات لهذا المصروف</p>
                                            </div>
                                        `}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="window.ExpensesManager.editExpense(${expense.id}); bootstrap.Modal.getInstance(document.getElementById('${modalId}')).hide();">
                            <i class="bi bi-pencil"></i>
                            تعديل
                        </button>
                        <button type="button" class="btn btn-info" onclick="window.ExpensesManager.printExpenseVoucher(${expense.id})">
                            <i class="bi bi-printer"></i>
                            طباعة سند
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    viewAttachment(filePath) {
        try {
            const { shell } = require('electron');
            shell.openPath(filePath);
        } catch (error) {
            console.error('Error opening attachment:', error);
            UIUtils.showNotification('خطأ في فتح المرفق', 'danger');
        }
    }

    async printExpenseVoucher(expenseId) {
        try {
            const expense = await database.get(`
                SELECT e.*,
                       ec.name as category_name
                FROM expenses e
                JOIN expense_categories ec ON e.category_id = ec.id
                WHERE e.id = ?
            `, [expenseId]);

            if (!expense) {
                UIUtils.showNotification('المصروف غير موجود', 'warning');
                return;
            }

            const voucherContent = `
                <div class="text-center mb-4">
                    <h2>سند صرف</h2>
                    <p>نظام إدارة الديوان</p>
                </div>

                <div class="row mb-4">
                    <div class="col-6">
                        <strong>رقم السند:</strong> ${expense.id}<br>
                        <strong>التاريخ:</strong> ${DateUtils.formatDate(expense.expense_date)}<br>
                        <strong>الفئة:</strong> ${expense.category_name}
                    </div>
                    <div class="col-6">
                        <strong>تاريخ الإنشاء:</strong> ${DateUtils.formatDate(expense.created_at)}<br>
                    </div>
                </div>

                <div class="text-center border p-4 mb-4">
                    <h3>المبلغ المصروف</h3>
                    <h1 class="text-danger">${NumberUtils.formatCurrency(expense.amount)}</h1>
                </div>

                <div class="mb-4">
                    <strong>وصف المصروف:</strong><br>
                    ${expense.description}
                </div>

                ${expense.notes ? `
                    <div class="mb-4">
                        <strong>ملاحظات:</strong><br>
                        ${expense.notes}
                    </div>
                ` : ''}

                <div class="row mt-5">
                    <div class="col-6">
                        <div class="text-center">
                            <div style="border-top: 1px solid #000; width: 200px; margin: 0 auto;"></div>
                            <p class="mt-2">توقيع المستلم</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div style="border-top: 1px solid #000; width: 200px; margin: 0 auto;"></div>
                            <p class="mt-2">توقيع المسؤول</p>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <small class="text-muted">تم إنشاء هذا السند في ${DateUtils.getCurrentDateArabic()}</small>
                </div>
            `;

            // Create a temporary element for printing
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = voucherContent;
            tempDiv.id = 'expense-voucher-print';
            document.body.appendChild(tempDiv);

            // Print
            UIUtils.printContent('expense-voucher-print', `سند صرف - ${expense.description}`);

            // Clean up
            setTimeout(() => {
                document.body.removeChild(tempDiv);
            }, 1000);

        } catch (error) {
            console.error('Error printing expense voucher:', error);
            UIUtils.showNotification('خطأ في طباعة السند', 'danger');
        }
    }

    showCategoriesModal() {
        // This will be implemented to manage expense categories
        UIUtils.showNotification('سيتم تطوير إدارة الفئات في الإعدادات', 'info');
    }

    exportExpenses() {
        const exportData = this.filteredExpenses.map(expense => ({
            'التاريخ': DateUtils.formatDate(expense.expense_date),
            'الوصف': expense.description,
            'المبلغ': expense.amount,
            'الفئة': expense.category_name,
            'ملاحظات': expense.notes || '',
            'يوجد مرفق': expense.receipt_path ? 'نعم' : 'لا'
        }));

        UIUtils.exportToCSV(exportData, `expenses-${moment().format('YYYY-MM-DD')}.csv`);
    }

    printExpenses() {
        const printContent = `
            <div class="text-center mb-4">
                <h2>تقرير المصروفات</h2>
                <p>من ${this.dateFromFilter || 'البداية'} إلى ${this.dateToFilter || 'النهاية'}</p>
                <p>إجمالي المصروفات: ${this.filteredExpenses.length}</p>
                <p>إجمالي المبلغ: ${NumberUtils.formatCurrency(this.filteredExpenses.reduce((sum, e) => sum + parseFloat(e.amount), 0))}</p>
            </div>

            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الوصف</th>
                        <th>المبلغ</th>
                        <th>الفئة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.filteredExpenses.map(expense => `
                        <tr>
                            <td>${DateUtils.formatDate(expense.expense_date)}</td>
                            <td>${expense.description}</td>
                            <td>${NumberUtils.formatCurrency(expense.amount)}</td>
                            <td>${expense.category_name}</td>
                            <td>${expense.notes || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        // Create a temporary element for printing
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = printContent;
        tempDiv.id = 'expenses-print';
        document.body.appendChild(tempDiv);

        // Print
        UIUtils.printContent('expenses-print', 'تقرير المصروفات');

        // Clean up
        setTimeout(() => {
            document.body.removeChild(tempDiv);
        }, 1000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.ExpensesManager = new ExpensesManager();
        console.log('ExpensesManager initialized successfully');

        // Try to load expenses if database is ready
        setTimeout(() => {
            if (window.database && window.database.isReady) {
                console.log('Database is ready, loading expenses...');
                window.ExpensesManager.loadExpenses().catch(error => {
                    console.log('Initial expenses load failed, will retry when database is ready');
                });
            }
        }, 1000);

    } catch (error) {
        console.error('Error initializing ExpensesManager:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة نظام إدارة المصروفات', 'danger');
        }
    }
});

// Add database ready listener
window.addEventListener('database-ready', () => {
    console.log('Database ready, ExpensesManager can now load data');
    if (window.ExpensesManager) {
        setTimeout(() => {
            window.ExpensesManager.loadExpenses().catch(error => {
                console.error('Error loading expenses after database ready:', error);
            });
        }, 500);
    }
});
