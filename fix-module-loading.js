// Fix module loading issues

console.log('🔧 Module Loading Fix Script');
console.log('='.repeat(50));

// Function to check module loading status
function checkModuleStatus() {
    console.log('🔍 Checking module loading status...');
    
    const modules = {
        database: {
            loaded: !!window.database,
            ready: window.database && window.database.isReady,
            flag: window.databaseModuleLoaded
        },
        utils: {
            loaded: !!(window.UIUtils && window.DateUtils && window.NumberUtils),
            ready: true
        },
        navigation: {
            loaded: !!window.navigationManager,
            ready: true
        },
        members: {
            loaded: !!window.MembersManager,
            ready: true
        }
    };
    
    console.log('Module Status:', modules);
    
    let allLoaded = true;
    for (const [name, status] of Object.entries(modules)) {
        if (!status.loaded) {
            console.error(`❌ ${name} module not loaded`);
            allLoaded = false;
        } else {
            console.log(`✅ ${name} module loaded`);
        }
    }
    
    return { allLoaded, modules };
}

// Function to force reload database module
function forceReloadDatabase() {
    console.log('🔄 Force reloading database module...');
    
    try {
        // Clear existing database
        if (window.database) {
            if (window.database.db) {
                window.database.db.close();
            }
            delete window.database;
        }
        
        // Reset flags
        window.databaseModuleLoaded = false;
        
        // Try to recreate database
        const sqlite3 = require('sqlite3').verbose();
        const path = require('path');
        const fs = require('fs');
        const os = require('os');
        
        // Determine app path
        let appPath;
        try {
            const { app } = require('electron').remote || require('@electron/remote');
            appPath = app.getPath('userData');
        } catch (error) {
            appPath = path.join(os.homedir(), 'dewan-data');
        }
        
        // Create database class inline
        class Database {
            constructor() {
                this.db = null;
                this.dbPath = path.join(appPath, 'dewan.db');
                this.isReady = false;
                this.isInitializing = false;
                this.init();
            }
            
            init() {
                console.log('Initializing database...');
                
                const dbDir = path.dirname(this.dbPath);
                if (!fs.existsSync(dbDir)) {
                    fs.mkdirSync(dbDir, { recursive: true });
                }
                
                this.db = new sqlite3.Database(this.dbPath, (err) => {
                    if (err) {
                        console.error('Error opening database:', err);
                    } else {
                        console.log('Database connected successfully');
                        this.isReady = true;
                        window.databaseModuleLoaded = true;
                        window.dispatchEvent(new CustomEvent('database-module-loaded'));
                    }
                });
            }
            
            get(sql, params = []) {
                return new Promise((resolve, reject) => {
                    if (!this.db) {
                        reject(new Error('Database not initialized'));
                        return;
                    }
                    this.db.get(sql, params, (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    });
                });
            }
            
            all(sql, params = []) {
                return new Promise((resolve, reject) => {
                    if (!this.db) {
                        reject(new Error('Database not initialized'));
                        return;
                    }
                    this.db.all(sql, params, (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows || []);
                    });
                });
            }
            
            run(sql, params = []) {
                return new Promise((resolve, reject) => {
                    if (!this.db) {
                        reject(new Error('Database not initialized'));
                        return;
                    }
                    this.db.run(sql, params, function(err) {
                        if (err) reject(err);
                        else resolve({ id: this.lastID, changes: this.changes });
                    });
                });
            }
        }
        
        // Create new database instance
        window.database = new Database();
        console.log('✅ Database module recreated');
        
        return true;
    } catch (error) {
        console.error('❌ Error reloading database:', error);
        return false;
    }
}

// Function to wait for all modules to load
async function waitForAllModules() {
    console.log('⏳ Waiting for all modules to load...');
    
    const maxAttempts = 50; // 5 seconds
    let attempts = 0;
    
    while (attempts < maxAttempts) {
        const { allLoaded } = checkModuleStatus();
        
        if (allLoaded) {
            console.log('✅ All modules loaded successfully');
            return true;
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }
    
    console.log('❌ Timeout waiting for modules');
    return false;
}

// Function to fix module loading issues
async function fixModuleLoading() {
    console.log('🚀 Starting module loading fix...');
    
    try {
        // Step 1: Check current status
        const { allLoaded, modules } = checkModuleStatus();
        
        if (allLoaded) {
            console.log('✅ All modules already loaded');
            return true;
        }
        
        // Step 2: Fix database module if needed
        if (!modules.database.loaded) {
            console.log('🔧 Fixing database module...');
            if (!forceReloadDatabase()) {
                throw new Error('Failed to reload database module');
            }
            
            // Wait for database to be ready
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // Step 3: Wait for all modules
        const success = await waitForAllModules();
        
        if (success) {
            console.log('🎉 Module loading fix completed successfully');
            
            // Try to load members if possible
            if (window.MembersManager && window.database && window.database.isReady) {
                console.log('🔄 Attempting to load members...');
                try {
                    await window.MembersManager.loadMembers();
                    console.log('✅ Members loaded successfully');
                } catch (error) {
                    console.log('⚠️ Members loading failed, but modules are ready');
                }
            }
            
            return true;
        } else {
            throw new Error('Module loading timeout');
        }
        
    } catch (error) {
        console.error('❌ Module loading fix failed:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.checkModuleStatus = checkModuleStatus;
    window.forceReloadDatabase = forceReloadDatabase;
    window.waitForAllModules = waitForAllModules;
    window.fixModuleLoading = fixModuleLoading;
    
    console.log('🎯 Module fix functions available:');
    console.log('- checkModuleStatus()');
    console.log('- forceReloadDatabase()');
    console.log('- waitForAllModules()');
    console.log('- fixModuleLoading()');
}

// Auto-run fix if modules are not loaded
setTimeout(() => {
    if (typeof window !== 'undefined') {
        const { allLoaded } = checkModuleStatus();
        if (!allLoaded) {
            console.log('🔧 Auto-running module loading fix...');
            fixModuleLoading();
        }
    }
}, 3000);
