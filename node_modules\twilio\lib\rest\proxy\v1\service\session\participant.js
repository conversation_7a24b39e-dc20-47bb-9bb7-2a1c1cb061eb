"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Proxy
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipantPage = exports.ParticipantListInstance = exports.ParticipantInstance = exports.ParticipantContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
const messageInteraction_1 = require("./participant/messageInteraction");
class ParticipantContextImpl {
    constructor(_version, serviceSid, sessionSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(serviceSid)) {
            throw new Error("Parameter 'serviceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sessionSid)) {
            throw new Error("Parameter 'sessionSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { serviceSid, sessionSid, sid };
        this._uri = `/Services/${serviceSid}/Sessions/${sessionSid}/Participants/${sid}`;
    }
    get messageInteractions() {
        this._messageInteractions =
            this._messageInteractions ||
                (0, messageInteraction_1.MessageInteractionListInstance)(this._version, this._solution.serviceSid, this._solution.sessionSid, this._solution.sid);
        return this._messageInteractions;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.sessionSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantContextImpl = ParticipantContextImpl;
class ParticipantInstance {
    constructor(_version, payload, serviceSid, sessionSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.sessionSid = payload.session_sid;
        this.serviceSid = payload.service_sid;
        this.accountSid = payload.account_sid;
        this.friendlyName = payload.friendly_name;
        this.identifier = payload.identifier;
        this.proxyIdentifier = payload.proxy_identifier;
        this.proxyIdentifierSid = payload.proxy_identifier_sid;
        this.dateDeleted = deserialize.iso8601DateTime(payload.date_deleted);
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { serviceSid, sessionSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ParticipantContextImpl(this._version, this._solution.serviceSid, this._solution.sessionSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a ParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a ParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ParticipantInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the messageInteractions.
     */
    messageInteractions() {
        return this._proxy.messageInteractions;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            sessionSid: this.sessionSid,
            serviceSid: this.serviceSid,
            accountSid: this.accountSid,
            friendlyName: this.friendlyName,
            identifier: this.identifier,
            proxyIdentifier: this.proxyIdentifier,
            proxyIdentifierSid: this.proxyIdentifierSid,
            dateDeleted: this.dateDeleted,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantInstance = ParticipantInstance;
function ParticipantListInstance(version, serviceSid, sessionSid) {
    if (!(0, utility_1.isValidPathParam)(serviceSid)) {
        throw new Error("Parameter 'serviceSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(sessionSid)) {
        throw new Error("Parameter 'sessionSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new ParticipantContextImpl(version, serviceSid, sessionSid, sid);
    };
    instance._version = version;
    instance._solution = { serviceSid, sessionSid };
    instance._uri = `/Services/${serviceSid}/Sessions/${sessionSid}/Participants`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["identifier"] === null || params["identifier"] === undefined) {
            throw new Error("Required parameter \"params['identifier']\" missing.");
        }
        let data = {};
        data["Identifier"] = params["identifier"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["proxyIdentifier"] !== undefined)
            data["ProxyIdentifier"] = params["proxyIdentifier"];
        if (params["proxyIdentifierSid"] !== undefined)
            data["ProxyIdentifierSid"] = params["proxyIdentifierSid"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.sessionSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ParticipantPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ParticipantListInstance = ParticipantListInstance;
class ParticipantPage extends Page_1.default {
    /**
     * Initialize the ParticipantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ParticipantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ParticipantInstance(this._version, payload, this._solution.serviceSid, this._solution.sessionSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantPage = ParticipantPage;
