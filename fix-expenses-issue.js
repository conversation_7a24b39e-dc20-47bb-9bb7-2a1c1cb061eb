// Fix expenses loading issues

console.log('🔧 Expenses Fix Script');
console.log('='.repeat(50));

// Function to check expenses module status
function checkExpensesStatus() {
    console.log('🔍 Checking expenses module status...');
    
    const status = {
        expensesManager: !!window.ExpensesManager,
        database: !!window.database,
        databaseReady: window.database && window.database.isReady,
        expensesPage: !!document.getElementById('expenses-page')
    };
    
    console.log('Expenses Status:', status);
    
    let allReady = true;
    for (const [key, value] of Object.entries(status)) {
        if (!value) {
            console.error(`❌ ${key} not ready`);
            allReady = false;
        } else {
            console.log(`✅ ${key} ready`);
        }
    }
    
    return { allReady, status };
}

// Function to test database tables for expenses
async function testExpensesTables() {
    console.log('🔍 Testing expenses database tables...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Test expenses table
        const expensesCount = await window.database.get('SELECT COUNT(*) as count FROM expenses');
        console.log(`✅ Expenses table: ${expensesCount.count} records`);
        
        // Test expense_categories table
        const categoriesCount = await window.database.get('SELECT COUNT(*) as count FROM expense_categories');
        console.log(`✅ Expense categories table: ${categoriesCount.count} records`);
        
        // Test complex query (same as used in expenses)
        const testQuery = await window.database.all(`
            SELECT e.*, 
                   ec.name as category_name
            FROM expenses e
            JOIN expense_categories ec ON e.category_id = ec.id
            ORDER BY e.expense_date DESC
            LIMIT 5
        `);
        console.log(`✅ Complex expenses query: ${testQuery.length} records`);
        
        return true;
    } catch (error) {
        console.error('❌ Database tables test failed:', error);
        return false;
    }
}

// Function to create default expense categories if missing
async function createDefaultExpenseCategories() {
    console.log('🔧 Creating default expense categories...');
    
    try {
        const categoriesCount = await window.database.get('SELECT COUNT(*) as count FROM expense_categories');
        
        if (categoriesCount.count === 0) {
            console.log('No expense categories found, creating defaults...');
            
            const defaultCategories = [
                { name: 'فعاليات', description: 'مصروفات الفعاليات والأنشطة' },
                { name: 'خدمات', description: 'مصروفات الخدمات العامة' },
                { name: 'دعم حالات', description: 'دعم الحالات الاجتماعية' },
                { name: 'فواتير', description: 'الفواتير والمصروفات الثابتة' },
                { name: 'صيانة', description: 'مصروفات الصيانة والإصلاح' },
                { name: 'مكتبية', description: 'المصروفات المكتبية والإدارية' }
            ];
            
            for (const category of defaultCategories) {
                await window.database.run(`
                    INSERT INTO expense_categories (name, description) 
                    VALUES (?, ?)
                `, [category.name, category.description]);
                console.log(`✅ Created category: ${category.name}`);
            }
            
            console.log('✅ Default expense categories created');
            return true;
        } else {
            console.log('✅ Expense categories already exist');
            return true;
        }
    } catch (error) {
        console.error('❌ Error creating default expense categories:', error);
        return false;
    }
}

// Function to fix expenses loading
async function fixExpensesLoading() {
    console.log('🚀 Starting expenses loading fix...');
    
    try {
        // Step 1: Check current status
        const { allReady, status } = checkExpensesStatus();
        
        if (!status.database) {
            throw new Error('Database module not loaded');
        }
        
        if (!status.databaseReady) {
            console.log('⏳ Waiting for database to be ready...');
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Database ready timeout'));
                }, 10000);
                
                const checkReady = () => {
                    if (window.database && window.database.isReady) {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                
                checkReady();
            });
        }
        
        // Step 2: Test database tables
        const tablesOk = await testExpensesTables();
        if (!tablesOk) {
            throw new Error('Database tables test failed');
        }
        
        // Step 3: Create default categories if needed
        await createDefaultExpenseCategories();
        
        // Step 4: Check ExpensesManager
        if (!window.ExpensesManager) {
            console.log('🔧 ExpensesManager not found, creating...');
            // Try to recreate ExpensesManager
            if (typeof ExpensesManager !== 'undefined') {
                window.ExpensesManager = new ExpensesManager();
                console.log('✅ ExpensesManager recreated');
            } else {
                throw new Error('ExpensesManager class not available');
            }
        }
        
        // Step 5: Try to load expenses
        console.log('🔄 Attempting to load expenses...');
        await window.ExpensesManager.loadExpenses();
        console.log('✅ Expenses loaded successfully');
        
        return true;
        
    } catch (error) {
        console.error('❌ Expenses loading fix failed:', error);
        return false;
    }
}

// Function to reload expenses after fix
async function reloadExpensesAfterFix() {
    console.log('🔄 Reloading expenses data...');
    
    try {
        if (window.ExpensesManager && typeof window.ExpensesManager.loadExpenses === 'function') {
            await window.ExpensesManager.loadExpenses();
            console.log('✅ Expenses data reloaded successfully');
        } else {
            console.log('⚠️ ExpensesManager not available');
        }
    } catch (error) {
        console.error('❌ Error reloading expenses:', error);
    }
}

// Function to create sample expense data for testing
async function createSampleExpenseData() {
    console.log('🔧 Creating sample expense data...');
    
    try {
        // Check if we have categories
        const categoriesCount = await window.database.get('SELECT COUNT(*) as count FROM expense_categories');
        
        if (categoriesCount.count === 0) {
            await createDefaultExpenseCategories();
        }
        
        // Get first category
        const category = await window.database.get('SELECT id FROM expense_categories LIMIT 1');
        
        // Check if sample expense already exists
        const existingExpense = await window.database.get('SELECT COUNT(*) as count FROM expenses');
        
        if (existingExpense.count === 0) {
            // Create sample expense
            await window.database.run(`
                INSERT INTO expenses (category_id, amount, expense_date, description, notes)
                VALUES (?, ?, ?, ?, ?)
            `, [
                category.id,
                50.00,
                new Date().toISOString().split('T')[0],
                'مصروف تجريبي',
                'مصروف تجريبي للاختبار'
            ]);
            
            console.log('✅ Sample expense created');
            return true;
        } else {
            console.log('✅ Expenses already exist');
            return true;
        }
    } catch (error) {
        console.error('❌ Error creating sample expense data:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.checkExpensesStatus = checkExpensesStatus;
    window.testExpensesTables = testExpensesTables;
    window.createDefaultExpenseCategories = createDefaultExpenseCategories;
    window.fixExpensesLoading = fixExpensesLoading;
    window.reloadExpensesAfterFix = reloadExpensesAfterFix;
    window.createSampleExpenseData = createSampleExpenseData;
    
    console.log('🎯 Expenses fix functions available:');
    console.log('- checkExpensesStatus()');
    console.log('- testExpensesTables()');
    console.log('- createDefaultExpenseCategories()');
    console.log('- fixExpensesLoading()');
    console.log('- reloadExpensesAfterFix()');
    console.log('- createSampleExpenseData()');
}

// Auto-run fix if expenses are not working
setTimeout(() => {
    if (typeof window !== 'undefined') {
        const { allReady } = checkExpensesStatus();
        if (!allReady) {
            console.log('🔧 Auto-running expenses loading fix...');
            fixExpensesLoading().then((success) => {
                if (success) {
                    reloadExpensesAfterFix();
                }
            });
        }
    }
}, 5000); // Wait a bit longer for expenses module
