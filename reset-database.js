// Script to reset and recreate the database

const fs = require('fs');
const path = require('path');
const os = require('os');

// Determine database path
let dbPath;
try {
    const { app } = require('electron');
    dbPath = path.join(app.getPath('userData'), 'dewan.db');
} catch (error) {
    // Fallback for when running outside Electron
    dbPath = path.join(os.homedir(), 'dewan-data', 'dewan.db');
}

console.log('🔄 Resetting database...');
console.log('Database path:', dbPath);

// Check if database exists
if (fs.existsSync(dbPath)) {
    try {
        // Create backup first
        const backupPath = dbPath + '.backup.' + Date.now();
        fs.copyFileSync(dbPath, backupPath);
        console.log('✅ Backup created:', backupPath);
        
        // Delete old database
        fs.unlinkSync(dbPath);
        console.log('✅ Old database deleted');
    } catch (error) {
        console.error('❌ Error deleting old database:', error);
        process.exit(1);
    }
} else {
    console.log('ℹ️  No existing database found');
}

// Ensure directory exists
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
    console.log('✅ Database directory created');
}

console.log('✅ Database reset completed');
console.log('🚀 Now run: npm start');
console.log('📝 The database will be recreated automatically with all tables');
