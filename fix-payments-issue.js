// Fix payments loading issues

console.log('🔧 Payments Fix Script');
console.log('='.repeat(50));

// Function to check payments module status
function checkPaymentsStatus() {
    console.log('🔍 Checking payments module status...');
    
    const status = {
        paymentsManager: !!window.PaymentsManager,
        database: !!window.database,
        databaseReady: window.database && window.database.isReady,
        paymentsPage: !!document.getElementById('payments-page')
    };
    
    console.log('Payments Status:', status);
    
    let allReady = true;
    for (const [key, value] of Object.entries(status)) {
        if (!value) {
            console.error(`❌ ${key} not ready`);
            allReady = false;
        } else {
            console.log(`✅ ${key} ready`);
        }
    }
    
    return { allReady, status };
}

// Function to test database tables for payments
async function testPaymentsTables() {
    console.log('🔍 Testing payments database tables...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Test payments table
        const paymentsCount = await window.database.get('SELECT COUNT(*) as count FROM payments');
        console.log(`✅ Payments table: ${paymentsCount.count} records`);
        
        // Test revenue_categories table
        const categoriesCount = await window.database.get('SELECT COUNT(*) as count FROM revenue_categories');
        console.log(`✅ Revenue categories table: ${categoriesCount.count} records`);
        
        // Test members table (needed for payments)
        const membersCount = await window.database.get('SELECT COUNT(*) as count FROM members');
        console.log(`✅ Members table: ${membersCount.count} records`);
        
        // Test complex query (same as used in payments)
        const testQuery = await window.database.all(`
            SELECT p.*, 
                   m.full_name as member_name,
                   m.membership_id,
                   rc.name as category_name
            FROM payments p
            LEFT JOIN members m ON p.member_id = m.id
            LEFT JOIN revenue_categories rc ON p.category_id = rc.id
            ORDER BY p.payment_date DESC
            LIMIT 5
        `);
        console.log(`✅ Complex payments query: ${testQuery.length} records`);
        
        return true;
    } catch (error) {
        console.error('❌ Database tables test failed:', error);
        return false;
    }
}

// Function to create default revenue categories if missing
async function createDefaultRevenueCategories() {
    console.log('🔧 Creating default revenue categories...');
    
    try {
        const categoriesCount = await window.database.get('SELECT COUNT(*) as count FROM revenue_categories');
        
        if (categoriesCount.count === 0) {
            console.log('No revenue categories found, creating defaults...');
            
            const defaultCategories = [
                { name: 'اشتراك شهري', description: 'الاشتراك الشهري للأعضاء' },
                { name: 'تبرعات', description: 'التبرعات والهبات' },
                { name: 'دعم فعاليات', description: 'دعم الفعاليات والأنشطة' },
                { name: 'رسوم خدمات', description: 'رسوم الخدمات المختلفة' }
            ];
            
            for (const category of defaultCategories) {
                await window.database.run(`
                    INSERT INTO revenue_categories (name, description) 
                    VALUES (?, ?)
                `, [category.name, category.description]);
                console.log(`✅ Created category: ${category.name}`);
            }
            
            console.log('✅ Default revenue categories created');
            return true;
        } else {
            console.log('✅ Revenue categories already exist');
            return true;
        }
    } catch (error) {
        console.error('❌ Error creating default revenue categories:', error);
        return false;
    }
}

// Function to fix payments loading
async function fixPaymentsLoading() {
    console.log('🚀 Starting payments loading fix...');
    
    try {
        // Step 1: Check current status
        const { allReady, status } = checkPaymentsStatus();
        
        if (!status.database) {
            throw new Error('Database module not loaded');
        }
        
        if (!status.databaseReady) {
            console.log('⏳ Waiting for database to be ready...');
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Database ready timeout'));
                }, 10000);
                
                const checkReady = () => {
                    if (window.database && window.database.isReady) {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                
                checkReady();
            });
        }
        
        // Step 2: Test database tables
        const tablesOk = await testPaymentsTables();
        if (!tablesOk) {
            throw new Error('Database tables test failed');
        }
        
        // Step 3: Create default categories if needed
        await createDefaultRevenueCategories();
        
        // Step 4: Check PaymentsManager
        if (!window.PaymentsManager) {
            console.log('🔧 PaymentsManager not found, creating...');
            // Try to recreate PaymentsManager
            if (typeof PaymentsManager !== 'undefined') {
                window.PaymentsManager = new PaymentsManager();
                console.log('✅ PaymentsManager recreated');
            } else {
                throw new Error('PaymentsManager class not available');
            }
        }
        
        // Step 5: Try to load payments
        console.log('🔄 Attempting to load payments...');
        await window.PaymentsManager.loadPayments();
        console.log('✅ Payments loaded successfully');
        
        return true;
        
    } catch (error) {
        console.error('❌ Payments loading fix failed:', error);
        return false;
    }
}

// Function to reload payments after fix
async function reloadPaymentsAfterFix() {
    console.log('🔄 Reloading payments data...');
    
    try {
        if (window.PaymentsManager && typeof window.PaymentsManager.loadPayments === 'function') {
            await window.PaymentsManager.loadPayments();
            console.log('✅ Payments data reloaded successfully');
        } else {
            console.log('⚠️ PaymentsManager not available');
        }
    } catch (error) {
        console.error('❌ Error reloading payments:', error);
    }
}

// Function to create sample payment data for testing
async function createSamplePaymentData() {
    console.log('🔧 Creating sample payment data...');
    
    try {
        // Check if we have members and categories
        const membersCount = await window.database.get('SELECT COUNT(*) as count FROM members');
        const categoriesCount = await window.database.get('SELECT COUNT(*) as count FROM revenue_categories');
        
        if (membersCount.count === 0) {
            console.log('⚠️ No members found, cannot create sample payments');
            return false;
        }
        
        if (categoriesCount.count === 0) {
            await createDefaultRevenueCategories();
        }
        
        // Get first member and category
        const member = await window.database.get('SELECT id FROM members LIMIT 1');
        const category = await window.database.get('SELECT id FROM revenue_categories LIMIT 1');
        
        // Check if sample payment already exists
        const existingPayment = await window.database.get('SELECT COUNT(*) as count FROM payments');
        
        if (existingPayment.count === 0) {
            // Create sample payment
            await window.database.run(`
                INSERT INTO payments (member_id, category_id, amount, payment_date, payment_method, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [
                member.id,
                category.id,
                100.00,
                new Date().toISOString().split('T')[0],
                'cash',
                'دفعة تجريبية'
            ]);
            
            console.log('✅ Sample payment created');
            return true;
        } else {
            console.log('✅ Payments already exist');
            return true;
        }
    } catch (error) {
        console.error('❌ Error creating sample payment data:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.checkPaymentsStatus = checkPaymentsStatus;
    window.testPaymentsTables = testPaymentsTables;
    window.createDefaultRevenueCategories = createDefaultRevenueCategories;
    window.fixPaymentsLoading = fixPaymentsLoading;
    window.reloadPaymentsAfterFix = reloadPaymentsAfterFix;
    window.createSamplePaymentData = createSamplePaymentData;
    
    console.log('🎯 Payments fix functions available:');
    console.log('- checkPaymentsStatus()');
    console.log('- testPaymentsTables()');
    console.log('- createDefaultRevenueCategories()');
    console.log('- fixPaymentsLoading()');
    console.log('- reloadPaymentsAfterFix()');
    console.log('- createSamplePaymentData()');
}

// Auto-run fix if payments are not working
setTimeout(() => {
    if (typeof window !== 'undefined') {
        const { allReady } = checkPaymentsStatus();
        if (!allReady) {
            console.log('🔧 Auto-running payments loading fix...');
            fixPaymentsLoading().then((success) => {
                if (success) {
                    reloadPaymentsAfterFix();
                }
            });
        }
    }
}, 4000); // Wait a bit longer for payments module
