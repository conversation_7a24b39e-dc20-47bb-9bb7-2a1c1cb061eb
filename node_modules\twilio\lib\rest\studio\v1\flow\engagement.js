"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Studio
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EngagementPage = exports.EngagementListInstance = exports.EngagementInstance = exports.EngagementContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const engagementContext_1 = require("./engagement/engagementContext");
const step_1 = require("./engagement/step");
class EngagementContextImpl {
    constructor(_version, flowSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(flowSid)) {
            throw new Error("Parameter 'flowSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { flowSid, sid };
        this._uri = `/Flows/${flowSid}/Engagements/${sid}`;
    }
    get engagementContext() {
        this._engagementContext =
            this._engagementContext ||
                (0, engagementContext_1.EngagementContextListInstance)(this._version, this._solution.flowSid, this._solution.sid);
        return this._engagementContext;
    }
    get steps() {
        this._steps =
            this._steps ||
                (0, step_1.StepListInstance)(this._version, this._solution.flowSid, this._solution.sid);
        return this._steps;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new EngagementInstance(operationVersion, payload, instance._solution.flowSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.EngagementContextImpl = EngagementContextImpl;
class EngagementInstance {
    constructor(_version, payload, flowSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.flowSid = payload.flow_sid;
        this.contactSid = payload.contact_sid;
        this.contactChannelAddress = payload.contact_channel_address;
        this.context = payload.context;
        this.status = payload.status;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { flowSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new EngagementContextImpl(this._version, this._solution.flowSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a EngagementInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a EngagementInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed EngagementInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the engagementContext.
     */
    engagementContext() {
        return this._proxy.engagementContext;
    }
    /**
     * Access the steps.
     */
    steps() {
        return this._proxy.steps;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            flowSid: this.flowSid,
            contactSid: this.contactSid,
            contactChannelAddress: this.contactChannelAddress,
            context: this.context,
            status: this.status,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.EngagementInstance = EngagementInstance;
function EngagementListInstance(version, flowSid) {
    if (!(0, utility_1.isValidPathParam)(flowSid)) {
        throw new Error("Parameter 'flowSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new EngagementContextImpl(version, flowSid, sid);
    };
    instance._version = version;
    instance._solution = { flowSid };
    instance._uri = `/Flows/${flowSid}/Engagements`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["to"] === null || params["to"] === undefined) {
            throw new Error("Required parameter \"params['to']\" missing.");
        }
        if (params["from"] === null || params["from"] === undefined) {
            throw new Error("Required parameter \"params['from']\" missing.");
        }
        let data = {};
        data["To"] = params["to"];
        data["From"] = params["from"];
        if (params["parameters"] !== undefined)
            data["Parameters"] = serialize.object(params["parameters"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new EngagementInstance(operationVersion, payload, instance._solution.flowSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new EngagementPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new EngagementPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.EngagementListInstance = EngagementListInstance;
class EngagementPage extends Page_1.default {
    /**
     * Initialize the EngagementPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of EngagementInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new EngagementInstance(this._version, payload, this._solution.flowSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.EngagementPage = EngagementPage;
