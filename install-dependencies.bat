@echo off
echo ========================================
echo     تثبيت مكتبات نظام إدارة الديوان
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo.
    echo يرجى تثبيت Node.js أولاً من:
    echo https://nodejs.org
    echo.
    echo اختر النسخة LTS (الموصى بها)
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت - الإصدار:
node --version
echo.

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

echo ✅ npm متاح - الإصدار:
npm --version
echo.

echo 📦 بدء تثبيت المكتبات المطلوبة...
echo هذا قد يستغرق بضع دقائق...
echo.

REM Install production dependencies
echo 🔧 تثبيت المكتبات الأساسية...
npm install sqlite3 bootstrap bootstrap-icons chart.js jspdf xlsx moment moment-hijri twilio node-cron archiver multer

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات الأساسية
    pause
    exit /b 1
)

echo ✅ تم تثبيت المكتبات الأساسية
echo.

REM Install development dependencies
echo 🔧 تثبيت مكتبات التطوير...
npm install electron electron-builder --save-dev

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت مكتبات التطوير
    pause
    exit /b 1
)

echo ✅ تم تثبيت مكتبات التطوير
echo.

REM Create necessary directories
echo 📁 إنشاء المجلدات المطلوبة...

if not exist "database" (
    mkdir database
    echo ✅ تم إنشاء مجلد database
)

if not exist "assets" (
    mkdir assets
    echo ✅ تم إنشاء مجلد assets
)

if not exist "dist" (
    mkdir dist
    echo ✅ تم إنشاء مجلد dist
)

echo.

REM Check if main files exist
echo 🔍 فحص الملفات الأساسية...

if not exist "src\main.js" (
    echo ⚠️ تحذير: ملف src\main.js غير موجود
)

if not exist "src\renderer\index.html" (
    echo ⚠️ تحذير: ملف src\renderer\index.html غير موجود
)

if not exist "package.json" (
    echo ⚠️ تحذير: ملف package.json غير موجود
)

echo.

REM Display installed packages
echo 📋 المكتبات المثبتة:
npm list --depth=0

echo.
echo ========================================
echo ✅ تم تثبيت جميع المكتبات بنجاح!
echo ========================================
echo.
echo الخطوات التالية:
echo 1. تشغيل التطبيق: run-desktop.bat
echo 2. بناء التطبيق: npm run build
echo 3. إنشاء ملف تثبيت: npm run dist
echo.
echo للمساعدة:
echo - npm start : تشغيل التطبيق
echo - npm run dev : تشغيل في وضع التطوير
echo - npm run build : بناء التطبيق
echo.
pause
