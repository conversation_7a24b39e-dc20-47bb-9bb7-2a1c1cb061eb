// Database module for Electron with proper context isolation
// This module works with the preload script to provide secure database access

class ElectronDatabase {
    constructor() {
        this.db = null;
        this.dbPath = null;
        this.isReady = false;
        this.isInitializing = false;
        this.initPromise = null;
        
        // Initialize database
        this.init();
    }

    async init() {
        if (this.isInitializing) {
            console.log('Database initialization already in progress, waiting...');
            return this.initPromise;
        }

        this.isInitializing = true;
        
        this.initPromise = new Promise(async (resolve, reject) => {
            try {
                console.log('🔄 Initializing Electron database...');

                // Check if we have Electron API access
                if (!window.electronAPI) {
                    throw new Error('Electron API not available');
                }

                // Get database path
                const dbDir = window.databasePath.ensureDatabaseDir();
                if (!dbDir) {
                    throw new Error('Could not create database directory');
                }

                this.dbPath = window.databasePath.getPath();
                console.log('📁 Database path:', this.dbPath);

                // Create database connection
                this.db = window.electronAPI.database.createConnection(this.dbPath);
                if (!this.db) {
                    throw new Error('Could not create database connection');
                }

                console.log('✅ Database connection created');

                // Enable foreign keys
                await this.run('PRAGMA foreign_keys = ON');
                console.log('🔗 Foreign keys enabled');

                // Create tables
                await this.createTables();
                console.log('📋 Tables created/verified');

                // Insert default data
                await this.insertDefaultData();
                console.log('📊 Default data inserted');

                this.isReady = true;
                this.isInitializing = false;

                // Dispatch ready event
                window.dispatchEvent(new CustomEvent('database-ready'));
                console.log('🎉 Database is ready for use');

                resolve(this);

            } catch (error) {
                console.error('❌ Database initialization failed:', error);
                this.isReady = false;
                this.isInitializing = false;
                this.handleDatabaseError(error);
                reject(error);
            }
        });

        return this.initPromise;
    }

    handleDatabaseError(error) {
        console.error('Database error:', error);
        this.isReady = false;
        
        // Show user-friendly error message
        if (typeof window !== 'undefined' && window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة قاعدة البيانات: ' + error.message, 'danger');
        }

        // Dispatch error event
        window.dispatchEvent(new CustomEvent('database-error', {
            detail: { error: error.message }
        }));
    }

    async waitForReady(timeout = 10000) {
        if (this.isReady) {
            return true;
        }

        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Database ready timeout'));
            }, timeout);

            const handleReady = () => {
                clearTimeout(timeoutId);
                window.removeEventListener('database-ready', handleReady);
                resolve(true);
            };

            window.addEventListener('database-ready', handleReady);

            // If already initializing, wait for the promise
            if (this.initPromise) {
                this.initPromise.then(() => {
                    if (this.isReady) {
                        handleReady();
                    }
                }).catch(() => {
                    clearTimeout(timeoutId);
                    window.removeEventListener('database-ready', handleReady);
                    reject(new Error('Database initialization failed'));
                });
            }
        });
    }

    async run(sql, params = []) {
        if (!this.isReady) {
            await this.waitForReady();
        }

        try {
            return await window.electronAPI.database.run(this.db, sql, params);
        } catch (error) {
            console.error('Database run error:', error, 'SQL:', sql, 'Params:', params);
            throw error;
        }
    }

    async get(sql, params = []) {
        if (!this.isReady) {
            await this.waitForReady();
        }

        try {
            return await window.electronAPI.database.get(this.db, sql, params);
        } catch (error) {
            console.error('Database get error:', error, 'SQL:', sql, 'Params:', params);
            throw error;
        }
    }

    async all(sql, params = []) {
        if (!this.isReady) {
            await this.waitForReady();
        }

        try {
            return await window.electronAPI.database.all(this.db, sql, params);
        } catch (error) {
            console.error('Database all error:', error, 'SQL:', sql, 'Params:', params);
            throw error;
        }
    }

    async close() {
        if (this.db) {
            try {
                await window.electronAPI.database.close(this.db);
                this.db = null;
                this.isReady = false;
                console.log('🔒 Database connection closed');
            } catch (error) {
                console.error('Error closing database:', error);
                throw error;
            }
        }
    }

    async createTables() {
        console.log('📋 Creating database tables...');
        
        const tables = [
            // Members table
            `CREATE TABLE IF NOT EXISTS members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                membership_id TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                join_date DATE NOT NULL,
                status TEXT DEFAULT 'active' CHECK(status IN ('active', 'inactive', 'suspended')),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Revenue categories table
            `CREATE TABLE IF NOT EXISTS revenue_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Payments table
            `CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                member_id INTEGER NOT NULL,
                category_id INTEGER NOT NULL,
                amount DECIMAL(10,3) NOT NULL,
                payment_date DATE NOT NULL,
                payment_method TEXT DEFAULT 'cash' CHECK(payment_method IN ('cash', 'bank_transfer', 'check', 'credit_card', 'other')),
                reference_number TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (member_id) REFERENCES members (id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES revenue_categories (id)
            )`,

            // Expense categories table
            `CREATE TABLE IF NOT EXISTS expense_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Expenses table
            `CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_id INTEGER NOT NULL,
                amount DECIMAL(10,3) NOT NULL,
                expense_date DATE NOT NULL,
                description TEXT NOT NULL,
                receipt_path TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES expense_categories (id)
            )`,

            // Settings table
            `CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT NOT NULL UNIQUE,
                value TEXT,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        // Create tables sequentially
        for (let i = 0; i < tables.length; i++) {
            try {
                await this.run(tables[i]);
                console.log(`✅ Table ${i + 1}/${tables.length} created successfully`);
            } catch (error) {
                console.error(`❌ Error creating table ${i + 1}:`, error);
                throw error;
            }
        }

        console.log('🎉 All tables created successfully');
    }

    async insertDefaultData() {
        console.log('📊 Inserting default data...');

        try {
            // Default revenue categories
            const revenueCategories = [
                ['اشتراك شهري', 'الاشتراك الشهري للأعضاء'],
                ['تبرعات', 'التبرعات المختلفة'],
                ['دعم الفعاليات', 'دعم الفعاليات والأنشطة'],
                ['رسوم خدمات', 'رسوم الخدمات المختلفة']
            ];

            for (const [name, description] of revenueCategories) {
                await this.run(
                    'INSERT OR IGNORE INTO revenue_categories (name, description) VALUES (?, ?)',
                    [name, description]
                );
            }

            // Default expense categories
            const expenseCategories = [
                ['فعاليات', 'مصروفات الفعاليات والأنشطة'],
                ['خدمات', 'مصروفات الخدمات المقدمة'],
                ['دعم الحالات', 'دعم الحالات المحتاجة'],
                ['فواتير', 'الفواتير والمصروفات الإدارية'],
                ['صيانة', 'مصروفات الصيانة والتطوير']
            ];

            for (const [name, description] of expenseCategories) {
                await this.run(
                    'INSERT OR IGNORE INTO expense_categories (name, description) VALUES (?, ?)',
                    [name, description]
                );
            }

            // Default settings
            const defaultSettings = [
                ['currency', 'د.أ', 'العملة المستخدمة'],
                ['organization_name', 'الديوان', 'اسم المنظمة'],
                ['monthly_subscription', '25.000', 'قيمة الاشتراك الشهري'],
                ['reminder_days', '5', 'عدد أيام التذكير قبل الاستحقاق'],
                ['backup_frequency', 'weekly', 'تكرار النسخ الاحتياطي'],
                ['date_format', 'DD/MM/YYYY', 'تنسيق التاريخ']
            ];

            for (const [key, value, description] of defaultSettings) {
                await this.run(
                    'INSERT OR IGNORE INTO settings (key, value, description) VALUES (?, ?, ?)',
                    [key, value, description]
                );
            }

            console.log('✅ Default data inserted successfully');

        } catch (error) {
            console.error('❌ Error inserting default data:', error);
            // Don't throw error, just log it
        }
    }

    async backup(backupPath) {
        try {
            if (!window.electronAPI.fileExists(this.dbPath)) {
                throw new Error('Database file not found');
            }

            const dbContent = window.electronAPI.readFile(this.dbPath);
            if (!dbContent) {
                throw new Error('Could not read database file');
            }

            const success = window.electronAPI.writeFile(backupPath, dbContent);
            if (!success) {
                throw new Error('Could not write backup file');
            }

            console.log('✅ Database backup created:', backupPath);
            return backupPath;

        } catch (error) {
            console.error('❌ Database backup failed:', error);
            throw error;
        }
    }
}

// Create and export database instance
let database;

function createElectronDatabase() {
    try {
        console.log('🔄 Creating Electron database instance...');
        database = new ElectronDatabase();
        return database;
    } catch (error) {
        console.error('❌ Critical error creating Electron database:', error);
        
        // Create fallback database object
        return {
            isReady: false,
            error: error.message,
            init: () => Promise.reject(error),
            waitForReady: () => Promise.reject(error),
            get: () => Promise.reject(new Error('Database not available')),
            all: () => Promise.reject(new Error('Database not available')),
            run: () => Promise.reject(new Error('Database not available')),
            close: () => Promise.resolve(),
            backup: () => Promise.reject(new Error('Database not available'))
        };
    }
}

// Initialize database
if (typeof window !== 'undefined') {
    // Wait for Electron API to be available
    if (window.electronAPI) {
        database = createElectronDatabase();
        window.database = database;
        console.log('✅ Electron database attached to window');
    } else {
        // Wait for preload to finish
        window.addEventListener('DOMContentLoaded', () => {
            if (window.electronAPI) {
                database = createElectronDatabase();
                window.database = database;
                console.log('✅ Electron database attached to window (delayed)');
            } else {
                console.error('❌ Electron API not available after DOMContentLoaded');
            }
        });
    }

    // Dispatch module loaded event
    window.dispatchEvent(new CustomEvent('database-module-loaded', {
        detail: { database: database }
    }));
    
    window.databaseModuleLoaded = true;
}

console.log('📦 Electron database module loaded');

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = database;
}
