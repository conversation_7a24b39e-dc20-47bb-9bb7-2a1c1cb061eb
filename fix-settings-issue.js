// Fix settings loading issues

console.log('🔧 Settings Fix Script');
console.log('='.repeat(50));

// Function to check settings module status
function checkSettingsStatus() {
    console.log('🔍 Checking settings module status...');
    
    const status = {
        settingsManager: !!window.SettingsManager,
        database: !!window.database,
        databaseReady: window.database && window.database.isReady,
        settingsPage: !!document.getElementById('settings-page')
    };
    
    console.log('Settings Status:', status);
    
    let allReady = true;
    for (const [key, value] of Object.entries(status)) {
        if (!value) {
            console.error(`❌ ${key} not ready`);
            allReady = false;
        } else {
            console.log(`✅ ${key} ready`);
        }
    }
    
    return { allReady, status };
}

// Function to test settings database tables
async function testSettingsTables() {
    console.log('🔍 Testing settings database tables...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Test settings table
        const settingsCount = await window.database.get('SELECT COUNT(*) as count FROM settings');
        console.log(`✅ Settings table: ${settingsCount.count} records`);
        
        // Test users table
        const usersCount = await window.database.get('SELECT COUNT(*) as count FROM users');
        console.log(`✅ Users table: ${usersCount.count} records`);
        
        // Test revenue_categories table (used in settings)
        const revenueCategoriesCount = await window.database.get('SELECT COUNT(*) as count FROM revenue_categories');
        console.log(`✅ Revenue categories table: ${revenueCategoriesCount.count} records`);
        
        // Test expense_categories table (used in settings)
        const expenseCategoriesCount = await window.database.get('SELECT COUNT(*) as count FROM expense_categories');
        console.log(`✅ Expense categories table: ${expenseCategoriesCount.count} records`);
        
        return true;
    } catch (error) {
        console.error('❌ Settings database tables test failed:', error);
        return false;
    }
}

// Function to create default settings if missing
async function createDefaultSettings() {
    console.log('🔧 Creating default settings...');
    
    try {
        const settingsCount = await window.database.get('SELECT COUNT(*) as count FROM settings');
        
        if (settingsCount.count === 0) {
            console.log('No settings found, creating defaults...');
            
            const defaultSettings = [
                { key: 'organization_name', value: 'ديوان المجتمع', description: 'اسم المنظمة' },
                { key: 'monthly_subscription', value: '25', description: 'قيمة الاشتراك الشهري' },
                { key: 'currency', value: 'د.أ', description: 'العملة المستخدمة' },
                { key: 'admin_phone', value: '0791234567', description: 'رقم هاتف المدير للإرسال' },
                { key: 'payment_message_template', value: 'تم استلام دفعتك بمبلغ {amount} {currency} بتاريخ {date}. شكراً لك - {organization}', description: 'نموذج رسالة تأكيد الدفع' },
                { key: 'reminder_days', value: '5', description: 'عدد أيام التذكير قبل الاستحقاق' },
                { key: 'date_format', value: 'DD/MM/YYYY', description: 'تنسيق التاريخ' },
                { key: 'backup_frequency', value: 'weekly', description: 'تكرار النسخ الاحتياطي' },
                { key: 'enable_notifications', value: '1', description: 'تفعيل التنبيهات' },
                { key: 'enable_sound_notifications', value: '1', description: 'تفعيل الأصوات' },
                { key: 'auto_send_payment_confirmation', value: '1', description: 'إرسال تأكيد الدفع تلقائياً' }
            ];
            
            for (const setting of defaultSettings) {
                await window.database.run(`
                    INSERT INTO settings (key, value, description) 
                    VALUES (?, ?, ?)
                `, [setting.key, setting.value, setting.description]);
                console.log(`✅ Created setting: ${setting.key}`);
            }
            
            console.log('✅ Default settings created');
            return true;
        } else {
            console.log('✅ Settings already exist');
            return true;
        }
    } catch (error) {
        console.error('❌ Error creating default settings:', error);
        return false;
    }
}

// Function to create default admin user if missing
async function createDefaultAdminUser() {
    console.log('🔧 Creating default admin user...');
    
    try {
        const adminUser = await window.database.get('SELECT * FROM users WHERE username = ? LIMIT 1', ['admin']);
        
        if (!adminUser) {
            console.log('No admin user found, creating default...');
            
            // Create default admin user (password: admin123)
            const passwordHash = 'admin123'; // In real app, this should be properly hashed
            
            await window.database.run(`
                INSERT INTO users (username, password_hash, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            `, ['admin', passwordHash, 'المدير العام', 'admin', 1]);
            
            console.log('✅ Default admin user created (username: admin, password: admin123)');
            return true;
        } else {
            console.log('✅ Admin user already exists');
            return true;
        }
    } catch (error) {
        console.error('❌ Error creating default admin user:', error);
        return false;
    }
}

// Function to fix settings loading
async function fixSettingsLoading() {
    console.log('🚀 Starting settings loading fix...');
    
    try {
        // Step 1: Check current status
        const { allReady, status } = checkSettingsStatus();
        
        if (!status.database) {
            throw new Error('Database module not loaded');
        }
        
        if (!status.databaseReady) {
            console.log('⏳ Waiting for database to be ready...');
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Database ready timeout'));
                }, 10000);
                
                const checkReady = () => {
                    if (window.database && window.database.isReady) {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                
                checkReady();
            });
        }
        
        // Step 2: Test database tables
        const tablesOk = await testSettingsTables();
        if (!tablesOk) {
            console.warn('⚠️ Some database tables had issues, but continuing...');
        }
        
        // Step 3: Create default settings if needed
        await createDefaultSettings();
        
        // Step 4: Create default admin user if needed
        await createDefaultAdminUser();
        
        // Step 5: Check SettingsManager
        if (!window.SettingsManager) {
            console.log('🔧 SettingsManager not found, creating...');
            // Try to recreate SettingsManager
            if (typeof SettingsManager !== 'undefined') {
                window.SettingsManager = new SettingsManager();
                console.log('✅ SettingsManager recreated');
            } else {
                throw new Error('SettingsManager class not available');
            }
        }
        
        // Step 6: Try to load settings
        console.log('🔄 Attempting to load settings...');
        await window.SettingsManager.loadSettings();
        console.log('✅ Settings loaded successfully');
        
        return true;
        
    } catch (error) {
        console.error('❌ Settings loading fix failed:', error);
        return false;
    }
}

// Function to reload settings after fix
async function reloadSettingsAfterFix() {
    console.log('🔄 Reloading settings data...');
    
    try {
        if (window.SettingsManager && typeof window.SettingsManager.loadSettings === 'function') {
            await window.SettingsManager.loadSettings();
            console.log('✅ Settings data reloaded successfully');
        } else {
            console.log('⚠️ SettingsManager not available');
        }
    } catch (error) {
        console.error('❌ Error reloading settings:', error);
    }
}

// Function to test settings functionality
async function testSettingsFunctionality() {
    console.log('🔍 Testing settings functionality...');
    
    try {
        // Test reading settings
        const settings = await window.database.all('SELECT * FROM settings LIMIT 5');
        console.log(`✅ Can read settings: ${settings.length} records`);
        
        // Test reading users
        const users = await window.database.all('SELECT * FROM users LIMIT 5');
        console.log(`✅ Can read users: ${users.length} records`);
        
        // Test reading categories
        const revenueCategories = await window.database.all('SELECT * FROM revenue_categories LIMIT 5');
        const expenseCategories = await window.database.all('SELECT * FROM expense_categories LIMIT 5');
        console.log(`✅ Can read categories: ${revenueCategories.length} revenue, ${expenseCategories.length} expense`);
        
        return true;
    } catch (error) {
        console.error('❌ Settings functionality test failed:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.checkSettingsStatus = checkSettingsStatus;
    window.testSettingsTables = testSettingsTables;
    window.createDefaultSettings = createDefaultSettings;
    window.createDefaultAdminUser = createDefaultAdminUser;
    window.fixSettingsLoading = fixSettingsLoading;
    window.reloadSettingsAfterFix = reloadSettingsAfterFix;
    window.testSettingsFunctionality = testSettingsFunctionality;
    
    console.log('🎯 Settings fix functions available:');
    console.log('- checkSettingsStatus()');
    console.log('- testSettingsTables()');
    console.log('- createDefaultSettings()');
    console.log('- createDefaultAdminUser()');
    console.log('- fixSettingsLoading()');
    console.log('- reloadSettingsAfterFix()');
    console.log('- testSettingsFunctionality()');
}

// Auto-run fix if settings are not working
setTimeout(() => {
    if (typeof window !== 'undefined') {
        const { allReady } = checkSettingsStatus();
        if (!allReady) {
            console.log('🔧 Auto-running settings loading fix...');
            fixSettingsLoading().then((success) => {
                if (success) {
                    reloadSettingsAfterFix();
                }
            });
        }
    }
}, 8000); // Wait longer for settings module
