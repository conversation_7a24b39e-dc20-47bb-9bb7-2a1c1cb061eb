// Diagnostic tools for troubleshooting

class DiagnosticsManager {
    constructor() {
        this.tests = [];
        this.results = {};
    }

    async runDiagnostics() {
        console.log('🔍 Running system diagnostics...');
        
        const tests = [
            { name: 'Database Connection', test: this.testDatabaseConnection },
            { name: 'Database Tables', test: this.testDatabaseTables },
            { name: 'Members Table', test: this.testMembersTable },
            { name: 'UI Elements', test: this.testUIElements },
            { name: 'JavaScript Modules', test: this.testJavaScriptModules },
            { name: 'Event Listeners', test: this.testEventListeners }
        ];

        for (const test of tests) {
            try {
                console.log(`Testing: ${test.name}`);
                const result = await test.test.call(this);
                this.results[test.name] = { status: 'PASS', result };
                console.log(`✅ ${test.name}: PASS`);
            } catch (error) {
                this.results[test.name] = { status: 'FAIL', error: error.message };
                console.error(`❌ ${test.name}: FAIL - ${error.message}`);
            }
        }

        this.displayResults();
        return this.results;
    }

    async testDatabaseConnection() {
        if (!window.database) {
            throw new Error('Database object not found');
        }

        if (!window.database.db) {
            throw new Error('Database connection not established');
        }

        // Test simple query
        const result = await window.database.get('SELECT 1 as test');
        if (result.test !== 1) {
            throw new Error('Database query test failed');
        }

        return 'Database connection working';
    }

    async testDatabaseTables() {
        const tables = await window.database.all(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        `);

        const expectedTables = [
            'members', 'payments', 'expenses', 'revenue_categories', 
            'expense_categories', 'settings', 'users', 'whatsapp_messages', 'audit_log'
        ];

        const foundTables = tables.map(t => t.name);
        const missingTables = expectedTables.filter(t => !foundTables.includes(t));

        if (missingTables.length > 0) {
            throw new Error(`Missing tables: ${missingTables.join(', ')}`);
        }

        return `Found ${foundTables.length} tables: ${foundTables.join(', ')}`;
    }

    async testMembersTable() {
        // Test members table structure
        const tableInfo = await window.database.all('PRAGMA table_info(members)');
        const columns = tableInfo.map(col => col.name);
        
        const expectedColumns = ['id', 'membership_id', 'full_name', 'phone', 'address', 'join_date', 'status', 'notes'];
        const missingColumns = expectedColumns.filter(col => !columns.includes(col));
        
        if (missingColumns.length > 0) {
            throw new Error(`Missing columns in members table: ${missingColumns.join(', ')}`);
        }

        // Test sample query
        const count = await window.database.get('SELECT COUNT(*) as count FROM members');
        
        return `Members table OK, ${count.count} records found`;
    }

    testUIElements() {
        const requiredElements = [
            'members-page',
            'main-content',
            'loading-spinner'
        ];

        const missingElements = requiredElements.filter(id => !document.getElementById(id));
        
        if (missingElements.length > 0) {
            throw new Error(`Missing UI elements: ${missingElements.join(', ')}`);
        }

        return 'All required UI elements found';
    }

    testJavaScriptModules() {
        const requiredModules = [
            'database',
            'UIUtils',
            'DateUtils',
            'NumberUtils',
            'ValidationUtils',
            'MembersManager'
        ];

        const missingModules = requiredModules.filter(module => !window[module]);
        
        if (missingModules.length > 0) {
            throw new Error(`Missing JavaScript modules: ${missingModules.join(', ')}`);
        }

        return 'All required JavaScript modules loaded';
    }

    testEventListeners() {
        // Test if navigation works
        if (!window.navigationManager) {
            throw new Error('Navigation manager not found');
        }

        // Test if members manager is properly initialized
        if (!window.MembersManager) {
            throw new Error('Members manager not found');
        }

        return 'Event listeners and managers initialized';
    }

    displayResults() {
        console.log('\n📊 Diagnostic Results:');
        console.log('='.repeat(50));
        
        let passCount = 0;
        let failCount = 0;

        for (const [testName, result] of Object.entries(this.results)) {
            if (result.status === 'PASS') {
                console.log(`✅ ${testName}: ${result.result}`);
                passCount++;
            } else {
                console.log(`❌ ${testName}: ${result.error}`);
                failCount++;
            }
        }

        console.log('='.repeat(50));
        console.log(`📈 Summary: ${passCount} passed, ${failCount} failed`);

        if (failCount > 0) {
            console.log('\n🔧 Recommended actions:');
            this.provideTroubleshootingTips();
        }
    }

    provideTroubleshootingTips() {
        const tips = [
            '1. Check browser console for JavaScript errors',
            '2. Verify all script files are loaded correctly',
            '3. Ensure database file permissions are correct',
            '4. Try refreshing the page',
            '5. Check if Node.js modules are properly installed',
            '6. Verify Electron remote API is working'
        ];

        tips.forEach(tip => console.log(`   ${tip}`));
    }

    // Quick fix attempts
    async attemptQuickFixes() {
        console.log('🔧 Attempting quick fixes...');

        try {
            // Try to reinitialize database if needed
            if (!window.database || !window.database.db) {
                console.log('Attempting to reinitialize database...');
                if (window.database) {
                    window.database.init();
                }
            }

            // Try to reload members if manager exists
            if (window.MembersManager && window.database && window.database.db) {
                console.log('Attempting to reload members...');
                await window.MembersManager.loadMembers();
            }

            console.log('✅ Quick fixes completed');
        } catch (error) {
            console.error('❌ Quick fixes failed:', error);
        }
    }
}

// Make diagnostics available globally
window.DiagnosticsManager = DiagnosticsManager;

// Add diagnostic functions to console for easy access
window.runDiagnostics = async () => {
    const diagnostics = new DiagnosticsManager();
    return await diagnostics.runDiagnostics();
};

window.quickFix = async () => {
    const diagnostics = new DiagnosticsManager();
    return await diagnostics.attemptQuickFixes();
};

// Auto-run diagnostics in development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    setTimeout(() => {
        console.log('🔍 Auto-running diagnostics in development mode...');
        window.runDiagnostics();
    }, 3000);
}
