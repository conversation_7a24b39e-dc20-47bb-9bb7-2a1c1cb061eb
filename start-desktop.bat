@echo off
title نظام إدارة الديوان - Desktop App

echo ========================================
echo    نظام إدارة الديوان - سطح المكتب
echo ========================================
echo.

REM Check if Node.js is installed
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo اختر النسخة LTS (الموصى بها)
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات
    echo.
)

REM Create database directory
if not exist "database" mkdir database

echo 🚀 تشغيل تطبيق نظام إدارة الديوان...
echo.

REM Start the application
npm start

echo.
echo 👋 تم إغلاق التطبيق
pause
