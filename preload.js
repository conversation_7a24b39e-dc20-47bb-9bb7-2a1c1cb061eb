// Preload script for Electron security
// This script runs in the renderer process before the web content begins loading

const { contextBridge, ipcRenderer } = require('electron');
const path = require('path');
const fs = require('fs');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File system operations
  readFile: (filePath) => {
    try {
      return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
      console.error('Error reading file:', error);
      return null;
    }
  },

  writeFile: (filePath, data) => {
    try {
      fs.writeFileSync(filePath, data, 'utf8');
      return true;
    } catch (error) {
      console.error('Error writing file:', error);
      return false;
    }
  },

  fileExists: (filePath) => {
    try {
      return fs.existsSync(filePath);
    } catch (error) {
      return false;
    }
  },

  // Path operations
  joinPath: (...paths) => path.join(...paths),
  dirname: (filePath) => path.dirname(filePath),
  basename: (filePath) => path.basename(filePath),

  // App information
  getAppPath: () => process.resourcesPath || process.cwd(),
  getUserDataPath: () => {
    const { app } = require('electron').remote || require('@electron/remote');
    return app.getPath('userData');
  },

  // Dialog operations
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),

  // Menu event listeners
  onMenuAction: (callback) => {
    ipcRenderer.on('menu-backup', callback);
    ipcRenderer.on('menu-import', callback);
    ipcRenderer.on('menu-export', callback);
    ipcRenderer.on('menu-settings', callback);
    ipcRenderer.on('menu-new-member', callback);
    ipcRenderer.on('menu-members-list', callback);
    ipcRenderer.on('menu-new-payment', callback);
    ipcRenderer.on('menu-new-expense', callback);
    ipcRenderer.on('menu-financial-reports', callback);
    ipcRenderer.on('menu-monthly-report', callback);
    ipcRenderer.on('menu-annual-report', callback);
    ipcRenderer.on('menu-overdue-report', callback);
  },

  // Remove menu event listeners
  removeMenuListeners: () => {
    ipcRenderer.removeAllListeners('menu-backup');
    ipcRenderer.removeAllListeners('menu-import');
    ipcRenderer.removeAllListeners('menu-export');
    ipcRenderer.removeAllListeners('menu-settings');
    ipcRenderer.removeAllListeners('menu-new-member');
    ipcRenderer.removeAllListeners('menu-members-list');
    ipcRenderer.removeAllListeners('menu-new-payment');
    ipcRenderer.removeAllListeners('menu-new-expense');
    ipcRenderer.removeAllListeners('menu-financial-reports');
    ipcRenderer.removeAllListeners('menu-monthly-report');
    ipcRenderer.removeAllListeners('menu-annual-report');
    ipcRenderer.removeAllListeners('menu-overdue-report');
  },

  // Print operations
  print: () => {
    window.print();
  },

  // Platform information
  platform: process.platform,
  isWindows: process.platform === 'win32',
  isMac: process.platform === 'darwin',
  isLinux: process.platform === 'linux',

  // Environment
  isDev: process.env.NODE_ENV === 'development' || process.argv.includes('--dev'),

  // Version information
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  }
});

// Expose database path helper
contextBridge.exposeInMainWorld('databasePath', {
  getPath: () => {
    const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
    if (isDev) {
      // In development, use local database
      return path.join(process.cwd(), 'database', 'dewan.db');
    } else {
      // In production, use app data directory
      const { app } = require('electron').remote || require('@electron/remote');
      const userDataPath = app.getPath('userData');
      return path.join(userDataPath, 'database', 'dewan.db');
    }
  },

  getDatabaseDir: () => {
    const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
    if (isDev) {
      return path.join(process.cwd(), 'database');
    } else {
      const { app } = require('electron').remote || require('@electron/remote');
      const userDataPath = app.getPath('userData');
      return path.join(userDataPath, 'database');
    }
  },

  ensureDatabaseDir: () => {
    const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
    let dbDir;
    
    if (isDev) {
      dbDir = path.join(process.cwd(), 'database');
    } else {
      const { app } = require('electron').remote || require('@electron/remote');
      const userDataPath = app.getPath('userData');
      dbDir = path.join(userDataPath, 'database');
    }

    try {
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }
      return dbDir;
    } catch (error) {
      console.error('Error creating database directory:', error);
      return null;
    }
  }
});

// Security: Remove node integration from window object
delete window.require;
delete window.exports;
delete window.module;

// Log preload completion
console.log('🔒 Preload script loaded successfully');
console.log('🖥️ Platform:', process.platform);
console.log('⚡ Electron version:', process.versions.electron);
console.log('🌐 Chrome version:', process.versions.chrome);
console.log('📦 Node version:', process.versions.node);
