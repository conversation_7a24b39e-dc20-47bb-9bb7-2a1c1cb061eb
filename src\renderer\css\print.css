/* Print Styles - أنماط الطباعة */

/* General Print Settings */
@media print {
    /* Hide unnecessary elements */
    .no-print,
    .btn,
    .navbar,
    .sidebar,
    .breadcrumb,
    .pagination,
    .modal,
    .tooltip,
    .popover,
    .alert-dismissible .btn-close,
    .form-control,
    .form-select,
    .input-group-text,
    .card-header .btn,
    .table-actions,
    .action-buttons {
        display: none !important;
    }
    
    /* Page setup */
    @page {
        size: A4;
        margin: 1.5cm 1cm 1cm 1cm;
        
        /* Header with logo */
        @top-center {
            content: element(page-header);
        }
        
        /* Footer with page numbers */
        @bottom-right {
            content: "صفحة " counter(page) " من " counter(pages);
            font-family: 'Cairo', sans-serif;
            font-size: 10pt;
            color: #666;
        }
        
        @bottom-left {
            content: "تاريخ الطباعة: " attr(data-print-date);
            font-family: 'Cairo', sans-serif;
            font-size: 10pt;
            color: #666;
        }
    }
    
    /* Body styles */
    body {
        font-family: 'Cairo', sans-serif !important;
        font-size: 12pt;
        line-height: 1.4;
        color: #000 !important;
        background: white !important;
        direction: rtl;
        text-align: right;
    }
    
    /* Print header */
    .print-header {
        position: running(page-header);
        display: block !important;
        text-align: center;
        border-bottom: 2px solid #0d6efd;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .print-logo {
        max-height: 60px;
        max-width: 200px;
        margin-bottom: 10px;
    }
    
    .print-title {
        font-size: 18pt;
        font-weight: bold;
        color: #0d6efd;
        margin: 0;
    }
    
    .print-subtitle {
        font-size: 12pt;
        color: #666;
        margin: 5px 0 0 0;
    }
    
    /* Content styles */
    .print-content {
        margin-top: 20px;
    }
    
    /* Cards */
    .card {
        border: 1px solid #ddd !important;
        border-radius: 5px !important;
        margin-bottom: 15px !important;
        page-break-inside: avoid;
        background: white !important;
    }
    
    .card-header {
        background: #f8f9fa !important;
        border-bottom: 1px solid #ddd !important;
        padding: 10px 15px !important;
        font-weight: bold;
        color: #000 !important;
    }
    
    .card-body {
        padding: 15px !important;
        color: #000 !important;
    }
    
    /* Tables */
    .table {
        border-collapse: collapse !important;
        width: 100% !important;
        margin-bottom: 15px !important;
        font-size: 11pt !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #ddd !important;
        padding: 8px !important;
        text-align: right !important;
        vertical-align: top !important;
        color: #000 !important;
    }
    
    .table th {
        background: #f8f9fa !important;
        font-weight: bold !important;
        color: #000 !important;
    }
    
    .table-striped tbody tr:nth-of-type(odd) {
        background: #f9f9f9 !important;
    }
    
    /* Statistics cards */
    .stats-card {
        border: 1px solid #ddd !important;
        border-radius: 5px !important;
        padding: 15px !important;
        margin-bottom: 10px !important;
        text-align: center !important;
        background: white !important;
        page-break-inside: avoid;
    }
    
    .stats-card h3,
    .stats-card h4 {
        color: #000 !important;
        margin-bottom: 5px !important;
    }
    
    .stats-card p {
        color: #666 !important;
        margin: 0 !important;
    }
    
    /* Progress bars */
    .progress {
        background: #e9ecef !important;
        border: 1px solid #ddd !important;
        height: 15px !important;
    }
    
    .progress-bar {
        background: #6c757d !important;
        color: white !important;
        text-align: center !important;
        line-height: 15px !important;
    }
    
    /* Badges */
    .badge {
        border: 1px solid #ddd !important;
        background: white !important;
        color: #000 !important;
        padding: 3px 6px !important;
        border-radius: 3px !important;
    }
    
    /* Text colors for print */
    .text-success { color: #198754 !important; }
    .text-danger { color: #dc3545 !important; }
    .text-warning { color: #fd7e14 !important; }
    .text-info { color: #0dcaf0 !important; }
    .text-primary { color: #0d6efd !important; }
    .text-muted { color: #6c757d !important; }
    
    /* Page breaks */
    .page-break {
        page-break-before: always;
    }
    
    .page-break-inside-avoid {
        page-break-inside: avoid;
    }
    
    /* Charts - hide or show message */
    canvas {
        display: none !important;
    }
    
    .chart-print-message {
        display: block !important;
        text-align: center;
        padding: 20px;
        border: 1px dashed #ddd;
        color: #666;
        font-style: italic;
    }
    
    /* Print info section */
    .print-info {
        margin-top: 30px;
        padding-top: 15px;
        border-top: 1px solid #ddd;
        font-size: 10pt;
        color: #666;
        text-align: center;
    }
    
    /* Hide specific elements */
    .btn-group,
    .dropdown,
    .nav-tabs,
    .nav-pills,
    .navbar-nav,
    .offcanvas,
    .toast,
    .spinner-border,
    .spinner-grow {
        display: none !important;
    }
    
    /* Ensure proper spacing */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        margin-top: 15px !important;
        margin-bottom: 10px !important;
        color: #000 !important;
    }
    
    p, div {
        orphans: 3;
        widows: 3;
    }
    
    /* Force black text */
    * {
        color-adjust: exact !important;
        -webkit-print-color-adjust: exact !important;
    }
    
    /* Specific adjustments for Arabic text */
    .arabic-text {
        font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif !important;
        direction: rtl !important;
        text-align: right !important;
    }
    
    /* Print-only elements */
    .print-only {
        display: block !important;
    }
    
    .screen-only {
        display: none !important;
    }
}

/* Screen styles for print preview */
.print-preview {
    background: white;
    padding: 20px;
    margin: 20px auto;
    max-width: 210mm;
    min-height: 297mm;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
}

.print-preview .print-header {
    display: block;
    text-align: center;
    border-bottom: 2px solid #0d6efd;
    padding-bottom: 15px;
    margin-bottom: 25px;
}

.print-preview .print-logo {
    max-height: 80px;
    max-width: 250px;
    margin-bottom: 15px;
}

.print-preview .print-title {
    font-size: 24px;
    font-weight: bold;
    color: #0d6efd;
    margin: 0;
}

.print-preview .print-subtitle {
    font-size: 16px;
    color: #666;
    margin: 10px 0 0 0;
}

/* Print button styles */
.print-controls {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1050;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid #ddd;
}

.print-controls .btn {
    margin: 5px;
    min-width: 120px;
}

/* Logo upload area */
.logo-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logo-upload-area:hover {
    border-color: #0d6efd;
    background: #e7f3ff;
}

.logo-upload-area.dragover {
    border-color: #0d6efd;
    background: #e7f3ff;
}

.current-logo {
    max-width: 200px;
    max-height: 100px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    background: white;
}
