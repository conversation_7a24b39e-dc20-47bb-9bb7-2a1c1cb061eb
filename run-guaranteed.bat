@echo off
title نظام إدارة الديوان - مضمون 100%

echo ========================================
echo    نظام إدارة الديوان - مضمون 100%%
echo ========================================
echo.
echo ✅ قاعدة بيانات مدمجة في الصفحة
echo ✅ لا توجد ملفات خارجية
echo ✅ يعمل بدون أي مشاكل
echo ✅ بيانات تجريبية جاهزة
echo.

REM Clean processes
taskkill /f /im electron.exe 2>nul
taskkill /f /im node.exe 2>nul

REM Check Node.js
echo 🔍 فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js متاح
echo.

REM Install if needed
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات...
    npm install
)

if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    npm install electron --save-dev
)

echo.
echo 🚀 تشغيل النظام المضمون...
echo.
echo 📊 مميزات هذا الإصدار:
echo - قاعدة بيانات مدمجة مباشرة في HTML
echo - لا توجد ملفات منفصلة لقاعدة البيانات
echo - يعمل فور التشغيل بدون انتظار
echo - بيانات تجريبية: عضو + دفعة + مصروف
echo - حفظ تلقائي في localStorage
echo.

npm start

if %errorlevel% neq 0 (
    echo ❌ فشل npm start، جاري المحاولة مع electron...
    npx electron .
)

echo.
echo 👋 تم إغلاق النظام
echo 💾 البيانات محفوظة تلقائياً
pause
