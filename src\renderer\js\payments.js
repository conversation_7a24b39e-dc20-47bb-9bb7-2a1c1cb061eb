// Payment management module

class PaymentsManager {
    constructor() {
        this.payments = [];
        this.filteredPayments = [];
        this.members = [];
        this.revenueCategories = [];
        this.currentPage = 1;
        this.itemsPerPage = 15;
        this.searchTerm = '';
        this.memberFilter = 'all';
        this.categoryFilter = 'all';
        this.dateFromFilter = '';
        this.dateToFilter = '';
        this.sortBy = 'payment_date';
        this.sortOrder = 'desc';
        
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('payment-search');
        if (searchInput) {
            searchInput.addEventListener('input', UIUtils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.filterPayments();
            }, 300));
        }

        // Filters
        const memberFilter = document.getElementById('member-filter');
        if (memberFilter) {
            memberFilter.addEventListener('change', (e) => {
                this.memberFilter = e.target.value;
                this.filterPayments();
            });
        }

        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.categoryFilter = e.target.value;
                this.filterPayments();
            });
        }

        const dateFromFilter = document.getElementById('date-from-filter');
        if (dateFromFilter) {
            dateFromFilter.addEventListener('change', (e) => {
                this.dateFromFilter = e.target.value;
                this.filterPayments();
            });
        }

        const dateToFilter = document.getElementById('date-to-filter');
        if (dateToFilter) {
            dateToFilter.addEventListener('change', (e) => {
                this.dateToFilter = e.target.value;
                this.filterPayments();
            });
        }

        // Sort functionality
        const sortSelect = document.getElementById('sort-payments');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                this.sortBy = sortBy;
                this.sortOrder = sortOrder;
                this.sortPayments();
            });
        }

        // Items per page
        const itemsPerPageSelect = document.getElementById('payments-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', (e) => {
                this.itemsPerPage = parseInt(e.target.value);
                this.currentPage = 1;
                this.renderPayments();
            });
        }
    }

    async loadPayments() {
        try {
            UIUtils.showLoading();

            // Wait for database module to be loaded
            await this.waitForDatabaseModule();

            // Check if database is available
            if (!window.database || !window.database.db) {
                throw new Error('قاعدة البيانات غير متاحة');
            }

            // Load payments page content if not already loaded
            await this.loadPaymentsPageContent();

            // Fetch data from database with error handling
            try {
                await Promise.all([
                    this.fetchPayments(),
                    this.fetchMembers(),
                    this.fetchRevenueCategories()
                ]);
            } catch (dbError) {
                console.error('Database fetch error:', dbError);
                // Try individual fetches to identify the problem
                try {
                    await this.fetchPayments();
                    console.log('✅ Payments fetched successfully');
                } catch (e) {
                    console.error('❌ Error fetching payments:', e);
                    this.payments = [];
                }

                try {
                    await this.fetchMembers();
                    console.log('✅ Members fetched successfully');
                } catch (e) {
                    console.error('❌ Error fetching members:', e);
                    this.members = [];
                }

                try {
                    await this.fetchRevenueCategories();
                    console.log('✅ Revenue categories fetched successfully');
                } catch (e) {
                    console.error('❌ Error fetching revenue categories:', e);
                    this.revenueCategories = [];
                }
            }

            this.filteredPayments = [...this.payments];
            this.renderPayments();
            this.updatePaymentStats();
            this.populateFilters();

        } catch (error) {
            console.error('Error loading payments:', error);
            UIUtils.showNotification(`خطأ في تحميل بيانات المدفوعات: ${error.message}`, 'danger');

            // Show empty state
            this.payments = [];
            this.filteredPayments = [];
            this.members = [];
            this.revenueCategories = [];
            this.renderPayments();
            this.updatePaymentStats();
        } finally {
            UIUtils.hideLoading();
        }
    }

    async waitForDatabaseModule() {
        console.log('🔍 Waiting for database module (Payments)...');

        // If already loaded, return immediately
        if (window.database && window.databaseModuleLoaded) {
            console.log('✅ Database module already loaded (Payments)');
            return;
        }

        // Wait for database module to load
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Database module loading timeout (Payments)'));
            }, 10000); // 10 second timeout

            const checkDatabase = () => {
                if (window.database && window.databaseModuleLoaded) {
                    clearTimeout(timeout);
                    console.log('✅ Database module loaded successfully (Payments)');
                    resolve();
                } else {
                    setTimeout(checkDatabase, 100);
                }
            };

            // Also listen for the database module loaded event
            window.addEventListener('database-module-loaded', () => {
                clearTimeout(timeout);
                console.log('✅ Database module loaded via event (Payments)');
                resolve();
            }, { once: true });

            checkDatabase();
        });
    }

    async fetchPayments() {
        this.payments = await database.all(`
            SELECT p.*, 
                   m.full_name as member_name,
                   m.membership_id,
                   rc.name as category_name
            FROM payments p
            JOIN members m ON p.member_id = m.id
            JOIN revenue_categories rc ON p.category_id = rc.id
            ORDER BY p.payment_date DESC, p.created_at DESC
        `);
    }

    async fetchMembers() {
        this.members = await database.all(`
            SELECT id, full_name, membership_id, status
            FROM members
            WHERE status = 'active'
            ORDER BY full_name
        `);
    }

    async fetchRevenueCategories() {
        this.revenueCategories = await database.all(`
            SELECT id, name, description
            FROM revenue_categories
            WHERE is_active = 1
            ORDER BY name
        `);
    }

    async loadPaymentsPageContent() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        const pageElement = document.getElementById('payments-page');
        if (!pageElement) {
            console.error('Payments page element not found, available elements:',
                Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            throw new Error('عنصر صفحة المدفوعات غير موجود في HTML');
        }

        if (pageElement.innerHTML.trim()) {
            console.log('Payments page content already loaded');
            return;
        }

        pageElement.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="bi bi-credit-card"></i>
                            إدارة المدفوعات
                        </h2>
                        <button class="btn btn-primary" onclick="window.PaymentsManager.showAddPaymentModal()">
                            <i class="bi bi-plus-circle"></i>
                            تسجيل دفعة جديدة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-revenue">0 ر.س</h4>
                                    <p class="mb-0">إجمالي الإيرادات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-cash-coin fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="monthly-revenue">0 ر.س</h4>
                                    <p class="mb-0">إيرادات الشهر</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-calendar-month fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-payments-count">0</h4>
                                    <p class="mb-0">عدد المدفوعات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-receipt fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="avg-payment">0 ر.س</h4>
                                    <p class="mb-0">متوسط الدفعة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-graph-up fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="payment-search" class="form-label">البحث</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="payment-search" 
                                       placeholder="البحث بالعضو أو رقم المرجع...">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="member-filter" class="form-label">العضو</label>
                            <select class="form-select" id="member-filter">
                                <option value="all">جميع الأعضاء</option>
                                <!-- Members will be populated here -->
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="category-filter" class="form-label">نوع الإيراد</label>
                            <select class="form-select" id="category-filter">
                                <option value="all">جميع الأنواع</option>
                                <!-- Categories will be populated here -->
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="date-from-filter" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date-from-filter">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="date-to-filter" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date-to-filter">
                        </div>
                        
                        <div class="col-md-1 d-flex align-items-end">
                            <button class="btn btn-outline-secondary" onclick="window.PaymentsManager.clearFilters()" title="مسح الفلاتر">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <label for="sort-payments" class="form-label">ترتيب حسب</label>
                            <select class="form-select" id="sort-payments">
                                <option value="payment_date-desc">التاريخ (الأحدث)</option>
                                <option value="payment_date-asc">التاريخ (الأقدم)</option>
                                <option value="amount-desc">المبلغ (الأعلى)</option>
                                <option value="amount-asc">المبلغ (الأقل)</option>
                                <option value="member_name-asc">اسم العضو (أ-ي)</option>
                                <option value="member_name-desc">اسم العضو (ي-أ)</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="payments-per-page" class="form-label">عدد العناصر</label>
                            <select class="form-select" id="payments-per-page">
                                <option value="15">15</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        
                        <div class="col-md-7 d-flex align-items-end justify-content-end">
                            <button class="btn btn-outline-success me-2" onclick="window.PaymentsManager.exportPayments()">
                                <i class="bi bi-download"></i>
                                تصدير
                            </button>
                            <button class="btn btn-outline-info" onclick="window.PaymentsManager.printPayments()">
                                <i class="bi bi-printer"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payments Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>العضو</th>
                                    <th>المبلغ</th>
                                    <th>نوع الإيراد</th>
                                    <th>طريقة الدفع</th>
                                    <th>رقم المرجع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="payments-table-body">
                                <!-- Payments will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center" id="payments-pagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        `;

        // Set up event listeners after content is loaded
        setTimeout(() => this.setupEventListeners(), 100);
    }

    populateFilters() {
        // Populate member filter
        const memberFilter = document.getElementById('member-filter');
        if (memberFilter) {
            memberFilter.innerHTML = '<option value="all">جميع الأعضاء</option>';
            this.members.forEach(member => {
                memberFilter.innerHTML += `
                    <option value="${member.id}">${member.full_name} (${member.membership_id})</option>
                `;
            });
        }

        // Populate category filter
        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.innerHTML = '<option value="all">جميع الأنواع</option>';
            this.revenueCategories.forEach(category => {
                categoryFilter.innerHTML += `
                    <option value="${category.id}">${category.name}</option>
                `;
            });
        }
    }

    filterPayments() {
        this.filteredPayments = this.payments.filter(payment => {
            const matchesSearch = !this.searchTerm ||
                payment.member_name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                payment.membership_id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                (payment.reference_number && payment.reference_number.toLowerCase().includes(this.searchTerm.toLowerCase()));

            const matchesMember = this.memberFilter === 'all' || payment.member_id == this.memberFilter;
            const matchesCategory = this.categoryFilter === 'all' || payment.category_id == this.categoryFilter;

            const matchesDateFrom = !this.dateFromFilter || payment.payment_date >= this.dateFromFilter;
            const matchesDateTo = !this.dateToFilter || payment.payment_date <= this.dateToFilter;

            return matchesSearch && matchesMember && matchesCategory && matchesDateFrom && matchesDateTo;
        });

        this.currentPage = 1;
        this.renderPayments();
    }

    sortPayments() {
        this.filteredPayments.sort((a, b) => {
            let aValue = a[this.sortBy];
            let bValue = b[this.sortBy];

            // Handle different data types
            if (this.sortBy.includes('date')) {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            } else if (this.sortBy === 'amount') {
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            } else if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        this.renderPayments();
    }

    renderPayments() {
        const tbody = document.getElementById('payments-table-body');
        if (!tbody) return;

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedPayments = this.filteredPayments.slice(startIndex, endIndex);

        if (paginatedPayments.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="bi bi-credit-card fs-1 d-block mb-2"></i>
                        لا توجد مدفوعات مطابقة للبحث
                    </td>
                </tr>
            `;
        } else {
            tbody.innerHTML = paginatedPayments.map(payment => `
                <tr>
                    <td>${DateUtils.formatDate(payment.payment_date)}</td>
                    <td>
                        <strong>${payment.member_name}</strong><br>
                        <small class="text-muted">${payment.membership_id}</small>
                    </td>
                    <td><strong class="text-success">${NumberUtils.formatCurrency(payment.amount)}</strong></td>
                    <td>${payment.category_name}</td>
                    <td>${this.getPaymentMethodText(payment.payment_method)}</td>
                    <td>${payment.reference_number || '-'}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.PaymentsManager.viewPayment(${payment.id})" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.PaymentsManager.editPayment(${payment.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="window.PaymentsManager.deletePayment(${payment.id})" title="حذف">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        this.renderPagination();
    }

    renderPagination() {
        const pagination = document.getElementById('payments-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredPayments.length / this.itemsPerPage);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.PaymentsManager.goToPage(${this.currentPage - 1})">السابق</a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="window.PaymentsManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.PaymentsManager.goToPage(${this.currentPage + 1})">التالي</a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredPayments.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderPayments();
        }
    }

    updatePaymentStats() {
        const totalRevenue = this.payments.reduce((sum, p) => sum + parseFloat(p.amount), 0);
        const totalCount = this.payments.length;
        const avgPayment = totalCount > 0 ? totalRevenue / totalCount : 0;

        // Monthly revenue
        const currentMonth = moment().format('YYYY-MM');
        const monthlyRevenue = this.payments
            .filter(p => moment(p.payment_date).format('YYYY-MM') === currentMonth)
            .reduce((sum, p) => sum + parseFloat(p.amount), 0);

        document.getElementById('total-revenue').textContent = NumberUtils.formatCurrency(totalRevenue);
        document.getElementById('monthly-revenue').textContent = NumberUtils.formatCurrency(monthlyRevenue);
        document.getElementById('total-payments-count').textContent = NumberUtils.formatNumber(totalCount);
        document.getElementById('avg-payment').textContent = NumberUtils.formatCurrency(avgPayment);
    }

    getPaymentMethodText(method) {
        const methods = {
            'cash': 'نقداً',
            'bank_transfer': 'تحويل بنكي',
            'check': 'شيك',
            'card': 'بطاقة'
        };
        return methods[method] || method;
    }

    clearFilters() {
        document.getElementById('payment-search').value = '';
        document.getElementById('member-filter').value = 'all';
        document.getElementById('category-filter').value = 'all';
        document.getElementById('date-from-filter').value = '';
        document.getElementById('date-to-filter').value = '';

        this.searchTerm = '';
        this.memberFilter = 'all';
        this.categoryFilter = 'all';
        this.dateFromFilter = '';
        this.dateToFilter = '';

        this.filterPayments();
    }

    // Payment CRUD operations
    showAddPaymentModal(preselectedMemberId = null) {
        const modal = this.createPaymentModal('add', null, preselectedMemberId);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    async viewPayment(paymentId) {
        try {
            const payment = await database.get(`
                SELECT p.*,
                       m.full_name as member_name,
                       m.membership_id,
                       m.phone,
                       rc.name as category_name
                FROM payments p
                JOIN members m ON p.member_id = m.id
                JOIN revenue_categories rc ON p.category_id = rc.id
                WHERE p.id = ?
            `, [paymentId]);

            if (!payment) {
                UIUtils.showNotification('المدفوعة غير موجودة', 'warning');
                return;
            }

            this.showPaymentDetailsModal(payment);
        } catch (error) {
            console.error('Error viewing payment:', error);
            UIUtils.showNotification('خطأ في عرض تفاصيل المدفوعة', 'danger');
        }
    }

    async editPayment(paymentId) {
        try {
            const payment = await database.get('SELECT * FROM payments WHERE id = ?', [paymentId]);
            if (!payment) {
                UIUtils.showNotification('المدفوعة غير موجودة', 'warning');
                return;
            }

            const modal = this.createPaymentModal('edit', payment);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        } catch (error) {
            console.error('Error editing payment:', error);
            UIUtils.showNotification('خطأ في تحميل بيانات المدفوعة للتعديل', 'danger');
        }
    }

    async deletePayment(paymentId) {
        try {
            const payment = await database.get(`
                SELECT p.amount, m.full_name as member_name
                FROM payments p
                JOIN members m ON p.member_id = m.id
                WHERE p.id = ?
            `, [paymentId]);

            if (!payment) {
                UIUtils.showNotification('المدفوعة غير موجودة', 'warning');
                return;
            }

            const confirmed = await UIUtils.showConfirmDialog(
                'تأكيد الحذف',
                `هل أنت متأكد من حذف المدفوعة؟\nالعضو: ${payment.member_name}\nالمبلغ: ${NumberUtils.formatCurrency(payment.amount)}`,
                'حذف',
                'إلغاء'
            );

            if (confirmed) {
                await database.run('DELETE FROM payments WHERE id = ?', [paymentId]);
                UIUtils.showNotification('تم حذف المدفوعة بنجاح', 'success');
                await this.loadPayments();
            }
        } catch (error) {
            console.error('Error deleting payment:', error);
            UIUtils.showNotification('خطأ في حذف المدفوعة', 'danger');
        }
    }

    createPaymentModal(mode, payment = null, preselectedMemberId = null) {
        const isEdit = mode === 'edit';
        const modalId = `payment-modal-${Date.now()}`;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-credit-card-${isEdit ? '2-front' : 'plus'}"></i>
                            ${isEdit ? 'تعديل المدفوعة' : 'تسجيل دفعة جديدة'}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="payment-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment-member" class="form-label">العضو *</label>
                                        <select class="form-select" id="payment-member" required>
                                            <option value="">اختر العضو...</option>
                                            ${this.members.map(member => `
                                                <option value="${member.id}"
                                                    ${(payment?.member_id == member.id || preselectedMemberId == member.id) ? 'selected' : ''}>
                                                    ${member.full_name} (${member.membership_id})
                                                </option>
                                            `).join('')}
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment-category" class="form-label">نوع الإيراد *</label>
                                        <select class="form-select" id="payment-category" required>
                                            <option value="">اختر نوع الإيراد...</option>
                                            ${this.revenueCategories.map(category => `
                                                <option value="${category.id}"
                                                    ${payment?.category_id == category.id ? 'selected' : ''}>
                                                    ${category.name}
                                                </option>
                                            `).join('')}
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment-amount" class="form-label">المبلغ *</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="payment-amount"
                                                   value="${payment?.amount || ''}"
                                                   step="0.01" min="0" required>
                                            <span class="input-group-text">ر.س</span>
                                        </div>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment-date" class="form-label">تاريخ الدفع *</label>
                                        <input type="date" class="form-control" id="payment-date"
                                               value="${payment?.payment_date || moment().format('YYYY-MM-DD')}" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment-method" class="form-label">طريقة الدفع</label>
                                        <select class="form-select" id="payment-method">
                                            <option value="cash" ${payment?.payment_method === 'cash' ? 'selected' : ''}>نقداً</option>
                                            <option value="bank_transfer" ${payment?.payment_method === 'bank_transfer' ? 'selected' : ''}>تحويل بنكي</option>
                                            <option value="check" ${payment?.payment_method === 'check' ? 'selected' : ''}>شيك</option>
                                            <option value="card" ${payment?.payment_method === 'card' ? 'selected' : ''}>بطاقة</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment-reference" class="form-label">رقم المرجع</label>
                                        <input type="text" class="form-control" id="payment-reference"
                                               value="${payment?.reference_number || ''}"
                                               placeholder="رقم الشيك، رقم التحويل، إلخ...">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="payment-notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="payment-notes" rows="3"
                                          placeholder="أي ملاحظات إضافية...">${payment?.notes || ''}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="window.PaymentsManager.savePayment('${mode}', ${payment?.id || 'null'}, '${modalId}')">
                            <i class="bi bi-check-lg"></i>
                            ${isEdit ? 'حفظ التعديلات' : 'تسجيل الدفعة'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });

        return modal;
    }

    async savePayment(mode, paymentId, modalId) {
        try {
            // Get form values
            const paymentData = {
                member_id: document.getElementById('payment-member').value,
                category_id: document.getElementById('payment-category').value,
                amount: document.getElementById('payment-amount').value,
                payment_date: document.getElementById('payment-date').value,
                payment_method: document.getElementById('payment-method').value,
                reference_number: document.getElementById('payment-reference').value.trim(),
                notes: document.getElementById('payment-notes').value.trim()
            };

            // Validate form
            const validation = this.validatePaymentForm(paymentData);
            if (!validation.isValid) {
                this.showFormErrors(validation.errors);
                return;
            }

            UIUtils.showLoading();

            if (mode === 'add') {
                await database.run(`
                    INSERT INTO payments (member_id, category_id, amount, payment_date, payment_method, reference_number, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                `, [
                    paymentData.member_id,
                    paymentData.category_id,
                    paymentData.amount,
                    paymentData.payment_date,
                    paymentData.payment_method,
                    paymentData.reference_number || null,
                    paymentData.notes || null
                ]);

                UIUtils.showNotification('تم تسجيل الدفعة بنجاح', 'success');
            } else {
                await database.run(`
                    UPDATE payments
                    SET member_id = ?, category_id = ?, amount = ?, payment_date = ?,
                        payment_method = ?, reference_number = ?, notes = ?
                    WHERE id = ?
                `, [
                    paymentData.member_id,
                    paymentData.category_id,
                    paymentData.amount,
                    paymentData.payment_date,
                    paymentData.payment_method,
                    paymentData.reference_number || null,
                    paymentData.notes || null,
                    paymentId
                ]);

                UIUtils.showNotification('تم تحديث المدفوعة بنجاح', 'success');
            }

            // Close modal and refresh data
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            modal.hide();

            await this.loadPayments();

        } catch (error) {
            console.error('Error saving payment:', error);
            UIUtils.showNotification('خطأ في حفظ المدفوعة', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    validatePaymentForm(data) {
        const errors = {};
        let isValid = true;

        if (!data.member_id) {
            errors.member_id = 'يجب اختيار العضو';
            isValid = false;
        }

        if (!data.category_id) {
            errors.category_id = 'يجب اختيار نوع الإيراد';
            isValid = false;
        }

        if (!data.amount || parseFloat(data.amount) <= 0) {
            errors.amount = 'المبلغ يجب أن يكون أكبر من صفر';
            isValid = false;
        }

        if (!data.payment_date) {
            errors.payment_date = 'تاريخ الدفع مطلوب';
            isValid = false;
        }

        return { isValid, errors };
    }

    showFormErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

        // Show new errors
        Object.keys(errors).forEach(field => {
            const input = document.getElementById(`payment-${field.replace('_', '-')}`);
            if (input) {
                input.classList.add('is-invalid');
                const feedback = input.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = errors[field];
                }
            }
        });
    }

    exportPayments() {
        const exportData = this.filteredPayments.map(payment => ({
            'التاريخ': DateUtils.formatDate(payment.payment_date),
            'العضو': payment.member_name,
            'رقم العضوية': payment.membership_id,
            'المبلغ': payment.amount,
            'نوع الإيراد': payment.category_name,
            'طريقة الدفع': this.getPaymentMethodText(payment.payment_method),
            'رقم المرجع': payment.reference_number || '',
            'ملاحظات': payment.notes || ''
        }));

        UIUtils.exportToCSV(exportData, `payments-${moment().format('YYYY-MM-DD')}.csv`);
    }

    showPaymentDetailsModal(payment) {
        const modalId = `payment-details-modal-${Date.now()}`;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-credit-card-2-front"></i>
                            تفاصيل المدفوعة
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="bi bi-info-circle"></i>
                                            معلومات المدفوعة
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>المبلغ:</strong></td>
                                                <td><span class="h4 text-success">${NumberUtils.formatCurrency(payment.amount)}</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ الدفع:</strong></td>
                                                <td>${DateUtils.formatDate(payment.payment_date)}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>نوع الإيراد:</strong></td>
                                                <td>${payment.category_name}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>طريقة الدفع:</strong></td>
                                                <td>${this.getPaymentMethodText(payment.payment_method)}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>رقم المرجع:</strong></td>
                                                <td>${payment.reference_number || '-'}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>ملاحظات:</strong></td>
                                                <td>${payment.notes || '-'}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="bi bi-person-badge"></i>
                                            معلومات العضو
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>الاسم:</strong></td>
                                                <td>${payment.member_name}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>رقم العضوية:</strong></td>
                                                <td>${payment.membership_id}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>رقم الهاتف:</strong></td>
                                                <td>${payment.phone || '-'}</td>
                                            </tr>
                                        </table>

                                        <div class="mt-3">
                                            <button class="btn btn-sm btn-outline-primary" onclick="window.MembersManager?.viewMember(${payment.member_id}); bootstrap.Modal.getInstance(document.getElementById('${modalId}')).hide();">
                                                <i class="bi bi-eye"></i>
                                                عرض ملف العضو
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="window.PaymentsManager.editPayment(${payment.id}); bootstrap.Modal.getInstance(document.getElementById('${modalId}')).hide();">
                            <i class="bi bi-pencil"></i>
                            تعديل
                        </button>
                        <button type="button" class="btn btn-info" onclick="window.PaymentsManager.printPaymentReceipt(${payment.id})">
                            <i class="bi bi-printer"></i>
                            طباعة إيصال
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    async printPaymentReceipt(paymentId) {
        try {
            const payment = await database.get(`
                SELECT p.*,
                       m.full_name as member_name,
                       m.membership_id,
                       m.phone,
                       rc.name as category_name
                FROM payments p
                JOIN members m ON p.member_id = m.id
                JOIN revenue_categories rc ON p.category_id = rc.id
                WHERE p.id = ?
            `, [paymentId]);

            if (!payment) {
                UIUtils.showNotification('المدفوعة غير موجودة', 'warning');
                return;
            }

            const receiptContent = `
                <div class="text-center mb-4">
                    <h2>إيصال دفع</h2>
                    <p>نظام إدارة الديوان</p>
                </div>

                <div class="row mb-4">
                    <div class="col-6">
                        <strong>معلومات العضو:</strong><br>
                        الاسم: ${payment.member_name}<br>
                        رقم العضوية: ${payment.membership_id}<br>
                        الهاتف: ${payment.phone || '-'}
                    </div>
                    <div class="col-6">
                        <strong>معلومات المدفوعة:</strong><br>
                        التاريخ: ${DateUtils.formatDate(payment.payment_date)}<br>
                        رقم المرجع: ${payment.reference_number || '-'}<br>
                        طريقة الدفع: ${this.getPaymentMethodText(payment.payment_method)}
                    </div>
                </div>

                <div class="text-center border p-4 mb-4">
                    <h3>المبلغ المدفوع</h3>
                    <h1 class="text-success">${NumberUtils.formatCurrency(payment.amount)}</h1>
                    <p><strong>نوع الإيراد:</strong> ${payment.category_name}</p>
                </div>

                ${payment.notes ? `
                    <div class="mb-4">
                        <strong>ملاحظات:</strong><br>
                        ${payment.notes}
                    </div>
                ` : ''}

                <div class="text-center mt-5">
                    <p>شكراً لكم</p>
                    <small class="text-muted">تم إنشاء هذا الإيصال في ${DateUtils.getCurrentDateArabic()}</small>
                </div>
            `;

            // Create a temporary element for printing
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = receiptContent;
            tempDiv.id = 'payment-receipt-print';
            document.body.appendChild(tempDiv);

            // Print
            UIUtils.printContent('payment-receipt-print', `إيصال دفع - ${payment.member_name}`);

            // Clean up
            setTimeout(() => {
                document.body.removeChild(tempDiv);
            }, 1000);

        } catch (error) {
            console.error('Error printing payment receipt:', error);
            UIUtils.showNotification('خطأ في طباعة الإيصال', 'danger');
        }
    }

    printPayments() {
        const printContent = `
            <div class="text-center mb-4">
                <h2>تقرير المدفوعات</h2>
                <p>من ${this.dateFromFilter || 'البداية'} إلى ${this.dateToFilter || 'النهاية'}</p>
                <p>إجمالي المدفوعات: ${this.filteredPayments.length}</p>
                <p>إجمالي المبلغ: ${NumberUtils.formatCurrency(this.filteredPayments.reduce((sum, p) => sum + parseFloat(p.amount), 0))}</p>
            </div>

            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>العضو</th>
                        <th>المبلغ</th>
                        <th>نوع الإيراد</th>
                        <th>طريقة الدفع</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.filteredPayments.map(payment => `
                        <tr>
                            <td>${DateUtils.formatDate(payment.payment_date)}</td>
                            <td>${payment.member_name}</td>
                            <td>${NumberUtils.formatCurrency(payment.amount)}</td>
                            <td>${payment.category_name}</td>
                            <td>${this.getPaymentMethodText(payment.payment_method)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        // Create a temporary element for printing
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = printContent;
        tempDiv.id = 'payments-print';
        document.body.appendChild(tempDiv);

        // Print
        UIUtils.printContent('payments-print', 'تقرير المدفوعات');

        // Clean up
        setTimeout(() => {
            document.body.removeChild(tempDiv);
        }, 1000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.PaymentsManager = new PaymentsManager();
        console.log('PaymentsManager initialized successfully');

        // Try to load payments if database is ready
        setTimeout(() => {
            if (window.database && window.database.isReady) {
                console.log('Database is ready, loading payments...');
                window.PaymentsManager.loadPayments().catch(error => {
                    console.log('Initial payments load failed, will retry when database is ready');
                });
            }
        }, 1000);

    } catch (error) {
        console.error('Error initializing PaymentsManager:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة نظام إدارة المدفوعات', 'danger');
        }
    }
});

// Add database ready listener
window.addEventListener('database-ready', () => {
    console.log('Database ready, PaymentsManager can now load data');
    if (window.PaymentsManager) {
        setTimeout(() => {
            window.PaymentsManager.loadPayments().catch(error => {
                console.error('Error loading payments after database ready:', error);
            });
        }, 500);
    }
});
