// Fix reports loading issues

console.log('🔧 Reports Fix Script');
console.log('='.repeat(50));

// Function to check reports module status
function checkReportsStatus() {
    console.log('🔍 Checking reports module status...');
    
    const status = {
        reportsManager: !!window.ReportsManager,
        database: !!window.database,
        databaseReady: window.database && window.database.isReady,
        reportsPage: !!document.getElementById('reports-page'),
        chartJS: typeof Chart !== 'undefined',
        momentJS: typeof moment !== 'undefined'
    };
    
    console.log('Reports Status:', status);
    
    let allReady = true;
    for (const [key, value] of Object.entries(status)) {
        if (!value) {
            console.error(`❌ ${key} not ready`);
            allReady = false;
        } else {
            console.log(`✅ ${key} ready`);
        }
    }
    
    return { allReady, status };
}

// Function to test reports database queries
async function testReportsQueries() {
    console.log('🔍 Testing reports database queries...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Test financial summary query
        const financialSummary = await window.database.get(`
            SELECT 
                COALESCE(SUM(CASE WHEN p.amount > 0 THEN p.amount ELSE 0 END), 0) as total_revenue,
                COALESCE(SUM(CASE WHEN e.amount > 0 THEN e.amount ELSE 0 END), 0) as total_expenses
            FROM (SELECT amount FROM payments WHERE payment_date >= date('now', '-30 days')) p
            FULL OUTER JOIN (SELECT amount FROM expenses WHERE expense_date >= date('now', '-30 days')) e ON 1=1
        `);
        console.log('✅ Financial summary query:', financialSummary);
        
        // Test revenue data query
        const revenueData = await window.database.all(`
            SELECT DATE(payment_date) as date, SUM(amount) as total
            FROM payments 
            WHERE payment_date >= date('now', '-30 days')
            GROUP BY DATE(payment_date)
            ORDER BY date DESC
            LIMIT 10
        `);
        console.log(`✅ Revenue data query: ${revenueData.length} records`);
        
        // Test expense data query
        const expenseData = await window.database.all(`
            SELECT DATE(expense_date) as date, SUM(amount) as total
            FROM expenses 
            WHERE expense_date >= date('now', '-30 days')
            GROUP BY DATE(expense_date)
            ORDER BY date DESC
            LIMIT 10
        `);
        console.log(`✅ Expense data query: ${expenseData.length} records`);
        
        // Test overdue members query
        const overdueMembers = await window.database.all(`
            SELECT m.*, 
                   COALESCE(SUM(p.amount), 0) as total_paid,
                   COUNT(p.id) as payment_count
            FROM members m
            LEFT JOIN payments p ON m.id = p.member_id
            WHERE m.status = 'active'
            GROUP BY m.id
            LIMIT 5
        `);
        console.log(`✅ Overdue members query: ${overdueMembers.length} records`);
        
        return true;
    } catch (error) {
        console.error('❌ Reports queries test failed:', error);
        return false;
    }
}

// Function to test Chart.js functionality
function testChartJS() {
    console.log('🔍 Testing Chart.js functionality...');
    
    try {
        if (typeof Chart === 'undefined') {
            throw new Error('Chart.js not loaded');
        }
        
        // Test creating a simple chart
        const testCanvas = document.createElement('canvas');
        testCanvas.id = 'test-chart';
        testCanvas.width = 100;
        testCanvas.height = 100;
        document.body.appendChild(testCanvas);
        
        const testChart = new Chart(testCanvas, {
            type: 'line',
            data: {
                labels: ['Test'],
                datasets: [{
                    label: 'Test Data',
                    data: [1],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // Clean up
        testChart.destroy();
        document.body.removeChild(testCanvas);
        
        console.log('✅ Chart.js functionality test passed');
        return true;
    } catch (error) {
        console.error('❌ Chart.js test failed:', error);
        return false;
    }
}

// Function to fix reports loading
async function fixReportsLoading() {
    console.log('🚀 Starting reports loading fix...');
    
    try {
        // Step 1: Check current status
        const { allReady, status } = checkReportsStatus();
        
        if (!status.database) {
            throw new Error('Database module not loaded');
        }
        
        if (!status.databaseReady) {
            console.log('⏳ Waiting for database to be ready...');
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Database ready timeout'));
                }, 10000);
                
                const checkReady = () => {
                    if (window.database && window.database.isReady) {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                
                checkReady();
            });
        }
        
        // Step 2: Test database queries
        const queriesOk = await testReportsQueries();
        if (!queriesOk) {
            console.warn('⚠️ Some database queries failed, but continuing...');
        }
        
        // Step 3: Test Chart.js
        if (status.chartJS) {
            const chartOk = testChartJS();
            if (!chartOk) {
                console.warn('⚠️ Chart.js test failed, charts may not work');
            }
        } else {
            console.warn('⚠️ Chart.js not available, charts will be disabled');
        }
        
        // Step 4: Check ReportsManager
        if (!window.ReportsManager) {
            console.log('🔧 ReportsManager not found, creating...');
            // Try to recreate ReportsManager
            if (typeof ReportsManager !== 'undefined') {
                window.ReportsManager = new ReportsManager();
                console.log('✅ ReportsManager recreated');
            } else {
                throw new Error('ReportsManager class not available');
            }
        }
        
        // Step 5: Try to load reports
        console.log('🔄 Attempting to load reports...');
        await window.ReportsManager.loadReports();
        console.log('✅ Reports loaded successfully');
        
        return true;
        
    } catch (error) {
        console.error('❌ Reports loading fix failed:', error);
        return false;
    }
}

// Function to reload reports after fix
async function reloadReportsAfterFix() {
    console.log('🔄 Reloading reports data...');
    
    try {
        if (window.ReportsManager && typeof window.ReportsManager.loadReports === 'function') {
            await window.ReportsManager.loadReports();
            console.log('✅ Reports data reloaded successfully');
        } else {
            console.log('⚠️ ReportsManager not available');
        }
    } catch (error) {
        console.error('❌ Error reloading reports:', error);
    }
}

// Function to create sample data for reports
async function createSampleReportsData() {
    console.log('🔧 Creating sample data for reports...');
    
    try {
        // Ensure we have members
        const membersCount = await window.database.get('SELECT COUNT(*) as count FROM members');
        if (membersCount.count === 0) {
            await window.database.run(`
                INSERT INTO members (membership_id, full_name, phone, join_date, status)
                VALUES (?, ?, ?, ?, ?)
            `, ['001', 'عضو تجريبي', '0501234567', new Date().toISOString().split('T')[0], 'active']);
            console.log('✅ Sample member created for reports');
        }
        
        // Create sample payments and expenses if needed
        if (typeof window.createSamplePaymentData === 'function') {
            await window.createSamplePaymentData();
        }
        
        if (typeof window.createSampleExpenseData === 'function') {
            await window.createSampleExpenseData();
        }
        
        console.log('✅ Sample reports data created');
        return true;
    } catch (error) {
        console.error('❌ Error creating sample reports data:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.checkReportsStatus = checkReportsStatus;
    window.testReportsQueries = testReportsQueries;
    window.testChartJS = testChartJS;
    window.fixReportsLoading = fixReportsLoading;
    window.reloadReportsAfterFix = reloadReportsAfterFix;
    window.createSampleReportsData = createSampleReportsData;
    
    console.log('🎯 Reports fix functions available:');
    console.log('- checkReportsStatus()');
    console.log('- testReportsQueries()');
    console.log('- testChartJS()');
    console.log('- fixReportsLoading()');
    console.log('- reloadReportsAfterFix()');
    console.log('- createSampleReportsData()');
}

// Auto-run fix if reports are not working
setTimeout(() => {
    if (typeof window !== 'undefined') {
        const { allReady } = checkReportsStatus();
        if (!allReady) {
            console.log('🔧 Auto-running reports loading fix...');
            fixReportsLoading().then((success) => {
                if (success) {
                    reloadReportsAfterFix();
                }
            });
        }
    }
}, 6000); // Wait longer for reports module
