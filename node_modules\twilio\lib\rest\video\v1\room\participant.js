"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipantPage = exports.ParticipantListInstance = exports.ParticipantInstance = exports.ParticipantContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const anonymize_1 = require("./participant/anonymize");
const publishedTrack_1 = require("./participant/publishedTrack");
const subscribeRules_1 = require("./participant/subscribeRules");
const subscribedTrack_1 = require("./participant/subscribedTrack");
class ParticipantContextImpl {
    constructor(_version, roomSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(roomSid)) {
            throw new Error("Parameter 'roomSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { roomSid, sid };
        this._uri = `/Rooms/${roomSid}/Participants/${sid}`;
    }
    get anonymize() {
        this._anonymize =
            this._anonymize ||
                (0, anonymize_1.AnonymizeListInstance)(this._version, this._solution.roomSid, this._solution.sid);
        return this._anonymize;
    }
    get publishedTracks() {
        this._publishedTracks =
            this._publishedTracks ||
                (0, publishedTrack_1.PublishedTrackListInstance)(this._version, this._solution.roomSid, this._solution.sid);
        return this._publishedTracks;
    }
    get subscribeRules() {
        this._subscribeRules =
            this._subscribeRules ||
                (0, subscribeRules_1.SubscribeRulesListInstance)(this._version, this._solution.roomSid, this._solution.sid);
        return this._subscribeRules;
    }
    get subscribedTracks() {
        this._subscribedTracks =
            this._subscribedTracks ||
                (0, subscribedTrack_1.SubscribedTrackListInstance)(this._version, this._solution.roomSid, this._solution.sid);
        return this._subscribedTracks;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.roomSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.roomSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantContextImpl = ParticipantContextImpl;
class ParticipantInstance {
    constructor(_version, payload, roomSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.roomSid = payload.room_sid;
        this.accountSid = payload.account_sid;
        this.status = payload.status;
        this.identity = payload.identity;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.startTime = deserialize.iso8601DateTime(payload.start_time);
        this.endTime = deserialize.iso8601DateTime(payload.end_time);
        this.duration = deserialize.integer(payload.duration);
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { roomSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ParticipantContextImpl(this._version, this._solution.roomSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a ParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ParticipantInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the anonymize.
     */
    anonymize() {
        return this._proxy.anonymize;
    }
    /**
     * Access the publishedTracks.
     */
    publishedTracks() {
        return this._proxy.publishedTracks;
    }
    /**
     * Access the subscribeRules.
     */
    subscribeRules() {
        return this._proxy.subscribeRules;
    }
    /**
     * Access the subscribedTracks.
     */
    subscribedTracks() {
        return this._proxy.subscribedTracks;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            roomSid: this.roomSid,
            accountSid: this.accountSid,
            status: this.status,
            identity: this.identity,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            startTime: this.startTime,
            endTime: this.endTime,
            duration: this.duration,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantInstance = ParticipantInstance;
function ParticipantListInstance(version, roomSid) {
    if (!(0, utility_1.isValidPathParam)(roomSid)) {
        throw new Error("Parameter 'roomSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new ParticipantContextImpl(version, roomSid, sid);
    };
    instance._version = version;
    instance._solution = { roomSid };
    instance._uri = `/Rooms/${roomSid}/Participants`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["identity"] !== undefined)
            data["Identity"] = params["identity"];
        if (params["dateCreatedAfter"] !== undefined)
            data["DateCreatedAfter"] = serialize.iso8601DateTime(params["dateCreatedAfter"]);
        if (params["dateCreatedBefore"] !== undefined)
            data["DateCreatedBefore"] = serialize.iso8601DateTime(params["dateCreatedBefore"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ParticipantPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ParticipantListInstance = ParticipantListInstance;
class ParticipantPage extends Page_1.default {
    /**
     * Initialize the ParticipantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ParticipantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ParticipantInstance(this._version, payload, this._solution.roomSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantPage = ParticipantPage;
