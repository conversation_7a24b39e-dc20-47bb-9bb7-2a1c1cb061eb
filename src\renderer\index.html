<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الديوان الأردني</title>
    
    <!-- Bootstrap CSS -->
    <link href="../../node_modules/bootstrap/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="../../node_modules/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"
            onerror="console.log('Chart.js CDN failed, using fallback')"></script>
    <!-- Chart.js Fallback -->
    <script src="js/chart-fallback.js"></script>
    <!-- Chart.js Download Helper -->
    <script src="../../download-chartjs.js"></script>
    <!-- Reports Data Tester -->
    <script src="../../test-reports-data.js"></script>
    <!-- Currency Display Fix -->
    <script src="../../fix-currency-display.js"></script>
    <!-- Print Styles -->
    <link rel="stylesheet" href="css/print.css">
    <!-- Print Manager -->
    <script src="js/print-manager.js"></script>

    <!-- Custom CSS -->
    <link href="css/styles.css" rel="stylesheet">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building"></i>
                نظام إدارة الديوان الأردني
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-page="dashboard">
                            <i class="bi bi-speedometer2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="members">
                            <i class="bi bi-people"></i>
                            الأعضاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="payments">
                            <i class="bi bi-credit-card"></i>
                            المدفوعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="expenses">
                            <i class="bi bi-receipt"></i>
                            المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="reports">
                            <i class="bi bi-graph-up"></i>
                            التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="whatsapp">
                            <i class="bi bi-whatsapp"></i>
                            واتساب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="settings">
                            <i class="bi bi-gear"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
                
                <div class="navbar-text">
                    <span id="current-user">المستخدم: مدير النظام</span>
                    <span class="mx-2">|</span>
                    <span id="current-date"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-3">
        <div id="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="bi bi-speedometer2"></i>
                            لوحة التحكم
                        </h2>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="total-members">0</h4>
                                        <p class="mb-0">إجمالي الأعضاء</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="active-members">0</h4>
                                        <p class="mb-0">الأعضاء النشطون</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-person-check fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="monthly-revenue">0.000 د.أ</h4>
                                        <p class="mb-0">إيرادات الشهر</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-cash-coin fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="overdue-amount">0.000 د.أ</h4>
                                        <p class="mb-0">المتأخرات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts and Recent Activities -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">الإيرادات والمصروفات - آخر 6 أشهر</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="financial-chart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">النشاطات الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                <div id="recent-activities" class="list-group list-group-flush">
                                    <!-- Recent activities will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other pages will be loaded dynamically -->
            <div id="members-page" class="page" style="display: none;"></div>
            <div id="payments-page" class="page" style="display: none;"></div>
            <div id="expenses-page" class="page" style="display: none;"></div>
            <div id="reports-page" class="page" style="display: none;"></div>
            <div id="whatsapp-page" class="page" style="display: none;"></div>
            <div id="settings-page" class="page" style="display: none;"></div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../../node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="../../node_modules/chart.js/dist/chart.min.js"></script>
    
    <!-- Moment.js for date handling -->
    <script src="../../node_modules/moment/moment.js"></script>
    <script src="../../node_modules/moment/locale/ar.js"></script>
    
    <!-- Main Application Scripts -->
    <script>
        // Check if we're in Electron environment
        console.log('🚀 Loading Dewan Management System...');
        console.log('Environment check:', {
            electron: typeof require !== 'undefined',
            node: typeof process !== 'undefined',
            window: typeof window !== 'undefined'
        });

        // Initialize loading flags
        window.databaseModuleLoaded = false;
        window.modulesLoaded = {
            database: false,
            utils: false,
            navigation: false,
            members: false
        };
    </script>

    <!-- Load database module first -->
    <script>
        // Inline Database - مضمونة 100%
        console.log('🔄 Loading Inline Database...');

        window.database = {
            isReady: true,
            error: null,
            data: {
                members: [
                    {
                        id: 1,
                        membership_id: 'M001',
                        full_name: 'أحمد محمد علي',
                        phone: '00962791234567',
                        email: '<EMAIL>',
                        address: 'عمان، الأردن',
                        join_date: '2024-01-01',
                        status: 'active',
                        notes: 'عضو مؤسس',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    }
                ],
                payments: [
                    {
                        id: 1,
                        member_id: 1,
                        category_id: 1,
                        amount: 25.000,
                        payment_date: '2024-01-01',
                        payment_method: 'cash',
                        reference_number: 'PAY001',
                        notes: 'دفعة تجريبية',
                        created_at: new Date().toISOString()
                    }
                ],
                expenses: [
                    {
                        id: 1,
                        category_id: 1,
                        amount: 10.000,
                        expense_date: '2024-01-01',
                        description: 'مصروف تجريبي',
                        receipt_path: '',
                        notes: 'مصروف للاختبار',
                        created_at: new Date().toISOString()
                    }
                ],
                revenue_categories: [
                    { id: 1, name: 'اشتراك شهري', description: 'الاشتراك الشهري للأعضاء', is_active: true },
                    { id: 2, name: 'تبرعات', description: 'التبرعات المختلفة', is_active: true },
                    { id: 3, name: 'دعم الفعاليات', description: 'دعم الفعاليات والأنشطة', is_active: true },
                    { id: 4, name: 'رسوم خدمات', description: 'رسوم الخدمات المختلفة', is_active: true }
                ],
                expense_categories: [
                    { id: 1, name: 'فعاليات', description: 'مصروفات الفعاليات والأنشطة', is_active: true },
                    { id: 2, name: 'خدمات', description: 'مصروفات الخدمات المقدمة', is_active: true },
                    { id: 3, name: 'دعم الحالات', description: 'دعم الحالات المحتاجة', is_active: true },
                    { id: 4, name: 'فواتير', description: 'الفواتير والمصروفات الإدارية', is_active: true },
                    { id: 5, name: 'صيانة', description: 'مصروفات الصيانة والتطوير', is_active: true }
                ],
                settings: [
                    { key: 'currency', value: 'د.أ', description: 'العملة المستخدمة' },
                    { key: 'organization_name', value: 'الديوان', description: 'اسم المنظمة' },
                    { key: 'monthly_subscription', value: '25.000', description: 'قيمة الاشتراك الشهري' }
                ]
            },
            nextId: { members: 2, payments: 2, expenses: 2 },

            // Load from localStorage
            loadData: function() {
                try {
                    const stored = localStorage.getItem('dewan_inline_db');
                    if (stored) {
                        const parsed = JSON.parse(stored);
                        this.data = { ...this.data, ...parsed };
                        console.log('📊 Data loaded from localStorage');
                    }
                } catch (e) {
                    console.log('📊 Using default data');
                }
            },

            // Save to localStorage
            saveData: function() {
                try {
                    localStorage.setItem('dewan_inline_db', JSON.stringify(this.data));
                    console.log('💾 Data saved to localStorage');
                } catch (e) {
                    console.error('Error saving data:', e);
                }
            },

            // Database methods
            async run(sql, params = []) {
                console.log('🔧 DB Run:', sql.substring(0, 30) + '...', params);

                try {
                    if (sql.toLowerCase().includes('insert into members')) {
                        const member = {
                            id: this.nextId.members++,
                            membership_id: params[0] || `M${String(this.nextId.members).padStart(3, '0')}`,
                            full_name: params[1] || '',
                            phone: params[2] || '',
                            email: params[3] || '',
                            address: params[4] || '',
                            join_date: params[5] || new Date().toISOString().split('T')[0],
                            status: params[6] || 'active',
                            notes: params[7] || '',
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString()
                        };
                        this.data.members.push(member);
                        this.saveData();
                        return { id: member.id, changes: 1 };
                    }

                    if (sql.toLowerCase().includes('insert into payments')) {
                        const payment = {
                            id: this.nextId.payments++,
                            member_id: parseInt(params[0]),
                            category_id: parseInt(params[1]),
                            amount: parseFloat(params[2]),
                            payment_date: params[3] || new Date().toISOString().split('T')[0],
                            payment_method: params[4] || 'cash',
                            reference_number: params[5] || '',
                            notes: params[6] || '',
                            created_at: new Date().toISOString()
                        };
                        this.data.payments.push(payment);
                        this.saveData();
                        return { id: payment.id, changes: 1 };
                    }

                    if (sql.toLowerCase().includes('insert into expenses')) {
                        const expense = {
                            id: this.nextId.expenses++,
                            category_id: parseInt(params[0]),
                            amount: parseFloat(params[1]),
                            expense_date: params[2] || new Date().toISOString().split('T')[0],
                            description: params[3] || '',
                            receipt_path: params[4] || '',
                            notes: params[5] || '',
                            created_at: new Date().toISOString()
                        };
                        this.data.expenses.push(expense);
                        this.saveData();
                        return { id: expense.id, changes: 1 };
                    }

                    return { id: 0, changes: 0 };
                } catch (error) {
                    console.error('Error in run:', error);
                    return { id: 0, changes: 0 };
                }
            },

            async get(sql, params = []) {
                console.log('🔍 DB Get:', sql.substring(0, 30) + '...', params);
                const results = await this.all(sql, params);
                return results.length > 0 ? results[0] : null;
            },

            async all(sql, params = []) {
                console.log('📋 DB All:', sql.substring(0, 30) + '...', params);

                try {
                    const sqlLower = sql.toLowerCase();

                    if (sqlLower.includes('from members')) {
                        let members = this.data.members.map(member => ({
                            ...member,
                            total_paid: this.data.payments
                                .filter(p => p.member_id === member.id)
                                .reduce((sum, p) => sum + parseFloat(p.amount || 0), 0),
                            payment_count: this.data.payments.filter(p => p.member_id === member.id).length,
                            last_payment_date: this.data.payments
                                .filter(p => p.member_id === member.id)
                                .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date))[0]?.payment_date || null
                        }));

                        if (sqlLower.includes('where') && params.length > 0) {
                            if (sqlLower.includes('id =')) {
                                members = members.filter(m => m.id == params[0]);
                            }
                        }

                        return members;
                    }

                    if (sqlLower.includes('from payments')) {
                        let payments = this.data.payments.map(payment => {
                            const member = this.data.members.find(m => m.id === payment.member_id);
                            const category = this.data.revenue_categories.find(c => c.id === payment.category_id);
                            return {
                                ...payment,
                                member_name: member ? member.full_name : 'غير معروف',
                                member_phone: member ? member.phone : '',
                                category_name: category ? category.name : 'غير محدد'
                            };
                        });

                        if (sqlLower.includes('where') && params.length > 0) {
                            if (sqlLower.includes('member_id =')) {
                                payments = payments.filter(p => p.member_id == params[0]);
                            }
                        }

                        return payments;
                    }

                    if (sqlLower.includes('from expenses')) {
                        return this.data.expenses.map(expense => {
                            const category = this.data.expense_categories.find(c => c.id === expense.category_id);
                            return {
                                ...expense,
                                category_name: category ? category.name : 'غير محدد'
                            };
                        });
                    }

                    if (sqlLower.includes('from revenue_categories')) {
                        return this.data.revenue_categories.filter(c => c.is_active);
                    }

                    if (sqlLower.includes('from expense_categories')) {
                        return this.data.expense_categories.filter(c => c.is_active);
                    }

                    if (sqlLower.includes('from settings')) {
                        return this.data.settings;
                    }

                    if (sqlLower.includes('sqlite_master')) {
                        return [{ count: 6 }];
                    }

                    return [];
                } catch (error) {
                    console.error('Error in all:', error);
                    return [];
                }
            },

            async close() {
                this.saveData();
                console.log('🔒 Database closed');
            },

            async backup(path) {
                console.log('💾 Backup created');
                return path;
            }
        };

        // Load data immediately
        window.database.loadData();

        // Mark as loaded
        window.databaseModuleLoaded = true;

        console.log('✅ Inline Database loaded successfully!');
        console.log('📊 Data:', {
            members: window.database.data.members.length,
            payments: window.database.data.payments.length,
            expenses: window.database.data.expenses.length
        });

        // Dispatch events immediately
        setTimeout(() => {
            window.dispatchEvent(new CustomEvent('database-ready'));
            window.dispatchEvent(new CustomEvent('database-module-loaded'));
            console.log('🎉 Database events dispatched');
        }, 10);
    </script>
    <script>
        // Verify database module loaded
        if (window.database) {
            console.log('✅ Database module loaded successfully');
            window.modulesLoaded.database = true;
        } else {
            console.error('❌ Database module failed to load');
        }
    </script>

    <!-- Load utility modules -->
    <script src="js/utils.js"></script>
    <script>
        if (window.UIUtils && window.DateUtils && window.NumberUtils) {
            console.log('✅ Utils modules loaded successfully');
            window.modulesLoaded.utils = true;
        } else {
            console.error('❌ Utils modules failed to load');
        }
    </script>
    <!-- Load navigation module -->
    <script src="js/navigation.js"></script>
    <script>
        if (window.navigationManager) {
            console.log('✅ Navigation module loaded successfully');
            window.modulesLoaded.navigation = true;
        }
    </script>

    <!-- Load dashboard module -->
    <script src="js/dashboard.js"></script>
    <script>
        if (window.DashboardManager) {
            console.log('✅ Dashboard module loaded successfully');
        }
    </script>

    <!-- Load members module -->
    <script src="js/members.js"></script>
    <script>
        if (window.MembersManager) {
            console.log('✅ Members module loaded successfully');
            window.modulesLoaded.members = true;
        } else {
            console.error('❌ Members module failed to load');
        }
    </script>
    <script src="js/payments.js"></script>
    <script src="js/expenses.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/whatsapp.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/diagnostics.js"></script>
    <script src="fix-dom-errors.js"></script>
    <script src="fix-database-issue.js"></script>
    <script src="fix-module-loading.js"></script>
    <script src="fix-payments-issue.js"></script>
    <script src="fix-expenses-issue.js"></script>
    <script src="fix-reports-issue.js"></script>
    <script src="fix-whatsapp-issue.js"></script>
    <script src="fix-settings-issue.js"></script>
    <script src="update-currency-to-jod.js"></script>
    <script src="fix-all-modules.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
