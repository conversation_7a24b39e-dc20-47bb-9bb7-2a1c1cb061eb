<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الديوان الأردني</title>
    
    <!-- Bootstrap CSS -->
    <link href="../../node_modules/bootstrap/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="../../node_modules/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/styles.css" rel="stylesheet">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building"></i>
                نظام إدارة الديوان الأردني
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-page="dashboard">
                            <i class="bi bi-speedometer2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="members">
                            <i class="bi bi-people"></i>
                            الأعضاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="payments">
                            <i class="bi bi-credit-card"></i>
                            المدفوعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="expenses">
                            <i class="bi bi-receipt"></i>
                            المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="reports">
                            <i class="bi bi-graph-up"></i>
                            التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="whatsapp">
                            <i class="bi bi-whatsapp"></i>
                            واتساب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="settings">
                            <i class="bi bi-gear"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
                
                <div class="navbar-text">
                    <span id="current-user">المستخدم: مدير النظام</span>
                    <span class="mx-2">|</span>
                    <span id="current-date"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-3">
        <div id="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="bi bi-speedometer2"></i>
                            لوحة التحكم
                        </h2>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="total-members">0</h4>
                                        <p class="mb-0">إجمالي الأعضاء</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="active-members">0</h4>
                                        <p class="mb-0">الأعضاء النشطون</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-person-check fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="monthly-revenue">0 ر.س</h4>
                                        <p class="mb-0">إيرادات الشهر</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-cash-coin fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="overdue-amount">0 ر.س</h4>
                                        <p class="mb-0">المتأخرات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts and Recent Activities -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">الإيرادات والمصروفات - آخر 6 أشهر</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="financial-chart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">النشاطات الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                <div id="recent-activities" class="list-group list-group-flush">
                                    <!-- Recent activities will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other pages will be loaded dynamically -->
            <div id="members-page" class="page" style="display: none;"></div>
            <div id="payments-page" class="page" style="display: none;"></div>
            <div id="expenses-page" class="page" style="display: none;"></div>
            <div id="reports-page" class="page" style="display: none;"></div>
            <div id="whatsapp-page" class="page" style="display: none;"></div>
            <div id="settings-page" class="page" style="display: none;"></div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../../node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="../../node_modules/chart.js/dist/chart.min.js"></script>
    
    <!-- Moment.js for date handling -->
    <script src="../../node_modules/moment/moment.js"></script>
    <script src="../../node_modules/moment/locale/ar.js"></script>
    
    <!-- Main Application Scripts -->
    <script>
        // Check if we're in Electron environment
        console.log('🚀 Loading Dewan Management System...');
        console.log('Environment check:', {
            electron: typeof require !== 'undefined',
            node: typeof process !== 'undefined',
            window: typeof window !== 'undefined'
        });

        // Initialize loading flags
        window.databaseModuleLoaded = false;
        window.modulesLoaded = {
            database: false,
            utils: false,
            navigation: false,
            members: false
        };
    </script>

    <!-- Load database module first -->
    <script src="js/database.js"></script>
    <script>
        // Verify database module loaded
        if (window.database) {
            console.log('✅ Database module loaded successfully');
            window.modulesLoaded.database = true;
        } else {
            console.error('❌ Database module failed to load');
        }
    </script>

    <!-- Load utility modules -->
    <script src="js/utils.js"></script>
    <script>
        if (window.UIUtils && window.DateUtils && window.NumberUtils) {
            console.log('✅ Utils modules loaded successfully');
            window.modulesLoaded.utils = true;
        } else {
            console.error('❌ Utils modules failed to load');
        }
    </script>
    <!-- Load navigation module -->
    <script src="js/navigation.js"></script>
    <script>
        if (window.navigationManager) {
            console.log('✅ Navigation module loaded successfully');
            window.modulesLoaded.navigation = true;
        }
    </script>

    <!-- Load dashboard module -->
    <script src="js/dashboard.js"></script>
    <script>
        if (window.DashboardManager) {
            console.log('✅ Dashboard module loaded successfully');
        }
    </script>

    <!-- Load members module -->
    <script src="js/members.js"></script>
    <script>
        if (window.MembersManager) {
            console.log('✅ Members module loaded successfully');
            window.modulesLoaded.members = true;
        } else {
            console.error('❌ Members module failed to load');
        }
    </script>
    <script src="js/payments.js"></script>
    <script src="js/expenses.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/whatsapp.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/diagnostics.js"></script>
    <script src="fix-dom-errors.js"></script>
    <script src="fix-database-issue.js"></script>
    <script src="fix-module-loading.js"></script>
    <script src="fix-payments-issue.js"></script>
    <script src="fix-expenses-issue.js"></script>
    <script src="fix-reports-issue.js"></script>
    <script src="fix-whatsapp-issue.js"></script>
    <script src="fix-settings-issue.js"></script>
    <script src="update-currency-to-jod.js"></script>
    <script src="fix-all-modules.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
