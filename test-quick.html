<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        .success {
            background: rgba(40, 167, 69, 0.8);
            border: 2px solid #28a745;
        }
        .error {
            background: rgba(220, 53, 69, 0.8);
            border: 2px solid #dc3545;
        }
        .info {
            background: rgba(23, 162, 184, 0.8);
            border: 2px solid #17a2b8;
        }
        .data-box {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .data-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .data-item:last-child {
            border-bottom: none;
        }
        button {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .loading {
            text-align: center;
            font-size: 1.2em;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار سريع</h1>
        
        <div id="status" class="loading">⏳ جاري تحميل قاعدة البيانات...</div>
        
        <div id="results" style="display: none;">
            <div class="data-box">
                <h3>📊 إحصائيات البيانات:</h3>
                <div id="stats"></div>
            </div>
            
            <div class="data-box">
                <h3>👥 الأعضاء:</h3>
                <div id="members"></div>
            </div>
            
            <div class="data-box">
                <h3>💰 المدفوعات:</h3>
                <div id="payments"></div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="runTest()">🔄 إعادة الاختبار</button>
            <button onclick="clearData()">🗑️ مسح البيانات</button>
            <button onclick="addSampleData()">➕ إضافة بيانات تجريبية</button>
        </div>
    </div>

    <script src="src/renderer/js/database-fixed.js"></script>
    <script>
        let testRunning = false;

        async function runTest() {
            if (testRunning) return;
            testRunning = true;

            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            try {
                statusDiv.innerHTML = '⏳ جاري فحص قاعدة البيانات...';
                statusDiv.className = 'status info';
                resultsDiv.style.display = 'none';

                // Wait a bit for database to load
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Check if database exists
                if (!window.database) {
                    throw new Error('قاعدة البيانات غير محملة');
                }

                // Check if database is ready
                if (!window.database.isReady) {
                    statusDiv.innerHTML = '⏳ انتظار جاهزية قاعدة البيانات...';
                    
                    // Wait for ready event
                    await new Promise((resolve, reject) => {
                        const timeout = setTimeout(() => {
                            reject(new Error('انتهت مهلة انتظار قاعدة البيانات'));
                        }, 5000);

                        window.addEventListener('database-ready', () => {
                            clearTimeout(timeout);
                            resolve();
                        }, { once: true });

                        // Check if already ready
                        if (window.database.isReady) {
                            clearTimeout(timeout);
                            resolve();
                        }
                    });
                }

                // Test database operations
                statusDiv.innerHTML = '🔍 اختبار العمليات...';

                const members = await window.database.all('SELECT * FROM members');
                const payments = await window.database.all('SELECT * FROM payments');
                const expenses = await window.database.all('SELECT * FROM expenses');
                const revenueCategories = await window.database.all('SELECT * FROM revenue_categories');

                // Display results
                statusDiv.innerHTML = '✅ قاعدة البيانات تعمل بنجاح!';
                statusDiv.className = 'status success';

                // Show stats
                document.getElementById('stats').innerHTML = `
                    <div class="data-item"><span>👥 الأعضاء:</span><span>${members.length}</span></div>
                    <div class="data-item"><span>💰 المدفوعات:</span><span>${payments.length}</span></div>
                    <div class="data-item"><span>💸 المصروفات:</span><span>${expenses.length}</span></div>
                    <div class="data-item"><span>📂 فئات الإيرادات:</span><span>${revenueCategories.length}</span></div>
                `;

                // Show members
                if (members.length > 0) {
                    document.getElementById('members').innerHTML = members.map(member => `
                        <div class="data-item">
                            <span>${member.full_name}</span>
                            <span>${member.membership_id}</span>
                        </div>
                    `).join('');
                } else {
                    document.getElementById('members').innerHTML = '<div class="data-item"><span>لا يوجد أعضاء</span></div>';
                }

                // Show payments
                if (payments.length > 0) {
                    document.getElementById('payments').innerHTML = payments.map(payment => `
                        <div class="data-item">
                            <span>${payment.amount} د.أ</span>
                            <span>${payment.payment_date}</span>
                        </div>
                    `).join('');
                } else {
                    document.getElementById('payments').innerHTML = '<div class="data-item"><span>لا يوجد مدفوعات</span></div>';
                }

                resultsDiv.style.display = 'block';

            } catch (error) {
                statusDiv.innerHTML = `❌ خطأ: ${error.message}`;
                statusDiv.className = 'status error';
                resultsDiv.style.display = 'none';
            }

            testRunning = false;
        }

        async function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('dewan_fixed_database');
                location.reload();
            }
        }

        async function addSampleData() {
            try {
                if (!window.database) {
                    alert('قاعدة البيانات غير متاحة');
                    return;
                }

                // Add sample member
                await window.database.run(
                    'INSERT INTO members (membership_id, full_name, phone, email, address, join_date, status, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                    ['M002', 'سارة أحمد محمد', '**************', '<EMAIL>', 'إربد، الأردن', '2024-01-15', 'active', 'عضو جديد']
                );

                // Add sample payment
                await window.database.run(
                    'INSERT INTO payments (member_id, category_id, amount, payment_date, payment_method, reference_number, notes) VALUES (?, ?, ?, ?, ?, ?, ?)',
                    [1, 1, 25.000, '2024-01-15', 'bank_transfer', 'PAY002', 'دفعة شهرية']
                );

                alert('تم إضافة البيانات التجريبية بنجاح!');
                runTest();

            } catch (error) {
                alert('خطأ في إضافة البيانات: ' + error.message);
            }
        }

        // Auto-run test when page loads
        window.addEventListener('load', () => {
            setTimeout(runTest, 500);
        });

        // Listen for database events
        window.addEventListener('database-ready', () => {
            console.log('🎉 Database ready event received');
        });

        window.addEventListener('database-module-loaded', () => {
            console.log('📦 Database module loaded event received');
        });
    </script>
</body>
</html>
