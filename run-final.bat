@echo off
title نظام إدارة الديوان - الإصدار النهائي

echo ========================================
echo    نظام إدارة الديوان - الإصدار النهائي
echo ========================================
echo.
echo ✅ قاعدة بيانات محسنة ومضمونة
echo ✅ بيانات تجريبية جاهزة
echo ✅ حفظ تلقائي في المتصفح
echo ✅ مقاوم للأخطاء
echo.

REM Clean up processes
echo 🧹 تنظيف العمليات السابقة...
taskkill /f /im electron.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 1 /nobreak >nul

REM Check Node.js
echo 🔍 فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo اختر النسخة LTS (الموصى بها)
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js متاح - الإصدار:
node --version
echo.

REM Check dependencies
echo 🔍 فحص المكتبات...
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات للمرة الأولى...
    echo هذا قد يستغرق بضع دقائق...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        echo.
        echo حاول الحلول التالية:
        echo 1. تأكد من اتصال الإنترنت
        echo 2. شغل كمدير: npm cache clean --force
        echo 3. أعد تشغيل هذا الملف
        echo.
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات مثبتة مسبقاً
)

REM Check Electron
if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    npm install electron --save-dev
    if %errorlevel% neq 0 (
        echo ⚠️ فشل في تثبيت Electron، لكن سنحاول المتابعة...
    )
)

echo.
echo 🚀 تشغيل نظام إدارة الديوان...
echo.
echo 📊 معلومات النظام:
echo - قاعدة البيانات: محسنة ومضمونة
echo - التخزين: localStorage (متصفح)
echo - البيانات: محفوظة تلقائياً
echo - الأعضاء: بيانات تجريبية متاحة
echo.
echo ⏳ جاري التحميل...

REM Try npm start first
npm start

REM If npm start fails, try electron directly
if %errorlevel% neq 0 (
    echo.
    echo ⚠️ فشل npm start، جاري المحاولة مع electron مباشرة...
    echo.
    
    if exist "node_modules\.bin\electron.cmd" (
        node_modules\.bin\electron.cmd .
    ) else if exist "node_modules\electron\dist\electron.exe" (
        node_modules\electron\dist\electron.exe .
    ) else (
        echo ❌ لم يتم العثور على Electron
        echo.
        echo حاول الحلول التالية:
        echo 1. شغل: npm install electron --save-dev
        echo 2. أعد تشغيل هذا الملف
        echo 3. تأكد من اتصال الإنترنت
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo 👋 تم إغلاق نظام إدارة الديوان
echo ========================================
echo.
echo 💾 البيانات محفوظة تلقائياً في المتصفح
echo 🔄 يمكنك إعادة تشغيل النظام في أي وقت
echo.
echo شكراً لاستخدام نظام إدارة الديوان!
echo.
pause
