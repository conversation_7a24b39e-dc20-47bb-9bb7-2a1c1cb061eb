// Fix all modules issues

console.log('🔧 All Modules Fix Script');
console.log('='.repeat(50));

// Function to check all modules status
function checkAllModulesStatus() {
    console.log('🔍 Checking all modules status...');
    
    const modules = {
        database: {
            loaded: !!window.database,
            ready: window.database && window.database.isReady
        },
        members: {
            loaded: !!window.MembersManager,
            ready: true
        },
        payments: {
            loaded: !!window.PaymentsManager,
            ready: true
        },
        expenses: {
            loaded: !!window.ExpensesManager,
            ready: true
        },
        dashboard: {
            loaded: !!window.DashboardManager,
            ready: true
        },
        navigation: {
            loaded: !!window.navigationManager,
            ready: true
        }
    };
    
    console.log('All Modules Status:', modules);
    
    let allReady = true;
    for (const [name, status] of Object.entries(modules)) {
        if (!status.loaded) {
            console.error(`❌ ${name} module not loaded`);
            allReady = false;
        } else if (!status.ready) {
            console.warn(`⚠️ ${name} module loaded but not ready`);
            allReady = false;
        } else {
            console.log(`✅ ${name} module ready`);
        }
    }
    
    return { allReady, modules };
}

// Function to fix all modules
async function fixAllModules() {
    console.log('🚀 Starting comprehensive modules fix...');
    
    try {
        // Step 1: Fix database module
        console.log('📋 Step 1: Fixing database module...');
        if (typeof window.fixDatabaseIssue === 'function') {
            const dbFixed = await window.fixDatabaseIssue();
            if (!dbFixed) {
                throw new Error('Database fix failed');
            }
            console.log('✅ Database module fixed');
        }
        
        // Step 2: Fix module loading
        console.log('📋 Step 2: Fixing module loading...');
        if (typeof window.fixModuleLoading === 'function') {
            const modulesFixed = await window.fixModuleLoading();
            if (!modulesFixed) {
                console.warn('⚠️ Module loading fix had issues, continuing...');
            } else {
                console.log('✅ Module loading fixed');
            }
        }
        
        // Step 3: Fix members module
        console.log('📋 Step 3: Fixing members module...');
        if (window.MembersManager && typeof window.MembersManager.loadMembers === 'function') {
            try {
                await window.MembersManager.loadMembers();
                console.log('✅ Members module working');
            } catch (error) {
                console.error('❌ Members module error:', error);
            }
        }
        
        // Step 4: Fix payments module
        console.log('📋 Step 4: Fixing payments module...');
        if (typeof window.fixPaymentsLoading === 'function') {
            const paymentsFixed = await window.fixPaymentsLoading();
            if (paymentsFixed) {
                console.log('✅ Payments module fixed');
            } else {
                console.warn('⚠️ Payments module fix had issues');
            }
        }
        
        // Step 5: Fix expenses module
        console.log('📋 Step 5: Fixing expenses module...');
        if (typeof window.fixExpensesLoading === 'function') {
            const expensesFixed = await window.fixExpensesLoading();
            if (expensesFixed) {
                console.log('✅ Expenses module fixed');
            } else {
                console.warn('⚠️ Expenses module fix had issues');
            }
        }
        
        // Step 6: Final status check
        console.log('📋 Step 6: Final status check...');
        const { allReady } = checkAllModulesStatus();
        
        if (allReady) {
            console.log('🎉 All modules fixed successfully!');
            return true;
        } else {
            console.warn('⚠️ Some modules still have issues');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Comprehensive fix failed:', error);
        return false;
    }
}

// Function to reload all data
async function reloadAllData() {
    console.log('🔄 Reloading all data...');
    
    const reloadTasks = [];
    
    // Reload members
    if (window.MembersManager && typeof window.MembersManager.loadMembers === 'function') {
        reloadTasks.push(
            window.MembersManager.loadMembers().then(() => {
                console.log('✅ Members data reloaded');
            }).catch(error => {
                console.error('❌ Error reloading members:', error);
            })
        );
    }
    
    // Reload payments
    if (window.PaymentsManager && typeof window.PaymentsManager.loadPayments === 'function') {
        reloadTasks.push(
            window.PaymentsManager.loadPayments().then(() => {
                console.log('✅ Payments data reloaded');
            }).catch(error => {
                console.error('❌ Error reloading payments:', error);
            })
        );
    }
    
    // Reload expenses
    if (window.ExpensesManager && typeof window.ExpensesManager.loadExpenses === 'function') {
        reloadTasks.push(
            window.ExpensesManager.loadExpenses().then(() => {
                console.log('✅ Expenses data reloaded');
            }).catch(error => {
                console.error('❌ Error reloading expenses:', error);
            })
        );
    }
    
    // Reload dashboard
    if (window.DashboardManager && typeof window.DashboardManager.loadDashboard === 'function') {
        reloadTasks.push(
            window.DashboardManager.loadDashboard().then(() => {
                console.log('✅ Dashboard data reloaded');
            }).catch(error => {
                console.error('❌ Error reloading dashboard:', error);
            })
        );
    }
    
    // Wait for all reloads to complete
    await Promise.allSettled(reloadTasks);
    console.log('🎉 All data reload completed');
}

// Function to create all sample data
async function createAllSampleData() {
    console.log('🔧 Creating all sample data...');
    
    try {
        // Create sample member if needed
        const membersCount = await window.database.get('SELECT COUNT(*) as count FROM members');
        if (membersCount.count === 0) {
            await window.database.run(`
                INSERT INTO members (membership_id, full_name, phone, join_date, status)
                VALUES (?, ?, ?, ?, ?)
            `, ['001', 'عضو تجريبي', '0501234567', new Date().toISOString().split('T')[0], 'active']);
            console.log('✅ Sample member created');
        }
        
        // Create sample categories and data
        if (typeof window.createDefaultRevenueCategories === 'function') {
            await window.createDefaultRevenueCategories();
        }
        
        if (typeof window.createDefaultExpenseCategories === 'function') {
            await window.createDefaultExpenseCategories();
        }
        
        if (typeof window.createSamplePaymentData === 'function') {
            await window.createSamplePaymentData();
        }
        
        if (typeof window.createSampleExpenseData === 'function') {
            await window.createSampleExpenseData();
        }
        
        console.log('✅ All sample data created');
        return true;
    } catch (error) {
        console.error('❌ Error creating sample data:', error);
        return false;
    }
}

// Function to run comprehensive diagnostics
async function runComprehensiveDiagnostics() {
    console.log('🔍 Running comprehensive diagnostics...');
    
    // Run all available diagnostic functions
    const diagnostics = [];
    
    if (typeof window.runDiagnostics === 'function') {
        diagnostics.push('System Diagnostics');
        window.runDiagnostics();
    }
    
    if (typeof window.checkModuleStatus === 'function') {
        diagnostics.push('Module Status');
        window.checkModuleStatus();
    }
    
    if (typeof window.checkPaymentsStatus === 'function') {
        diagnostics.push('Payments Status');
        window.checkPaymentsStatus();
    }
    
    if (typeof window.checkExpensesStatus === 'function') {
        diagnostics.push('Expenses Status');
        window.checkExpensesStatus();
    }
    
    if (typeof window.testDatabaseOperations === 'function') {
        diagnostics.push('Database Operations');
        await window.testDatabaseOperations();
    }
    
    console.log(`✅ Ran ${diagnostics.length} diagnostic tests:`, diagnostics);
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.checkAllModulesStatus = checkAllModulesStatus;
    window.fixAllModules = fixAllModules;
    window.reloadAllData = reloadAllData;
    window.createAllSampleData = createAllSampleData;
    window.runComprehensiveDiagnostics = runComprehensiveDiagnostics;
    
    console.log('🎯 Comprehensive fix functions available:');
    console.log('- checkAllModulesStatus()');
    console.log('- fixAllModules()');
    console.log('- reloadAllData()');
    console.log('- createAllSampleData()');
    console.log('- runComprehensiveDiagnostics()');
}

// Auto-run comprehensive fix if needed
setTimeout(() => {
    if (typeof window !== 'undefined') {
        const { allReady } = checkAllModulesStatus();
        if (!allReady) {
            console.log('🔧 Auto-running comprehensive modules fix...');
            fixAllModules().then((success) => {
                if (success) {
                    reloadAllData();
                }
            });
        }
    }
}, 6000); // Wait longer for all modules to load
