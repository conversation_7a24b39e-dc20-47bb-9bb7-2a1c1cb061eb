<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحقق من أرقام الهواتف الأردنية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .test-pass {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-fail {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">اختبار التحقق من أرقام الهواتف الأردنية</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار رقم هاتف</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="phone-input" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phone-input" placeholder="مثال: 0791234567 أو +962791234567">
                        </div>
                        <button class="btn btn-primary" onclick="testPhone()">اختبار</button>
                        <div id="test-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبارات تلقائية</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
                        <div id="auto-test-results" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="src/renderer/js/utils.js"></script>
    <script>
        function testPhone() {
            const phoneInput = document.getElementById('phone-input');
            const resultDiv = document.getElementById('test-result');
            const phone = phoneInput.value.trim();
            
            if (!phone) {
                resultDiv.innerHTML = '<div class="alert alert-warning">يرجى إدخال رقم هاتف</div>';
                return;
            }
            
            const validationResult = ValidationUtils.validatePhone(phone);
            const cleanedPhone = ValidationUtils.cleanPhone(phone);
            const formattedPhone = ValidationUtils.formatPhone(phone);
            
            let resultHtml = '<div class="card">';
            resultHtml += '<div class="card-body">';
            resultHtml += `<p><strong>الرقم المدخل:</strong> ${phone}</p>`;
            resultHtml += `<p><strong>الرقم المنظف:</strong> ${cleanedPhone}</p>`;
            resultHtml += `<p><strong>الرقم المنسق:</strong> ${formattedPhone}</p>`;
            
            if (validationResult) {
                resultHtml += `<div class="alert alert-danger"><strong>خطأ:</strong> ${validationResult}</div>`;
            } else {
                resultHtml += '<div class="alert alert-success"><strong>صحيح:</strong> رقم الهاتف صالح</div>';
            }
            
            resultHtml += '</div></div>';
            resultDiv.innerHTML = resultHtml;
        }
        
        function runAllTests() {
            const testCases = [
                // أرقام أردنية صحيحة (الأولوية)
                { phone: '0791234567', expected: null, description: 'رقم أردني موبايل (079)' },
                { phone: '0781234567', expected: null, description: 'رقم أردني موبايل (078)' },
                { phone: '0771234567', expected: null, description: 'رقم أردني موبايل (077)' },
                { phone: '791234567', expected: null, description: 'رقم أردني بدون 0' },
                { phone: '+962791234567', expected: null, description: 'رقم أردني مع +962' },
                { phone: '962791234567', expected: null, description: 'رقم أردني مع 962' },
                { phone: '00962791234567', expected: null, description: 'رقم أردني مع 00962' },
                { phone: '0612345678', expected: null, description: 'رقم أردني ثابت (عمان)' },
                { phone: '0212345678', expected: null, description: 'رقم أردني ثابت (إربد)' },

                // أرقام سعودية
                { phone: '0512345678', expected: null, description: 'رقم سعودي عادي' },
                { phone: '512345678', expected: null, description: 'رقم سعودي بدون 0' },
                { phone: '+966512345678', expected: null, description: 'رقم سعودي مع كود الدولة' },

                // أرقام عربية أخرى
                { phone: '+971501234567', expected: null, description: 'رقم إماراتي' },
                { phone: '+96512345678', expected: null, description: 'رقم كويتي' },
                
                // أرقام خاطئة
                { phone: '123', expected: 'رقم الهاتف قصير جداً (الحد الأدنى 8 أرقام)', description: 'رقم قصير' },
                { phone: '12345678901234567890', expected: 'رقم الهاتف طويل جداً (الحد الأقصى 15 رقم)', description: 'رقم طويل' },
                { phone: 'abc123', expected: 'رقم الهاتف يجب أن يحتوي على أرقام فقط', description: 'رقم يحتوي على حروف' },
                { phone: '', expected: 'رقم الهاتف مطلوب', description: 'رقم فارغ' },
                
                // أرقام أردنية مع تنسيق
                { phone: '************', expected: null, description: 'رقم أردني مع مسافات' },
                { phone: '************', expected: null, description: 'رقم أردني مع شرطات' },
                { phone: '(*************', expected: null, description: 'رقم أردني مع أقواس' },
                { phone: '+962 79 123 4567', expected: null, description: 'رقم أردني دولي مع مسافات' },

                // أرقام سعودية مع تنسيق
                { phone: '************', expected: null, description: 'رقم سعودي مع مسافات' },
                { phone: '************', expected: null, description: 'رقم سعودي مع شرطات' },
            ];
            
            const resultsDiv = document.getElementById('auto-test-results');
            let resultsHtml = '';
            let passCount = 0;
            let failCount = 0;
            
            testCases.forEach((testCase, index) => {
                const result = ValidationUtils.validatePhone(testCase.phone);
                const passed = result === testCase.expected;
                
                if (passed) {
                    passCount++;
                } else {
                    failCount++;
                }
                
                resultsHtml += `
                    <div class="test-result ${passed ? 'test-pass' : 'test-fail'}">
                        <strong>اختبار ${index + 1}:</strong> ${testCase.description}<br>
                        <strong>المدخل:</strong> "${testCase.phone}"<br>
                        <strong>المتوقع:</strong> ${testCase.expected || 'صحيح'}<br>
                        <strong>النتيجة:</strong> ${result || 'صحيح'}<br>
                        <strong>الحالة:</strong> ${passed ? '✅ نجح' : '❌ فشل'}
                    </div>
                `;
            });
            
            resultsHtml = `
                <div class="alert alert-info">
                    <strong>ملخص النتائج:</strong><br>
                    نجح: ${passCount} | فشل: ${failCount} | المجموع: ${testCases.length}
                </div>
            ` + resultsHtml;
            
            resultsDiv.innerHTML = resultsHtml;
        }
        
        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            console.log('تم تحميل صفحة اختبار أرقام الهواتف');
        });
    </script>
</body>
</html>
