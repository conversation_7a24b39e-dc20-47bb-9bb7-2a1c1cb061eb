// Fix database availability issues

console.log('🔧 Database Fix Script');
console.log('='.repeat(50));

// Function to check database status
function checkDatabaseStatus() {
    console.log('🔍 Checking database status...');
    
    if (typeof window === 'undefined') {
        console.log('❌ Not in browser environment');
        return false;
    }
    
    if (!window.database) {
        console.log('❌ Database module not loaded');
        return false;
    }
    
    if (!window.database.db) {
        console.log('❌ Database connection not established');
        return false;
    }
    
    if (!window.database.isReady) {
        console.log('⏳ Database not ready yet');
        return false;
    }
    
    console.log('✅ Database is available and ready');
    return true;
}

// Function to force database initialization
async function forceDatabaseInit() {
    console.log('🔧 Force initializing database...');
    
    try {
        if (window.database && typeof window.database.init === 'function') {
            await window.database.init();
            console.log('✅ Database initialization triggered');
            
            // Wait for initialization
            let attempts = 0;
            const maxAttempts = 30; // 30 seconds max
            
            while (!window.database.isReady && attempts < maxAttempts) {
                console.log(`⏳ Waiting for database... attempt ${attempts + 1}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                attempts++;
            }
            
            if (window.database.isReady) {
                console.log('✅ Database is now ready');
                return true;
            } else {
                console.log('❌ Database initialization timeout');
                return false;
            }
        } else {
            console.log('❌ Database init function not available');
            return false;
        }
    } catch (error) {
        console.error('❌ Error during database initialization:', error);
        return false;
    }
}

// Function to test database operations
async function testDatabaseOperations() {
    console.log('🧪 Testing database operations...');
    
    try {
        // Test simple query
        const result = await window.database.get('SELECT 1 as test');
        if (result && result.test === 1) {
            console.log('✅ Basic query test passed');
        } else {
            console.log('❌ Basic query test failed');
            return false;
        }
        
        // Test table existence
        const tables = await window.database.all(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
        `);
        
        console.log(`✅ Found ${tables.length} tables:`, tables.map(t => t.name));
        
        // Test members table specifically
        const membersCount = await window.database.get('SELECT COUNT(*) as count FROM members');
        console.log(`✅ Members table has ${membersCount.count} records`);
        
        return true;
    } catch (error) {
        console.error('❌ Database operation test failed:', error);
        return false;
    }
}

// Function to recreate database if needed
async function recreateDatabase() {
    console.log('🔄 Recreating database...');
    
    try {
        // Close existing connection
        if (window.database && window.database.db) {
            await window.database.close();
        }
        
        // Reinitialize
        if (window.database) {
            window.database.isReady = false;
            window.database.isInitializing = false;
            window.database.db = null;
            await window.database.init();
        }
        
        console.log('✅ Database recreated');
        return true;
    } catch (error) {
        console.error('❌ Error recreating database:', error);
        return false;
    }
}

// Main fix function
async function fixDatabaseIssue() {
    console.log('🚀 Starting database fix...');
    
    try {
        // Step 1: Check current status
        if (checkDatabaseStatus()) {
            console.log('✅ Database is already working');
            return await testDatabaseOperations();
        }
        
        // Step 2: Try to initialize
        console.log('🔧 Attempting to initialize database...');
        if (await forceDatabaseInit()) {
            if (await testDatabaseOperations()) {
                console.log('✅ Database fix successful');
                return true;
            }
        }
        
        // Step 3: Try to recreate
        console.log('🔄 Attempting to recreate database...');
        if (await recreateDatabase()) {
            // Wait a bit for initialization
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            if (await testDatabaseOperations()) {
                console.log('✅ Database recreation successful');
                return true;
            }
        }
        
        console.log('❌ All database fix attempts failed');
        return false;
        
    } catch (error) {
        console.error('❌ Database fix failed:', error);
        return false;
    }
}

// Function to reload members after fix
async function reloadMembersAfterFix() {
    console.log('🔄 Reloading members data...');
    
    try {
        if (window.MembersManager && typeof window.MembersManager.loadMembers === 'function') {
            await window.MembersManager.loadMembers();
            console.log('✅ Members data reloaded successfully');
        } else {
            console.log('⚠️ MembersManager not available');
        }
    } catch (error) {
        console.error('❌ Error reloading members:', error);
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.fixDatabaseIssue = fixDatabaseIssue;
    window.checkDatabaseStatus = checkDatabaseStatus;
    window.forceDatabaseInit = forceDatabaseInit;
    window.testDatabaseOperations = testDatabaseOperations;
    window.reloadMembersAfterFix = reloadMembersAfterFix;
    
    console.log('🎯 Database fix functions available:');
    console.log('- fixDatabaseIssue()');
    console.log('- checkDatabaseStatus()');
    console.log('- forceDatabaseInit()');
    console.log('- testDatabaseOperations()');
    console.log('- reloadMembersAfterFix()');
}

// Auto-run fix if database is not available
setTimeout(() => {
    if (typeof window !== 'undefined' && !checkDatabaseStatus()) {
        console.log('🔧 Auto-running database fix...');
        fixDatabaseIssue().then((success) => {
            if (success) {
                reloadMembersAfterFix();
            }
        });
    }
}, 2000);
