const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Handle Electron app path safely
let appPath;
try {
    const { app } = require('electron').remote || require('@electron/remote');
    appPath = app.getPath('userData');
} catch (error) {
    // Fallback for development or when remote is not available
    const os = require('os');
    appPath = path.join(os.homedir(), 'dewan-data');
}

class Database {
    constructor() {
        this.db = null;
        this.dbPath = path.join(appPath, 'dewan.db');
        this.init();
    }

    init() {
        try {
            // Ensure the directory exists
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
                console.log('Created database directory:', dbDir);
            }

            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('Error opening database:', err);
                    this.handleDatabaseError(err);
                } else {
                    console.log('Connected to SQLite database at:', this.dbPath);
                    // Enable foreign keys
                    this.db.run('PRAGMA foreign_keys = ON');
                    // Create tables
                    this.createTables();
                    // Set ready after a delay to ensure tables are created
                    setTimeout(() => {
                        this.isReady = true;
                        // Dispatch ready event
                        if (typeof window !== 'undefined') {
                            window.dispatchEvent(new CustomEvent('database-ready'));
                        }
                        console.log('Database is ready for use');
                    }, 2000);
                }
            });
        } catch (error) {
            console.error('Error initializing database:', error);
            this.handleDatabaseError(error);
        }
    }

    handleDatabaseError(error) {
        console.error('Database error:', error);
        this.isReady = false;
        if (typeof window !== 'undefined' && window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة قاعدة البيانات', 'danger');
        }
    }

    async createTables() {
        console.log('Creating database tables...');
        const tables = [
            // Members table
            `CREATE TABLE IF NOT EXISTS members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                membership_id TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                join_date DATE NOT NULL,
                status TEXT DEFAULT 'active' CHECK(status IN ('active', 'inactive')),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Revenue categories table
            `CREATE TABLE IF NOT EXISTS revenue_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Payments table
            `CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                member_id INTEGER NOT NULL,
                category_id INTEGER NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_date DATE NOT NULL,
                payment_method TEXT DEFAULT 'cash' CHECK(payment_method IN ('cash', 'bank_transfer', 'check', 'card')),
                reference_number TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (member_id) REFERENCES members (id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES revenue_categories (id)
            )`,

            // Expense categories table
            `CREATE TABLE IF NOT EXISTS expense_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Expenses table
            `CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_id INTEGER NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                expense_date DATE NOT NULL,
                description TEXT NOT NULL,
                receipt_path TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES expense_categories (id)
            )`,

            // Settings table
            `CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT NOT NULL UNIQUE,
                value TEXT,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Users table for multi-user support
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'user' CHECK(role IN ('admin', 'user', 'viewer')),
                is_active BOOLEAN DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // WhatsApp messages log
            `CREATE TABLE IF NOT EXISTS whatsapp_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                member_id INTEGER,
                phone_number TEXT NOT NULL,
                message_type TEXT NOT NULL CHECK(message_type IN ('payment_confirmation', 'reminder', 'announcement')),
                message_content TEXT NOT NULL,
                status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'sent', 'failed')),
                sent_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (member_id) REFERENCES members (id) ON DELETE SET NULL
            )`,

            // Audit log for tracking changes
            `CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                record_id INTEGER NOT NULL,
                action TEXT NOT NULL CHECK(action IN ('INSERT', 'UPDATE', 'DELETE')),
                old_values TEXT,
                new_values TEXT,
                user_id INTEGER,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`
        ];

        // Create tables sequentially to avoid conflicts
        let tablesCreated = 0;
        const createNextTable = (index) => {
            if (index >= tables.length) {
                console.log(`All ${tablesCreated} tables created successfully`);
                // Insert default data after all tables are created
                setTimeout(() => {
                    this.insertDefaultData();
                }, 500);
                return;
            }

            this.db.run(tables[index], (err) => {
                if (err) {
                    console.error(`Error creating table ${index + 1}:`, err);
                } else {
                    console.log(`Table ${index + 1} created successfully`);
                    tablesCreated++;
                }
                // Create next table regardless of error
                createNextTable(index + 1);
            });
        };

        // Start creating tables
        createNextTable(0);
    }

    insertDefaultData() {
        // Default revenue categories
        const revenueCategories = [
            ['اشتراك شهري', 'الاشتراك الشهري للأعضاء'],
            ['تبرعات', 'التبرعات المختلفة'],
            ['دعم الفعاليات', 'دعم الفعاليات والأنشطة'],
            ['رسوم خدمات', 'رسوم الخدمات المختلفة']
        ];

        revenueCategories.forEach(([name, description]) => {
            this.db.run(
                'INSERT OR IGNORE INTO revenue_categories (name, description) VALUES (?, ?)',
                [name, description]
            );
        });

        // Default expense categories
        const expenseCategories = [
            ['فعاليات', 'مصروفات الفعاليات والأنشطة'],
            ['خدمات', 'مصروفات الخدمات المقدمة'],
            ['دعم الحالات', 'دعم الحالات المحتاجة'],
            ['فواتير', 'الفواتير والمصروفات الإدارية'],
            ['صيانة', 'مصروفات الصيانة والتطوير']
        ];

        expenseCategories.forEach(([name, description]) => {
            this.db.run(
                'INSERT OR IGNORE INTO expense_categories (name, description) VALUES (?, ?)',
                [name, description]
            );
        });

        // Default settings
        const defaultSettings = [
            ['currency', 'ر.س', 'العملة المستخدمة'],
            ['organization_name', 'الديوان', 'اسم المنظمة'],
            ['monthly_subscription', '100', 'قيمة الاشتراك الشهري'],
            ['reminder_days', '5', 'عدد أيام التذكير قبل الاستحقاق'],
            ['whatsapp_api_key', '', 'مفتاح API للواتساب'],
            ['backup_frequency', 'weekly', 'تكرار النسخ الاحتياطي'],
            ['date_format', 'DD/MM/YYYY', 'تنسيق التاريخ']
        ];

        defaultSettings.forEach(([key, value, description]) => {
            this.db.run(
                'INSERT OR IGNORE INTO settings (key, value, description) VALUES (?, ?, ?)',
                [key, value, description]
            );
        });

        // Default admin user
        this.db.run(
            'INSERT OR IGNORE INTO users (username, password_hash, full_name, role) VALUES (?, ?, ?, ?)',
            ['admin', 'admin123', 'مدير النظام', 'admin']
        );
    }

    // Generic query methods with improved error handling
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('Database run error:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('Database get error:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('Database all error:', err, 'SQL:', sql, 'Params:', params);
                    reject(err);
                } else {
                    resolve(rows || []);
                }
            });
        });
    }

    close() {
        return new Promise((resolve, reject) => {
            this.db.close((err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    // Backup database
    async backup(backupPath) {
        return new Promise((resolve, reject) => {
            const readStream = fs.createReadStream(this.dbPath);
            const writeStream = fs.createWriteStream(backupPath);
            
            readStream.pipe(writeStream);
            
            writeStream.on('finish', () => {
                resolve(backupPath);
            });
            
            writeStream.on('error', (err) => {
                reject(err);
            });
        });
    }
}

// Create global database instance with error handling
let database;

try {
    console.log('Initializing database module...');
    database = new Database();
    console.log('Database module created successfully');
} catch (error) {
    console.error('Critical error creating database:', error);
    // Create a fallback database object
    database = {
        isReady: false,
        error: error.message,
        db: null,
        init: () => {
            console.error('Database failed to initialize:', error.message);
            return Promise.reject(error);
        },
        get: () => Promise.reject(new Error('Database not available')),
        all: () => Promise.reject(new Error('Database not available')),
        run: () => Promise.reject(new Error('Database not available')),
        close: () => Promise.resolve(),
        backup: () => Promise.reject(new Error('Database not available'))
    };
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = database;
} else {
    // Make sure window object exists
    if (typeof window !== 'undefined') {
        window.database = database;
        console.log('Database attached to window object');
    } else {
        console.error('Window object not available');
    }
}
