@echo off
echo ========================================
echo    نظام إدارة الديوان - سطح المكتب
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
echo.

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

echo ✅ npm متاح
echo.

REM Check if package.json exists
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    pause
    exit /b 1
)

echo ✅ ملف package.json موجود
echo.

REM Check if node_modules exists, if not install dependencies
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    echo هذا قد يستغرق بضع دقائق في المرة الأولى...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
    echo.
) else (
    echo ✅ المكتبات مثبتة مسبقاً
    echo.
)

REM Check if Electron is installed
if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    npm install electron --save-dev
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Electron
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت Electron بنجاح
    echo.
)

REM Create database directory if it doesn't exist
if not exist "database" (
    echo 📁 إنشاء مجلد قاعدة البيانات...
    mkdir database
    echo ✅ تم إنشاء مجلد قاعدة البيانات
    echo.
)

REM Check if database file exists
if not exist "database\dewan.db" (
    echo 🗄️ إنشاء قاعدة البيانات...
    if exist "create-database.js" (
        node create-database.js
        if %errorlevel% neq 0 (
            echo ⚠️ تحذير: فشل في إنشاء قاعدة البيانات
            echo سيتم إنشاؤها تلقائياً عند تشغيل التطبيق
        ) else (
            echo ✅ تم إنشاء قاعدة البيانات بنجاح
        )
    ) else (
        echo ⚠️ ملف إنشاء قاعدة البيانات غير موجود
        echo سيتم إنشاؤها تلقائياً عند تشغيل التطبيق
    )
    echo.
)

echo 🚀 تشغيل تطبيق نظام إدارة الديوان...
echo.
echo للخروج من التطبيق، أغلق النافذة أو اضغط Ctrl+C هنا
echo.

REM Start the Electron app
npm start

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo.
    echo الأخطاء المحتملة:
    echo - تأكد من وجود ملف src/main.js
    echo - تأكد من صحة ملف package.json
    echo - تأكد من تثبيت جميع المكتبات المطلوبة
    echo.
    pause
    exit /b 1
)

echo.
echo 👋 تم إغلاق التطبيق
pause
