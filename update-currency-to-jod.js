// Update currency to Jordanian Dinar

console.log('💰 Currency Update Script - Jordanian Dinar');
console.log('='.repeat(50));

// Function to update currency settings
async function updateCurrencyToJOD() {
    console.log('🔄 Updating currency to Jordanian Dinar...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Update currency setting
        await window.database.run(`
            INSERT OR REPLACE INTO settings (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        `, ['currency', 'د.أ', 'العملة المستخدمة']);
        
        console.log('✅ Currency setting updated to د.أ');
        
        // Update monthly subscription to reasonable JOD amount
        await window.database.run(`
            INSERT OR REPLACE INTO settings (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        `, ['monthly_subscription', '25', 'قيمة الاشتراك الشهري']);
        
        console.log('✅ Monthly subscription updated to 25 د.أ');
        
        return true;
    } catch (error) {
        console.error('❌ Error updating currency:', error);
        return false;
    }
}

// Function to update existing payment amounts (optional conversion)
async function convertExistingAmounts() {
    console.log('🔄 Converting existing amounts to JOD...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Get current exchange rate (approximate: 1 SAR = 0.27 JOD)
        const exchangeRate = 0.27;
        
        // Update payments amounts
        const payments = await window.database.all('SELECT id, amount FROM payments');
        console.log(`Found ${payments.length} payments to convert`);
        
        for (const payment of payments) {
            const newAmount = (parseFloat(payment.amount) * exchangeRate).toFixed(3);
            await window.database.run('UPDATE payments SET amount = ? WHERE id = ?', [newAmount, payment.id]);
        }
        
        console.log(`✅ Converted ${payments.length} payment amounts`);
        
        // Update expenses amounts
        const expenses = await window.database.all('SELECT id, amount FROM expenses');
        console.log(`Found ${expenses.length} expenses to convert`);
        
        for (const expense of expenses) {
            const newAmount = (parseFloat(expense.amount) * exchangeRate).toFixed(3);
            await window.database.run('UPDATE expenses SET amount = ? WHERE id = ?', [newAmount, expense.id]);
        }
        
        console.log(`✅ Converted ${expenses.length} expense amounts`);
        
        return true;
    } catch (error) {
        console.error('❌ Error converting amounts:', error);
        return false;
    }
}

// Function to update organization name to Jordanian context
async function updateOrganizationForJordan() {
    console.log('🏢 Updating organization settings for Jordan...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Update organization name
        await window.database.run(`
            INSERT OR REPLACE INTO settings (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        `, ['organization_name', 'ديوان المجتمع الأردني', 'اسم المنظمة']);
        
        console.log('✅ Organization name updated for Jordan');
        
        // Update date format to common Jordanian format
        await window.database.run(`
            INSERT OR REPLACE INTO settings (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        `, ['date_format', 'DD/MM/YYYY', 'تنسيق التاريخ']);
        
        console.log('✅ Date format updated');
        
        return true;
    } catch (error) {
        console.error('❌ Error updating organization settings:', error);
        return false;
    }
}

// Function to create Jordanian-specific revenue categories
async function createJordanianRevenueCategories() {
    console.log('💰 Creating Jordanian-specific revenue categories...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        const jordanianCategories = [
            { name: 'اشتراك شهري', description: 'الاشتراك الشهري للأعضاء - 25 د.أ' },
            { name: 'تبرعات', description: 'التبرعات والهبات من الأعضاء والمجتمع' },
            { name: 'دعم المشاريع', description: 'دعم المشاريع التنموية والخيرية' },
            { name: 'رسوم الخدمات', description: 'رسوم الخدمات المختلفة المقدمة' },
            { name: 'إيرادات الفعاليات', description: 'إيرادات من الفعاليات والأنشطة' }
        ];
        
        for (const category of jordanianCategories) {
            // Check if category already exists
            const existing = await window.database.get(
                'SELECT id FROM revenue_categories WHERE name = ?', 
                [category.name]
            );
            
            if (!existing) {
                await window.database.run(`
                    INSERT INTO revenue_categories (name, description, is_active)
                    VALUES (?, ?, 1)
                `, [category.name, category.description]);
                
                console.log(`✅ Created revenue category: ${category.name}`);
            }
        }
        
        return true;
    } catch (error) {
        console.error('❌ Error creating Jordanian revenue categories:', error);
        return false;
    }
}

// Function to create Jordanian-specific expense categories
async function createJordanianExpenseCategories() {
    console.log('💸 Creating Jordanian-specific expense categories...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        const jordanianExpenseCategories = [
            { name: 'المرافق العامة', description: 'فواتير الكهرباء والماء والهاتف' },
            { name: 'الصيانة والإصلاح', description: 'صيانة المقر والمعدات' },
            { name: 'المساعدات الاجتماعية', description: 'دعم الأسر المحتاجة في المجتمع' },
            { name: 'الفعاليات والأنشطة', description: 'تكاليف تنظيم الفعاليات' },
            { name: 'المصروفات الإدارية', description: 'القرطاسية والمصروفات المكتبية' },
            { name: 'النقل والمواصلات', description: 'تكاليف النقل والسفر' }
        ];
        
        for (const category of jordanianExpenseCategories) {
            // Check if category already exists
            const existing = await window.database.get(
                'SELECT id FROM expense_categories WHERE name = ?', 
                [category.name]
            );
            
            if (!existing) {
                await window.database.run(`
                    INSERT INTO expense_categories (name, description, is_active)
                    VALUES (?, ?, 1)
                `, [category.name, category.description]);
                
                console.log(`✅ Created expense category: ${category.name}`);
            }
        }
        
        return true;
    } catch (error) {
        console.error('❌ Error creating Jordanian expense categories:', error);
        return false;
    }
}

// Main function to update everything to Jordanian Dinar
async function updateToJordanianDinar(convertAmounts = false) {
    console.log('🇯🇴 Starting complete update to Jordanian Dinar...');
    
    try {
        // Wait for database to be ready
        if (!window.database || !window.database.isReady) {
            console.log('⏳ Waiting for database to be ready...');
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Database ready timeout'));
                }, 10000);
                
                const checkReady = () => {
                    if (window.database && window.database.isReady) {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                
                checkReady();
            });
        }
        
        // Step 1: Update currency settings
        const currencyUpdated = await updateCurrencyToJOD();
        if (!currencyUpdated) {
            throw new Error('Failed to update currency settings');
        }
        
        // Step 2: Update organization settings
        const orgUpdated = await updateOrganizationForJordan();
        if (!orgUpdated) {
            console.warn('⚠️ Failed to update organization settings, but continuing...');
        }
        
        // Step 3: Create Jordanian categories
        await createJordanianRevenueCategories();
        await createJordanianExpenseCategories();
        
        // Step 4: Convert existing amounts (optional)
        if (convertAmounts) {
            console.log('💱 Converting existing amounts from SAR to JOD...');
            const amountsConverted = await convertExistingAmounts();
            if (!amountsConverted) {
                console.warn('⚠️ Failed to convert some amounts, but continuing...');
            }
        }
        
        // Step 5: Reload all modules to reflect changes
        console.log('🔄 Reloading all modules...');
        if (typeof window.reloadAllData === 'function') {
            await window.reloadAllData();
        }
        
        console.log('🎉 Successfully updated system to Jordanian Dinar!');
        console.log('📋 Summary:');
        console.log('   • Currency: د.أ (Jordanian Dinar)');
        console.log('   • Monthly subscription: 25 د.أ');
        console.log('   • Organization: ديوان المجتمع الأردني');
        console.log('   • Categories: Updated for Jordanian context');
        if (convertAmounts) {
            console.log('   • Existing amounts: Converted from SAR to JOD');
        }
        
        // Show success notification
        if (window.UIUtils) {
            window.UIUtils.showNotification('تم تحديث النظام للدينار الأردني بنجاح', 'success');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Failed to update to Jordanian Dinar:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تحديث العملة', 'danger');
        }
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.updateCurrencyToJOD = updateCurrencyToJOD;
    window.convertExistingAmounts = convertExistingAmounts;
    window.updateOrganizationForJordan = updateOrganizationForJordan;
    window.createJordanianRevenueCategories = createJordanianRevenueCategories;
    window.createJordanianExpenseCategories = createJordanianExpenseCategories;
    window.updateToJordanianDinar = updateToJordanianDinar;
    
    console.log('🎯 Jordanian Dinar update functions available:');
    console.log('- updateCurrencyToJOD()');
    console.log('- convertExistingAmounts()');
    console.log('- updateOrganizationForJordan()');
    console.log('- createJordanianRevenueCategories()');
    console.log('- createJordanianExpenseCategories()');
    console.log('- updateToJordanianDinar(convertAmounts=false)');
    console.log('');
    console.log('🚀 To update everything: updateToJordanianDinar()');
    console.log('💱 To also convert existing amounts: updateToJordanianDinar(true)');
}

// Auto-run update after modules are loaded
setTimeout(() => {
    if (typeof window !== 'undefined' && window.database && window.database.isReady) {
        console.log('🇯🇴 Auto-running Jordanian Dinar update...');
        updateToJordanianDinar(false); // Don't convert existing amounts by default
    }
}, 10000); // Wait for all modules to load
