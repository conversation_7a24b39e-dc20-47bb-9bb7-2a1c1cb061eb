// Update currency to Jordanian Dinar

console.log('💰 Currency Update Script - Jordanian Dinar');
console.log('='.repeat(50));

// Function to update currency settings
async function updateCurrencyToJOD() {
    console.log('🔄 Updating currency to Jordanian Dinar...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Update currency setting
        await window.database.run(`
            INSERT OR REPLACE INTO settings (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        `, ['currency', 'د.أ', 'العملة المستخدمة']);
        
        console.log('✅ Currency setting updated to د.أ');
        
        // Update monthly subscription to reasonable JOD amount
        await window.database.run(`
            INSERT OR REPLACE INTO settings (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        `, ['monthly_subscription', '25', 'قيمة الاشتراك الشهري']);
        
        console.log('✅ Monthly subscription updated to 25 د.أ');
        
        return true;
    } catch (error) {
        console.error('❌ Error updating currency:', error);
        return false;
    }
}

// Function to update existing payment amounts (optional conversion)
async function convertExistingAmounts() {
    console.log('🔄 Converting existing amounts to JOD...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Get current exchange rate (approximate: 1 SAR = 0.27 JOD)
        const exchangeRate = 0.27;
        
        // Update payments amounts
        const payments = await window.database.all('SELECT id, amount FROM payments');
        console.log(`Found ${payments.length} payments to convert`);
        
        for (const payment of payments) {
            const newAmount = (parseFloat(payment.amount) * exchangeRate).toFixed(3);
            await window.database.run('UPDATE payments SET amount = ? WHERE id = ?', [newAmount, payment.id]);
        }
        
        console.log(`✅ Converted ${payments.length} payment amounts`);
        
        // Update expenses amounts
        const expenses = await window.database.all('SELECT id, amount FROM expenses');
        console.log(`Found ${expenses.length} expenses to convert`);
        
        for (const expense of expenses) {
            const newAmount = (parseFloat(expense.amount) * exchangeRate).toFixed(3);
            await window.database.run('UPDATE expenses SET amount = ? WHERE id = ?', [newAmount, expense.id]);
        }
        
        console.log(`✅ Converted ${expenses.length} expense amounts`);
        
        return true;
    } catch (error) {
        console.error('❌ Error converting amounts:', error);
        return false;
    }
}

// Function to update organization name to Jordanian context
async function updateOrganizationForJordan() {
    console.log('🏢 Updating organization settings for Jordan...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Update organization name
        await window.database.run(`
            INSERT OR REPLACE INTO settings (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        `, ['organization_name', 'ديوان المجتمع الأردني', 'اسم المنظمة']);
        
        console.log('✅ Organization name updated for Jordan');
        
        // Update date format to common Jordanian format
        await window.database.run(`
            INSERT OR REPLACE INTO settings (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        `, ['date_format', 'DD/MM/YYYY', 'تنسيق التاريخ']);
        
        console.log('✅ Date format updated');
        
        return true;
    } catch (error) {
        console.error('❌ Error updating organization settings:', error);
        return false;
    }
}

// Function to create Jordanian-specific revenue categories
async function createJordanianRevenueCategories() {
    console.log('💰 Creating Jordanian-specific revenue categories...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        const jordanianCategories = [
            { name: 'اشتراك شهري', description: 'الاشتراك الشهري للأعضاء - 25 د.أ' },
            { name: 'تبرعات', description: 'التبرعات والهبات من الأعضاء والمجتمع' },
            { name: 'دعم المشاريع', description: 'دعم المشاريع التنموية والخيرية' },
            { name: 'رسوم الخدمات', description: 'رسوم الخدمات المختلفة المقدمة' },
            { name: 'إيرادات الفعاليات', description: 'إيرادات من الفعاليات والأنشطة' }
        ];
        
        for (const category of jordanianCategories) {
            // Check if category already exists
            const existing = await window.database.get(
                'SELECT id FROM revenue_categories WHERE name = ?', 
                [category.name]
            );
            
            if (!existing) {
                await window.database.run(`
                    INSERT INTO revenue_categories (name, description, is_active)
                    VALUES (?, ?, 1)
                `, [category.name, category.description]);
                
                console.log(`✅ Created revenue category: ${category.name}`);
            }
        }
        
        return true;
    } catch (error) {
        console.error('❌ Error creating Jordanian revenue categories:', error);
        return false;
    }
}

// Function to create Jordanian-specific expense categories
async function createJordanianExpenseCategories() {
    console.log('💸 Creating Jordanian-specific expense categories...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        const jordanianExpenseCategories = [
            { name: 'المرافق العامة', description: 'فواتير الكهرباء والماء والهاتف' },
            { name: 'الصيانة والإصلاح', description: 'صيانة المقر والمعدات' },
            { name: 'المساعدات الاجتماعية', description: 'دعم الأسر المحتاجة في المجتمع' },
            { name: 'الفعاليات والأنشطة', description: 'تكاليف تنظيم الفعاليات' },
            { name: 'المصروفات الإدارية', description: 'القرطاسية والمصروفات المكتبية' },
            { name: 'النقل والمواصلات', description: 'تكاليف النقل والسفر' }
        ];
        
        for (const category of jordanianExpenseCategories) {
            // Check if category already exists
            const existing = await window.database.get(
                'SELECT id FROM expense_categories WHERE name = ?', 
                [category.name]
            );
            
            if (!existing) {
                await window.database.run(`
                    INSERT INTO expense_categories (name, description, is_active)
                    VALUES (?, ?, 1)
                `, [category.name, category.description]);
                
                console.log(`✅ Created expense category: ${category.name}`);
            }
        }
        
        return true;
    } catch (error) {
        console.error('❌ Error creating Jordanian expense categories:', error);
        return false;
    }
}

// Main function to update everything to Jordanian Dinar
async function updateToJordanianDinar(convertAmounts = false) {
    console.log('🇯🇴 Starting complete update to Jordanian Dinar...');
    
    try {
        // Wait for database to be ready
        if (!window.database || !window.database.isReady) {
            console.log('⏳ Waiting for database to be ready...');
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Database ready timeout'));
                }, 10000);
                
                const checkReady = () => {
                    if (window.database && window.database.isReady) {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                
                checkReady();
            });
        }
        
        // Step 1: Update currency settings
        const currencyUpdated = await updateCurrencyToJOD();
        if (!currencyUpdated) {
            throw new Error('Failed to update currency settings');
        }
        
        // Step 2: Update organization settings
        const orgUpdated = await updateOrganizationForJordan();
        if (!orgUpdated) {
            console.warn('⚠️ Failed to update organization settings, but continuing...');
        }
        
        // Step 3: Create Jordanian categories
        await createJordanianRevenueCategories();
        await createJordanianExpenseCategories();
        
        // Step 4: Update phone numbers to Jordanian format
        console.log('📱 Updating phone numbers to Jordanian format...');
        const phonesUpdated = await updatePhoneNumbersToJordanian();
        if (!phonesUpdated) {
            console.warn('⚠️ Failed to update some phone numbers, but continuing...');
        }

        // Step 5: Convert existing amounts (optional)
        if (convertAmounts) {
            console.log('💱 Converting existing amounts from SAR to JOD...');
            const amountsConverted = await convertExistingAmounts();
            if (!amountsConverted) {
                console.warn('⚠️ Failed to convert some amounts, but continuing...');
            }
        }
        
        // Step 5: Reload all modules to reflect changes
        console.log('🔄 Reloading all modules...');
        if (typeof window.reloadAllData === 'function') {
            await window.reloadAllData();
        }
        
        console.log('🎉 Successfully updated system to Jordanian context!');
        console.log('📋 Summary:');
        console.log('   • Currency: د.أ (Jordanian Dinar)');
        console.log('   • Monthly subscription: 25 د.أ');
        console.log('   • Organization: ديوان المجتمع الأردني');
        console.log('   • Phone numbers: Updated to Jordanian format (+962)');
        console.log('   • Categories: Updated for Jordanian context');
        if (convertAmounts) {
            console.log('   • Existing amounts: Converted from SAR to JOD');
        }
        
        // Show success notification
        if (window.UIUtils) {
            window.UIUtils.showNotification('تم تحديث النظام للدينار الأردني بنجاح', 'success');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Failed to update to Jordanian Dinar:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تحديث العملة', 'danger');
        }
        return false;
    }
}

// Function to update phone numbers to Jordanian format
async function updatePhoneNumbersToJordanian() {
    console.log('📱 Updating phone numbers to Jordanian format...');

    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }

        // Get all members with phone numbers
        const members = await window.database.all('SELECT id, phone FROM members WHERE phone IS NOT NULL AND phone != ""');
        console.log(`Found ${members.length} members with phone numbers`);

        let updatedCount = 0;

        for (const member of members) {
            let updatedPhone = member.phone;

            // Convert various formats to Jordanian format
            if (member.phone.includes('966') || member.phone.startsWith('05')) {
                // Convert Saudi numbers to Jordanian equivalent
                // This is just an example - in real scenario you'd need actual phone number mapping
                updatedPhone = '0791234567'; // Default Jordanian mobile
                updatedCount++;
            } else if (!member.phone.startsWith('079') && !member.phone.startsWith('+962')) {
                // If not already Jordanian format, convert to default Jordanian mobile
                const digits = member.phone.replace(/\D/g, '');
                if (digits.length >= 8) {
                    // Create a Jordanian mobile number
                    const lastDigits = digits.slice(-7);
                    updatedPhone = '079' + lastDigits;
                    updatedCount++;
                }
            }

            // Update the phone number if it was changed
            if (updatedPhone !== member.phone) {
                await window.database.run('UPDATE members SET phone = ? WHERE id = ?', [updatedPhone, member.id]);
            }
        }

        console.log(`✅ Updated ${updatedCount} phone numbers to Jordanian format`);

        // Update WhatsApp messages phone numbers if they exist
        try {
            const whatsappMessages = await window.database.all('SELECT id, phone_number FROM whatsapp_messages WHERE phone_number IS NOT NULL');

            for (const message of whatsappMessages) {
                let updatedPhone = message.phone_number;

                if (message.phone_number.includes('966') || message.phone_number.startsWith('05')) {
                    updatedPhone = '0791234567'; // Default Jordanian mobile
                    await window.database.run('UPDATE whatsapp_messages SET phone_number = ? WHERE id = ?', [updatedPhone, message.id]);
                }
            }

            console.log(`✅ Updated WhatsApp message phone numbers`);
        } catch (e) {
            console.log('⚠️ WhatsApp messages table not found or empty');
        }

        return true;
    } catch (error) {
        console.error('❌ Error updating phone numbers:', error);
        return false;
    }
}

// Function to create sample data with Jordanian phone numbers
async function createSampleDataWithJordanianPhones() {
    console.log('🔧 Creating sample data with Jordanian phone numbers...');

    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }

        // Check if we have members
        const membersCount = await window.database.get('SELECT COUNT(*) as count FROM members');

        if (membersCount.count === 0) {
            // Create sample member with Jordanian phone
            await window.database.run(`
                INSERT INTO members (membership_id, full_name, phone, join_date, status)
                VALUES (?, ?, ?, ?, ?)
            `, ['001', 'أحمد محمد الأردني', '0791234567', new Date().toISOString().split('T')[0], 'active']);

            console.log('✅ Sample member created with Jordanian phone');
        }

        // Create sample WhatsApp data with Jordanian phone
        try {
            const member = await window.database.get('SELECT id, phone FROM members LIMIT 1');
            const existingMessage = await window.database.get('SELECT COUNT(*) as count FROM whatsapp_messages');

            if (existingMessage.count === 0) {
                await window.database.run(`
                    INSERT INTO whatsapp_messages (member_id, phone_number, message_type, message_content, status)
                    VALUES (?, ?, ?, ?, ?)
                `, [
                    member.id,
                    member.phone || '0791234567',
                    'announcement',
                    'رسالة تجريبية من نظام إدارة الديوان الأردني',
                    'sent'
                ]);

                console.log('✅ Sample WhatsApp message created with Jordanian phone');
            }
        } catch (e) {
            console.log('⚠️ Could not create WhatsApp sample data');
        }

        return true;
    } catch (error) {
        console.error('❌ Error creating sample data with Jordanian phones:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.updateCurrencyToJOD = updateCurrencyToJOD;
    window.convertExistingAmounts = convertExistingAmounts;
    window.updateOrganizationForJordan = updateOrganizationForJordan;
    window.createJordanianRevenueCategories = createJordanianRevenueCategories;
    window.createJordanianExpenseCategories = createJordanianExpenseCategories;
    window.updatePhoneNumbersToJordanian = updatePhoneNumbersToJordanian;
    window.createSampleDataWithJordanianPhones = createSampleDataWithJordanianPhones;
    window.updateToJordanianDinar = updateToJordanianDinar;
    
    console.log('🎯 Jordanian update functions available:');
    console.log('- updateCurrencyToJOD()');
    console.log('- convertExistingAmounts()');
    console.log('- updateOrganizationForJordan()');
    console.log('- createJordanianRevenueCategories()');
    console.log('- createJordanianExpenseCategories()');
    console.log('- updatePhoneNumbersToJordanian()');
    console.log('- createSampleDataWithJordanianPhones()');
    console.log('- updateToJordanianDinar(convertAmounts=false)');
    console.log('');
    console.log('🚀 To update everything: updateToJordanianDinar()');
    console.log('💱 To also convert existing amounts: updateToJordanianDinar(true)');
    console.log('📱 To update phone numbers only: updatePhoneNumbersToJordanian()');
}

// Auto-run update after modules are loaded
setTimeout(() => {
    if (typeof window !== 'undefined' && window.database && window.database.isReady) {
        console.log('🇯🇴 Auto-running Jordanian Dinar update...');
        updateToJordanianDinar(false); // Don't convert existing amounts by default
    }
}, 10000); // Wait for all modules to load
