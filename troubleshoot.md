# دليل حل مشاكل نظام إدارة الديوان

## 🔍 **تشخيص المشاكل**

### الطريقة السريعة:
1. افتح Developer Tools في المتصفح (F12)
2. اذ<PERSON><PERSON> إلى Console
3. اكتب: `runDiagnostics()`
4. انتظر النتائج وتابع التوصيات

### الطريقة اليدوية:
1. تحقق من رسائل الخطأ في Console
2. تأكد من تحميل جميع الملفات
3. تحقق من حالة قاعدة البيانات

## ❗ **المشاكل الشائعة وحلولها**

### 1. خطأ "خطأ في تحميل بيانات الأعضاء"

**الأسباب المحتملة:**
- قاعدة البيانات غير مهيأة
- مشكلة في Electron Remote API
- ملفات JavaScript غير محملة

**الحلول:**
```javascript
// في Console المتصفح:
quickFix()

// أو يدوياً:
window.database.init()
window.MembersManager.loadMembers()
```

### 2. خطأ "Database not initialized"

**الحل:**
1. تأكد من تثبيت Node.js
2. تحقق من أذونات الملفات
3. جرب إعادة تشغيل التطبيق

```bash
# في Terminal:
npm install
npm start
```

### 3. خطأ "Cannot read property of undefined"

**الحل:**
1. تحقق من تحميل جميع السكريبتات
2. تأكد من ترتيب تحميل الملفات
3. انتظر تهيئة قاعدة البيانات

### 4. صفحة الأعضاء فارغة

**الحل:**
```javascript
// في Console:
window.MembersManager.loadMembersPageContent()
window.MembersManager.loadMembers()
```

### 5. خطأ في Electron Remote

**الحل:**
- تأكد من إصدار Electron المناسب
- تحقق من إعدادات webSecurity
- استخدم المسار البديل للبيانات

## 🔧 **أوامر التشخيص المفيدة**

### في Console المتصفح:

```javascript
// تشغيل التشخيص الشامل
runDiagnostics()

// إصلاح سريع
quickFix()

// فحص قاعدة البيانات
window.database.get('SELECT COUNT(*) FROM members')

// فحص الأعضاء
window.MembersManager.members

// إعادة تحميل الأعضاء
window.MembersManager.loadMembers()

// فحص العناصر المطلوبة
document.getElementById('members-page')

// فحص الوحدات المحملة
console.log({
    database: !!window.database,
    MembersManager: !!window.MembersManager,
    UIUtils: !!window.UIUtils
})
```

## 📋 **خطوات الإصلاح المنهجية**

### الخطوة 1: التحقق الأساسي
1. تأكد من تشغيل التطبيق بـ `npm start`
2. تحقق من عدم وجود أخطاء في Console
3. تأكد من تحميل جميع الملفات

### الخطوة 2: فحص قاعدة البيانات
```javascript
// فحص الاتصال
window.database.get('SELECT 1')

// فحص الجداول
window.database.all("SELECT name FROM sqlite_master WHERE type='table'")

// فحص بيانات الأعضاء
window.database.all('SELECT * FROM members LIMIT 5')
```

### الخطوة 3: فحص الواجهة
```javascript
// فحص العناصر
document.getElementById('members-page')
document.getElementById('members-table-body')

// فحص الوحدات
window.MembersManager
window.navigationManager
```

### الخطوة 4: إعادة التهيئة
```javascript
// إعادة تهيئة قاعدة البيانات
window.database.init()

// إعادة تحميل الأعضاء
setTimeout(() => {
    window.MembersManager.loadMembers()
}, 2000)
```

## 🚨 **حالات الطوارئ**

### إذا فشل كل شيء:

1. **إعادة تعيين قاعدة البيانات:**
```javascript
// احذف قاعدة البيانات وأعد إنشاءها
window.database.createTables()
```

2. **إعادة تحميل التطبيق:**
```javascript
location.reload()
```

3. **إعادة تثبيت التبعيات:**
```bash
rm -rf node_modules
npm install
npm start
```

## 📞 **طلب المساعدة**

إذا استمرت المشاكل:

1. شغل `runDiagnostics()` وانسخ النتائج
2. افتح Console وانسخ جميع رسائل الخطأ
3. تحقق من إصدار Node.js: `node --version`
4. تحقق من إصدار npm: `npm --version`

## 🔄 **الصيانة الدورية**

### أسبوعياً:
- شغل `runDiagnostics()` للتأكد من سلامة النظام
- أنشئ نسخة احتياطية من قاعدة البيانات

### شهرياً:
- نظف البيانات المؤقتة
- حدث التبعيات إذا لزم الأمر
- تحقق من مساحة القرص الصلب

## 📝 **سجل الأخطاء الشائعة**

| الخطأ | السبب | الحل |
|-------|--------|------|
| Database not initialized | قاعدة البيانات غير مهيأة | `window.database.init()` |
| Cannot read property | وحدة غير محملة | تحقق من ترتيب السكريبتات |
| Members page empty | فشل تحميل المحتوى | `loadMembersPageContent()` |
| Remote API error | مشكلة Electron | استخدم المسار البديل |

---

**ملاحظة:** هذا الدليل يغطي المشاكل الأكثر شيوعاً. للمشاكل المعقدة، استخدم أدوات التشخيص المدمجة في النظام.
