"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Pricing
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NumberListInstance = exports.NumberInstance = exports.NumberContextImpl = exports.PricingV1VoiceVoiceNumberOutboundCallPrice = exports.PricingV1VoiceVoiceNumberInboundCallPrice = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
/**
 * The [InboundCallPrice](https://www.twilio.com/docs/voice/pricing#inbound-call-price) record. If `null`, the Phone Number is not a Twilio number owned by this account.
 */
class PricingV1VoiceVoiceNumberInboundCallPrice {
}
exports.PricingV1VoiceVoiceNumberInboundCallPrice = PricingV1VoiceVoiceNumberInboundCallPrice;
/**
 * The OutboundCallPrice record, which includes a list of `origination_prefixes` and the `base_price` and `current_price` for those prefixes.
 */
class PricingV1VoiceVoiceNumberOutboundCallPrice {
}
exports.PricingV1VoiceVoiceNumberOutboundCallPrice = PricingV1VoiceVoiceNumberOutboundCallPrice;
class NumberContextImpl {
    constructor(_version, number) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(number)) {
            throw new Error("Parameter 'number' is not valid.");
        }
        this._solution = { number };
        this._uri = `/Voice/Numbers/${number}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new NumberInstance(operationVersion, payload, instance._solution.number));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.NumberContextImpl = NumberContextImpl;
class NumberInstance {
    constructor(_version, payload, number) {
        this._version = _version;
        this.number = payload.number;
        this.country = payload.country;
        this.isoCountry = payload.iso_country;
        this.outboundCallPrice = payload.outbound_call_price;
        this.inboundCallPrice = payload.inbound_call_price;
        this.priceUnit = payload.price_unit;
        this.url = payload.url;
        this._solution = { number: number || this.number };
    }
    get _proxy() {
        this._context =
            this._context ||
                new NumberContextImpl(this._version, this._solution.number);
        return this._context;
    }
    /**
     * Fetch a NumberInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed NumberInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            number: this.number,
            country: this.country,
            isoCountry: this.isoCountry,
            outboundCallPrice: this.outboundCallPrice,
            inboundCallPrice: this.inboundCallPrice,
            priceUnit: this.priceUnit,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.NumberInstance = NumberInstance;
function NumberListInstance(version) {
    const instance = ((number) => instance.get(number));
    instance.get = function get(number) {
        return new NumberContextImpl(version, number);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.NumberListInstance = NumberListInstance;
