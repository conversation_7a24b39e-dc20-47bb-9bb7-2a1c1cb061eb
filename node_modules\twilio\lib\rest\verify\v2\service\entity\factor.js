"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FactorPage = exports.FactorListInstance = exports.FactorInstance = exports.FactorContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class FactorContextImpl {
    constructor(_version, serviceSid, identity, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(serviceSid)) {
            throw new Error("Parameter 'serviceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(identity)) {
            throw new Error("Parameter 'identity' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { serviceSid, identity, sid };
        this._uri = `/Services/${serviceSid}/Entities/${identity}/Factors/${sid}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new FactorInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.identity, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["authPayload"] !== undefined)
            data["AuthPayload"] = params["authPayload"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["config.notificationToken"] !== undefined)
            data["Config.NotificationToken"] = params["config.notificationToken"];
        if (params["config.sdkVersion"] !== undefined)
            data["Config.SdkVersion"] = params["config.sdkVersion"];
        if (params["config.timeStep"] !== undefined)
            data["Config.TimeStep"] = params["config.timeStep"];
        if (params["config.skew"] !== undefined)
            data["Config.Skew"] = params["config.skew"];
        if (params["config.codeLength"] !== undefined)
            data["Config.CodeLength"] = params["config.codeLength"];
        if (params["config.alg"] !== undefined)
            data["Config.Alg"] = params["config.alg"];
        if (params["config.notificationPlatform"] !== undefined)
            data["Config.NotificationPlatform"] =
                params["config.notificationPlatform"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new FactorInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.identity, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.FactorContextImpl = FactorContextImpl;
class FactorInstance {
    constructor(_version, payload, serviceSid, identity, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.serviceSid = payload.service_sid;
        this.entitySid = payload.entity_sid;
        this.identity = payload.identity;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.friendlyName = payload.friendly_name;
        this.status = payload.status;
        this.factorType = payload.factor_type;
        this.config = payload.config;
        this.metadata = payload.metadata;
        this.url = payload.url;
        this._solution = { serviceSid, identity, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new FactorContextImpl(this._version, this._solution.serviceSid, this._solution.identity, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a FactorInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a FactorInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed FactorInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            serviceSid: this.serviceSid,
            entitySid: this.entitySid,
            identity: this.identity,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            friendlyName: this.friendlyName,
            status: this.status,
            factorType: this.factorType,
            config: this.config,
            metadata: this.metadata,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.FactorInstance = FactorInstance;
function FactorListInstance(version, serviceSid, identity) {
    if (!(0, utility_1.isValidPathParam)(serviceSid)) {
        throw new Error("Parameter 'serviceSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(identity)) {
        throw new Error("Parameter 'identity' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new FactorContextImpl(version, serviceSid, identity, sid);
    };
    instance._version = version;
    instance._solution = { serviceSid, identity };
    instance._uri = `/Services/${serviceSid}/Entities/${identity}/Factors`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new FactorPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new FactorPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.FactorListInstance = FactorListInstance;
class FactorPage extends Page_1.default {
    /**
     * Initialize the FactorPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of FactorInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new FactorInstance(this._version, payload, this._solution.serviceSid, this._solution.identity);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.FactorPage = FactorPage;
