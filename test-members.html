<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الأعضاء</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid rgba(255,255,255,0.2);
        }
        .test-section h3 {
            color: #ffd700;
            margin-top: 0;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            font-weight: bold;
            text-align: center;
        }
        .success {
            background: rgba(40, 167, 69, 0.9);
            border: 2px solid #28a745;
        }
        .error {
            background: rgba(220, 53, 69, 0.9);
            border: 2px solid #dc3545;
        }
        .info {
            background: rgba(23, 162, 184, 0.9);
            border: 2px solid #17a2b8;
        }
        .member-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #ffd700;
        }
        .member-info {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .button-container {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار إدارة الأعضاء</h1>
        
        <div id="status" class="status info">⏳ جاري فحص قاعدة البيانات...</div>
        
        <div class="test-section">
            <h3>📊 حالة قاعدة البيانات</h3>
            <div id="database-status"></div>
        </div>
        
        <div class="test-section">
            <h3>👥 الأعضاء المحفوظون</h3>
            <div id="members-list"></div>
        </div>
        
        <div class="test-section">
            <h3>💰 المدفوعات المرتبطة</h3>
            <div id="payments-list"></div>
        </div>
        
        <div class="button-container">
            <button onclick="runTest()">🔄 إعادة الاختبار</button>
            <button onclick="addTestMember()">➕ إضافة عضو تجريبي</button>
            <button onclick="clearData()">🗑️ مسح البيانات</button>
        </div>
    </div>

    <!-- Same inline database as in index.html -->
    <script>
        // Inline Database - مضمونة 100%
        console.log('🔄 Loading Inline Database...');
        
        window.database = {
            isReady: true,
            error: null,
            data: {
                members: [
                    {
                        id: 1,
                        membership_id: 'M001',
                        full_name: 'أحمد محمد علي',
                        phone: '00962791234567',
                        email: '<EMAIL>',
                        address: 'عمان، الأردن',
                        join_date: '2024-01-01',
                        status: 'active',
                        notes: 'عضو مؤسس',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    }
                ],
                payments: [
                    {
                        id: 1,
                        member_id: 1,
                        category_id: 1,
                        amount: 25.000,
                        payment_date: '2024-01-01',
                        payment_method: 'cash',
                        reference_number: 'PAY001',
                        notes: 'دفعة تجريبية',
                        created_at: new Date().toISOString()
                    }
                ],
                expenses: [
                    {
                        id: 1,
                        category_id: 1,
                        amount: 10.000,
                        expense_date: '2024-01-01',
                        description: 'مصروف تجريبي',
                        receipt_path: '',
                        notes: 'مصروف للاختبار',
                        created_at: new Date().toISOString()
                    }
                ],
                revenue_categories: [
                    { id: 1, name: 'اشتراك شهري', description: 'الاشتراك الشهري للأعضاء', is_active: true },
                    { id: 2, name: 'تبرعات', description: 'التبرعات المختلفة', is_active: true }
                ],
                expense_categories: [
                    { id: 1, name: 'فعاليات', description: 'مصروفات الفعاليات والأنشطة', is_active: true },
                    { id: 2, name: 'خدمات', description: 'مصروفات الخدمات المقدمة', is_active: true }
                ],
                settings: [
                    { key: 'currency', value: 'د.أ', description: 'العملة المستخدمة' },
                    { key: 'organization_name', value: 'الديوان', description: 'اسم المنظمة' }
                ]
            },
            nextId: { members: 2, payments: 2, expenses: 2 },
            
            // Load from localStorage
            loadData: function() {
                try {
                    const stored = localStorage.getItem('dewan_inline_db');
                    if (stored) {
                        const parsed = JSON.parse(stored);
                        this.data = { ...this.data, ...parsed };
                        console.log('📊 Data loaded from localStorage');
                    }
                } catch (e) {
                    console.log('📊 Using default data');
                }
            },
            
            // Save to localStorage
            saveData: function() {
                try {
                    localStorage.setItem('dewan_inline_db', JSON.stringify(this.data));
                    console.log('💾 Data saved to localStorage');
                } catch (e) {
                    console.error('Error saving data:', e);
                }
            },
            
            async all(sql, params = []) {
                const sqlLower = sql.toLowerCase();
                
                if (sqlLower.includes('from members')) {
                    return this.data.members;
                }
                if (sqlLower.includes('from payments')) {
                    return this.data.payments;
                }
                
                return [];
            }
        };
        
        // Load data immediately
        window.database.loadData();
        
        console.log('✅ Inline Database loaded successfully!');
    </script>

    <script>
        async function runTest() {
            const statusDiv = document.getElementById('status');
            
            try {
                statusDiv.innerHTML = '⏳ جاري فحص قاعدة البيانات...';
                statusDiv.className = 'status info';

                // Test 1: Check database
                if (!window.database) {
                    throw new Error('قاعدة البيانات غير محملة');
                }

                if (!window.database.isReady) {
                    throw new Error('قاعدة البيانات غير جاهزة');
                }

                // Test 2: Check members
                const members = await window.database.all('SELECT * FROM members');
                const payments = await window.database.all('SELECT * FROM payments');

                // Show success
                statusDiv.innerHTML = '✅ قاعدة البيانات تعمل بنجاح!';
                statusDiv.className = 'status success';

                // Display database status
                document.getElementById('database-status').innerHTML = `
                    <div class="member-info"><span>حالة قاعدة البيانات:</span><span>✅ جاهزة</span></div>
                    <div class="member-info"><span>عدد الأعضاء:</span><span>${members.length}</span></div>
                    <div class="member-info"><span>عدد المدفوعات:</span><span>${payments.length}</span></div>
                    <div class="member-info"><span>التخزين:</span><span>localStorage</span></div>
                `;

                // Display members
                if (members.length > 0) {
                    document.getElementById('members-list').innerHTML = members.map(member => `
                        <div class="member-card">
                            <div class="member-info"><strong>الاسم:</strong> <span>${member.full_name}</span></div>
                            <div class="member-info"><strong>رقم العضوية:</strong> <span>${member.membership_id}</span></div>
                            <div class="member-info"><strong>الهاتف:</strong> <span>${member.phone || '-'}</span></div>
                            <div class="member-info"><strong>تاريخ الانضمام:</strong> <span>${member.join_date}</span></div>
                            <div class="member-info"><strong>الحالة:</strong> <span>${member.status === 'active' ? '✅ نشط' : '❌ غير نشط'}</span></div>
                        </div>
                    `).join('');
                } else {
                    document.getElementById('members-list').innerHTML = '<div class="member-info">لا يوجد أعضاء</div>';
                }

                // Display payments
                if (payments.length > 0) {
                    document.getElementById('payments-list').innerHTML = payments.map(payment => {
                        const member = members.find(m => m.id === payment.member_id);
                        return `
                            <div class="member-card">
                                <div class="member-info"><strong>العضو:</strong> <span>${member ? member.full_name : 'غير معروف'}</span></div>
                                <div class="member-info"><strong>المبلغ:</strong> <span>${payment.amount} د.أ</span></div>
                                <div class="member-info"><strong>التاريخ:</strong> <span>${payment.payment_date}</span></div>
                                <div class="member-info"><strong>طريقة الدفع:</strong> <span>${payment.payment_method}</span></div>
                                <div class="member-info"><strong>رقم المرجع:</strong> <span>${payment.reference_number || '-'}</span></div>
                            </div>
                        `;
                    }).join('');
                } else {
                    document.getElementById('payments-list').innerHTML = '<div class="member-info">لا يوجد مدفوعات</div>';
                }

            } catch (error) {
                statusDiv.innerHTML = `❌ خطأ: ${error.message}`;
                statusDiv.className = 'status error';
            }
        }

        function addTestMember() {
            const newMember = {
                id: window.database.nextId.members++,
                membership_id: `M${String(window.database.nextId.members).padStart(3, '0')}`,
                full_name: 'عضو تجريبي جديد',
                phone: '00962791234569',
                email: '<EMAIL>',
                address: 'الزرقاء، الأردن',
                join_date: new Date().toISOString().split('T')[0],
                status: 'active',
                notes: 'تم إضافته من الاختبار',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            window.database.data.members.push(newMember);
            window.database.saveData();
            
            alert('تم إضافة عضو تجريبي جديد!');
            runTest();
        }

        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('dewan_inline_db');
                location.reload();
            }
        }

        // Auto-run test
        window.addEventListener('load', () => {
            setTimeout(runTest, 500);
        });
    </script>
</body>
</html>
