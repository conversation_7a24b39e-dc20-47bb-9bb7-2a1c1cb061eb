<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رسائل تأكيد الدفع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .test-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .message-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .whatsapp-link {
            background-color: #25d366;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin: 10px 0;
        }
        .whatsapp-link:hover {
            background-color: #128c7e;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">
            <i class="bi bi-whatsapp text-success"></i>
            اختبار رسائل تأكيد الدفع
        </h1>
        
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> إعدادات الرسالة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="admin-phone" class="form-label">رقم هاتف المدير</label>
                                    <input type="text" class="form-control" id="admin-phone" 
                                           value="0791234567" placeholder="0791234567">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="member-name" class="form-label">اسم العضو</label>
                                    <input type="text" class="form-control" id="member-name" 
                                           value="أحمد محمد الأردني" placeholder="اسم العضو">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="member-phone" class="form-label">رقم هاتف العضو</label>
                                    <input type="text" class="form-control" id="member-phone" 
                                           value="0791234568" placeholder="0791234568">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment-amount" class="form-label">مبلغ الدفعة</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="payment-amount" 
                                               value="25" step="0.001" min="0">
                                        <span class="input-group-text">د.أ</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="payment-date" class="form-label">تاريخ الدفعة</label>
                                    <input type="date" class="form-control" id="payment-date">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="organization-name" class="form-label">اسم المنظمة</label>
                                    <input type="text" class="form-control" id="organization-name" 
                                           value="ديوان المجتمع الأردني" placeholder="اسم المنظمة">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message-template" class="form-label">نموذج الرسالة</label>
                            <textarea class="form-control" id="message-template" rows="3"
                                      placeholder="تم استلام دفعتك بمبلغ {amount} {currency} بتاريخ {date}. شكراً لك - {organization}">تم استلام دفعتك بمبلغ {amount} {currency} بتاريخ {date}. شكراً لك - {organization}</textarea>
                            <div class="form-text">
                                المتغيرات المتاحة: {amount}, {currency}, {date}, {organization}, {member_name}
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button class="btn btn-primary btn-lg" onclick="generateMessage()">
                                <i class="bi bi-magic"></i>
                                إنشاء الرسالة
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="result-container" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-chat-text"></i> معاينة الرسالة</h5>
                        </div>
                        <div class="card-body">
                            <div id="message-preview" class="message-preview"></div>
                            
                            <div class="text-center">
                                <a id="whatsapp-link" class="whatsapp-link" target="_blank">
                                    <i class="bi bi-whatsapp"></i>
                                    إرسال عبر واتساب
                                </a>
                            </div>
                            
                            <div class="mt-3">
                                <h6>تفاصيل الرابط:</h6>
                                <div class="form-group">
                                    <label class="form-label">رقم الهاتف المستهدف:</label>
                                    <input type="text" class="form-control" id="target-phone" readonly>
                                </div>
                                <div class="form-group mt-2">
                                    <label class="form-label">الرابط الكامل:</label>
                                    <textarea class="form-control" id="full-link" rows="3" readonly></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-list-check"></i> اختبارات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testJordanianNumbers()">
                                        اختبار أرقام أردنية
                                    </button>
                                    <button class="btn btn-outline-success w-100 mb-2" onclick="testMessageTemplates()">
                                        اختبار قوالب الرسائل
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-info w-100 mb-2" onclick="testCurrencyFormats()">
                                        اختبار تنسيق العملة
                                    </button>
                                    <button class="btn btn-outline-warning w-100 mb-2" onclick="testDateFormats()">
                                        اختبار تنسيق التاريخ
                                    </button>
                                </div>
                            </div>
                            <div id="test-results" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set today's date as default
        document.getElementById('payment-date').value = new Date().toISOString().split('T')[0];
        
        function generateMessage() {
            const adminPhone = document.getElementById('admin-phone').value.trim();
            const memberName = document.getElementById('member-name').value.trim();
            const memberPhone = document.getElementById('member-phone').value.trim();
            const amount = document.getElementById('payment-amount').value;
            const date = document.getElementById('payment-date').value;
            const organization = document.getElementById('organization-name').value.trim();
            const template = document.getElementById('message-template').value.trim();
            
            // Validate inputs
            if (!memberPhone || !amount || !date) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            // Format amount
            const formattedAmount = parseFloat(amount).toLocaleString('ar-JO', {
                minimumFractionDigits: 3,
                maximumFractionDigits: 3
            }) + ' د.أ';
            
            // Format date
            const formattedDate = new Date(date).toLocaleDateString('ar-JO');
            
            // Generate message
            const message = template
                .replace('{amount}', formattedAmount)
                .replace('{currency}', 'د.أ')
                .replace('{date}', formattedDate)
                .replace('{organization}', organization)
                .replace('{member_name}', memberName);
            
            // Clean and format phone number
            const cleanMemberPhone = cleanPhone(memberPhone);
            const internationalPhone = convertToInternational(cleanMemberPhone);
            
            // Create WhatsApp URL
            const encodedMessage = encodeURIComponent(message);
            const whatsappUrl = `https://wa.me/${internationalPhone}?text=${encodedMessage}`;
            
            // Display results
            document.getElementById('message-preview').textContent = message;
            document.getElementById('target-phone').value = internationalPhone;
            document.getElementById('full-link').value = whatsappUrl;
            document.getElementById('whatsapp-link').href = whatsappUrl;
            document.getElementById('result-container').style.display = 'block';
            
            // Scroll to results
            document.getElementById('result-container').scrollIntoView({ behavior: 'smooth' });
        }
        
        function cleanPhone(phone) {
            // Remove all non-digit characters except +
            let cleaned = phone.replace(/[^\d\+]/g, '');
            
            // Handle Jordanian formats
            if (cleaned.startsWith('+962')) {
                cleaned = '0' + cleaned.substring(4);
            } else if (cleaned.startsWith('00962')) {
                cleaned = '0' + cleaned.substring(5);
            } else if (cleaned.startsWith('962')) {
                cleaned = '0' + cleaned.substring(3);
            } else if (cleaned.length === 9 && cleaned.startsWith('7')) {
                cleaned = '0' + cleaned;
            }
            
            return cleaned;
        }
        
        function convertToInternational(phone) {
            const cleaned = cleanPhone(phone);
            if (cleaned.startsWith('0')) {
                return '962' + cleaned.substring(1);
            }
            return cleaned;
        }
        
        function testJordanianNumbers() {
            const testNumbers = [
                '0791234567',
                '+962791234567',
                '00962781234567',
                '791234567',
                '0781234567'
            ];
            
            let results = '<h6>اختبار الأرقام الأردنية:</h6>';
            testNumbers.forEach(number => {
                const cleaned = cleanPhone(number);
                const international = convertToInternational(cleaned);
                results += `<div class="test-result test-info">
                    <strong>المدخل:</strong> ${number}<br>
                    <strong>منظف:</strong> ${cleaned}<br>
                    <strong>دولي:</strong> ${international}
                </div>`;
            });
            
            document.getElementById('test-results').innerHTML = results;
        }
        
        function testMessageTemplates() {
            const templates = [
                'تم استلام دفعتك بمبلغ {amount} بتاريخ {date}. شكراً لك - {organization}',
                'عزيزي {member_name}، تم تسجيل دفعة بقيمة {amount} {currency} في {date}. مع التقدير، {organization}',
                'إشعار دفع: {amount} {currency} - {date} | {organization}',
                'شكراً {member_name} لدفعتك {amount} المستلمة بتاريخ {date} 🙏'
            ];
            
            let results = '<h6>اختبار قوالب الرسائل:</h6>';
            templates.forEach((template, index) => {
                const message = template
                    .replace('{amount}', '25.000 د.أ')
                    .replace('{currency}', 'د.أ')
                    .replace('{date}', new Date().toLocaleDateString('ar-JO'))
                    .replace('{organization}', 'ديوان المجتمع الأردني')
                    .replace('{member_name}', 'أحمد محمد');
                
                results += `<div class="test-result test-success">
                    <strong>القالب ${index + 1}:</strong><br>
                    ${message}
                </div>`;
            });
            
            document.getElementById('test-results').innerHTML = results;
        }
        
        function testCurrencyFormats() {
            const amounts = [25, 100.5, 1250.750, 0.500];
            
            let results = '<h6>اختبار تنسيق العملة:</h6>';
            amounts.forEach(amount => {
                const formatted = amount.toLocaleString('ar-JO', {
                    minimumFractionDigits: 3,
                    maximumFractionDigits: 3
                }) + ' د.أ';
                
                results += `<div class="test-result test-info">
                    <strong>المبلغ:</strong> ${amount}<br>
                    <strong>منسق:</strong> ${formatted}
                </div>`;
            });
            
            document.getElementById('test-results').innerHTML = results;
        }
        
        function testDateFormats() {
            const dates = [
                new Date(),
                new Date('2024-01-15'),
                new Date('2024-12-31')
            ];
            
            let results = '<h6>اختبار تنسيق التاريخ:</h6>';
            dates.forEach(date => {
                const arabicDate = date.toLocaleDateString('ar-JO');
                const isoDate = date.toISOString().split('T')[0];
                
                results += `<div class="test-result test-success">
                    <strong>التاريخ الأصلي:</strong> ${isoDate}<br>
                    <strong>التاريخ العربي:</strong> ${arabicDate}
                </div>`;
            });
            
            document.getElementById('test-results').innerHTML = results;
        }
    </script>
</body>
</html>
