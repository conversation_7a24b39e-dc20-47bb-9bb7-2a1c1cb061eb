// Debug startup issues

console.log('🔍 Debug Startup Script');
console.log('='.repeat(50));

// Check environment
console.log('Environment Check:');
console.log('- typeof require:', typeof require);
console.log('- typeof process:', typeof process);
console.log('- typeof window:', typeof window);
console.log('- typeof document:', typeof document);

// Check if we can load sqlite3
try {
    const sqlite3 = require('sqlite3');
    console.log('✅ sqlite3 module available');
} catch (error) {
    console.error('❌ sqlite3 module error:', error.message);
}

// Check if we can access path
try {
    const path = require('path');
    console.log('✅ path module available');
} catch (error) {
    console.error('❌ path module error:', error.message);
}

// Check if we can access fs
try {
    const fs = require('fs');
    console.log('✅ fs module available');
} catch (error) {
    console.error('❌ fs module error:', error.message);
}

// Check if we can access os
try {
    const os = require('os');
    console.log('✅ os module available');
    console.log('- Home directory:', os.homedir());
} catch (error) {
    console.error('❌ os module error:', error.message);
}

// Check Electron
try {
    const { app } = require('electron').remote || require('@electron/remote');
    console.log('✅ Electron remote available');
    console.log('- User data path:', app.getPath('userData'));
} catch (error) {
    console.log('⚠️  Electron remote not available:', error.message);
    console.log('Will use fallback path');
}

console.log('='.repeat(50));
console.log('Debug completed. Check console for any errors.');

// Try to create a simple database connection
setTimeout(() => {
    console.log('🔧 Testing database creation...');
    try {
        const sqlite3 = require('sqlite3').verbose();
        const path = require('path');
        const os = require('os');
        
        const testDbPath = path.join(os.homedir(), 'test-dewan.db');
        console.log('Test database path:', testDbPath);
        
        const testDb = new sqlite3.Database(testDbPath, (err) => {
            if (err) {
                console.error('❌ Test database creation failed:', err);
            } else {
                console.log('✅ Test database created successfully');
                testDb.close();
                
                // Clean up test file
                const fs = require('fs');
                if (fs.existsSync(testDbPath)) {
                    fs.unlinkSync(testDbPath);
                    console.log('✅ Test database cleaned up');
                }
            }
        });
    } catch (error) {
        console.error('❌ Database test failed:', error);
    }
}, 1000);
