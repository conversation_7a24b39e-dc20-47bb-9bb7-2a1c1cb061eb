<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار قاعدة البيانات</h1>
        
        <div id="results"></div>
        
        <button onclick="runTests()">تشغيل الاختبارات</button>
        <button onclick="clearResults()">مسح النتائج</button>
        <button onclick="showData()">عرض البيانات</button>
        <button onclick="clearStorage()">مسح التخزين</button>
    </div>

    <script src="src/renderer/js/database-simple.js"></script>
    <script>
        let testResults = [];

        function addResult(type, message, details = null) {
            const result = { type, message, details, timestamp: new Date() };
            testResults.push(result);
            displayResults();
        }

        function displayResults() {
            const container = document.getElementById('results');
            container.innerHTML = testResults.map(result => `
                <div class="test-result ${result.type}">
                    <strong>${result.timestamp.toLocaleTimeString()}</strong> - ${result.message}
                    ${result.details ? `<pre>${JSON.stringify(result.details, null, 2)}</pre>` : ''}
                </div>
            `).join('');
            container.scrollTop = container.scrollHeight;
        }

        function clearResults() {
            testResults = [];
            displayResults();
        }

        async function runTests() {
            addResult('info', '🔄 بدء الاختبارات...');

            try {
                // Test 1: Check if database is loaded
                if (window.database) {
                    addResult('success', '✅ وحدة قاعدة البيانات محملة');
                } else {
                    addResult('error', '❌ وحدة قاعدة البيانات غير محملة');
                    return;
                }

                // Test 2: Check if database is ready
                if (window.database.isReady) {
                    addResult('success', '✅ قاعدة البيانات جاهزة');
                } else {
                    addResult('error', '❌ قاعدة البيانات غير جاهزة');
                    
                    // Wait for ready event
                    addResult('info', '⏳ انتظار جاهزية قاعدة البيانات...');
                    await new Promise((resolve) => {
                        const timeout = setTimeout(() => {
                            addResult('error', '❌ انتهت مهلة انتظار قاعدة البيانات');
                            resolve();
                        }, 5000);

                        window.addEventListener('database-ready', () => {
                            clearTimeout(timeout);
                            addResult('success', '✅ قاعدة البيانات أصبحت جاهزة');
                            resolve();
                        }, { once: true });
                    });
                }

                // Test 3: Test basic queries
                addResult('info', '🔍 اختبار الاستعلامات الأساسية...');

                // Test members query
                const members = await window.database.all('SELECT * FROM members');
                addResult('success', `✅ استعلام الأعضاء: ${members.length} عضو`, members);

                // Test payments query
                const payments = await window.database.all('SELECT * FROM payments');
                addResult('success', `✅ استعلام المدفوعات: ${payments.length} دفعة`, payments);

                // Test expenses query
                const expenses = await window.database.all('SELECT * FROM expenses');
                addResult('success', `✅ استعلام المصروفات: ${expenses.length} مصروف`, expenses);

                // Test categories
                const revenueCategories = await window.database.all('SELECT * FROM revenue_categories');
                addResult('success', `✅ فئات الإيرادات: ${revenueCategories.length} فئة`, revenueCategories);

                const expenseCategories = await window.database.all('SELECT * FROM expense_categories');
                addResult('success', `✅ فئات المصروفات: ${expenseCategories.length} فئة`, expenseCategories);

                // Test 4: Test insert operation
                addResult('info', '📝 اختبار إدراج البيانات...');
                
                const insertResult = await window.database.run(
                    'INSERT INTO members (membership_id, full_name, phone, join_date, status) VALUES (?, ?, ?, ?, ?)',
                    ['TEST001', 'عضو تجريبي', '00962791234567', '2024-01-01', 'active']
                );
                addResult('success', `✅ إدراج عضو جديد: ID ${insertResult.id}`, insertResult);

                // Test 5: Test get single record
                const member = await window.database.get('SELECT * FROM members WHERE id = ?', [insertResult.id]);
                addResult('success', '✅ استعلام عضو واحد', member);

                // Test 6: Test aggregate queries
                const memberCount = await window.database.get('SELECT COUNT(*) as count FROM members');
                addResult('success', `✅ عدد الأعضاء: ${memberCount.count}`, memberCount);

                addResult('success', '🎉 جميع الاختبارات نجحت!');

            } catch (error) {
                addResult('error', `❌ خطأ في الاختبار: ${error.message}`, error);
            }
        }

        async function showData() {
            try {
                const data = {
                    members: await window.database.all('SELECT * FROM members'),
                    payments: await window.database.all('SELECT * FROM payments'),
                    expenses: await window.database.all('SELECT * FROM expenses'),
                    revenue_categories: await window.database.all('SELECT * FROM revenue_categories'),
                    expense_categories: await window.database.all('SELECT * FROM expense_categories'),
                    settings: await window.database.all('SELECT * FROM settings')
                };
                addResult('info', '📊 جميع البيانات المحفوظة:', data);
            } catch (error) {
                addResult('error', `❌ خطأ في عرض البيانات: ${error.message}`);
            }
        }

        function clearStorage() {
            localStorage.removeItem('dewan_database');
            addResult('info', '🗑️ تم مسح التخزين المحلي - أعد تحميل الصفحة');
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });

        // Listen for database events
        window.addEventListener('database-ready', () => {
            addResult('success', '🎉 تم استلام حدث جاهزية قاعدة البيانات');
        });

        window.addEventListener('database-module-loaded', () => {
            addResult('success', '📦 تم تحميل وحدة قاعدة البيانات');
        });
    </script>
</body>
</html>
