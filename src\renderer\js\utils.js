// Utility functions for the application

// Date formatting utilities
const DateUtils = {
    // Format date for display
    formatDate(date, format = 'DD/MM/YYYY') {
        if (!date) return '';
        const momentDate = moment(date);
        return momentDate.format(format);
    },

    // Format date for database
    formatDateForDB(date) {
        if (!date) return null;
        return moment(date).format('YYYY-MM-DD');
    },

    // Get current date in Arabic
    getCurrentDateArabic() {
        moment.locale('ar');
        return moment().format('dddd، DD MMMM YYYY');
    },

    // Check if date is overdue
    isOverdue(date, days = 0) {
        const dueDate = moment(date).add(days, 'days');
        return moment().isAfter(dueDate);
    },

    // Get days difference
    getDaysDifference(date1, date2) {
        return moment(date1).diff(moment(date2), 'days');
    }
};

// Number formatting utilities
const NumberUtils = {
    // Format currency
    formatCurrency(amount, currency = 'ر.س') {
        if (isNaN(amount)) return '0 ' + currency;
        return parseFloat(amount).toLocaleString('ar-SA', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' ' + currency;
    },

    // Format number with Arabic numerals
    formatNumber(number) {
        if (isNaN(number)) return '0';
        return parseFloat(number).toLocaleString('ar-SA');
    },

    // Parse Arabic numerals to English
    parseArabicNumber(arabicNumber) {
        if (typeof arabicNumber !== 'string') return arabicNumber;
        
        const arabicNumerals = '٠١٢٣٤٥٦٧٨٩';
        const englishNumerals = '0123456789';
        
        let result = arabicNumber;
        for (let i = 0; i < arabicNumerals.length; i++) {
            result = result.replace(new RegExp(arabicNumerals[i], 'g'), englishNumerals[i]);
        }
        
        return parseFloat(result) || 0;
    }
};

// Validation utilities
const ValidationUtils = {
    // Validate required fields
    validateRequired(value, fieldName) {
        if (!value || value.toString().trim() === '') {
            return `حقل ${fieldName} مطلوب`;
        }
        return null;
    },

    // Validate phone number (flexible for multiple countries)
    validatePhone(phone) {
        if (!phone) return 'رقم الهاتف مطلوب';

        // Remove spaces and special characters
        const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, '');

        // Check if it contains only digits
        if (!/^\d+$/.test(cleanPhone)) {
            return 'رقم الهاتف يجب أن يحتوي على أرقام فقط';
        }

        // Check minimum and maximum length
        if (cleanPhone.length < 8) {
            return 'رقم الهاتف قصير جداً (الحد الأدنى 8 أرقام)';
        }

        if (cleanPhone.length > 15) {
            return 'رقم الهاتف طويل جداً (الحد الأقصى 15 رقم)';
        }

        // Additional validation for common formats
        const phonePatterns = [
            /^((\+?966)|0)?5[0-9]{8}$/,           // Saudi mobile
            /^((\+?966)|0)?1[0-9]{7}$/,           // Saudi landline
            /^((\+?971)|0)?5[0-9]{8}$/,           // UAE mobile
            /^((\+?965)|0)?[0-9]{8}$/,            // Kuwait
            /^((\+?974)|0)?[0-9]{8}$/,            // Qatar
            /^((\+?973)|0)?[0-9]{8}$/,            // Bahrain
            /^((\+?968)|0)?[0-9]{8}$/,            // Oman
            /^((\+?962)|0)?7[0-9]{8}$/,           // Jordan mobile
            /^((\+?20)|0)?1[0-9]{9}$/,            // Egypt mobile
            /^[0-9]{8,15}$/                       // Generic international
        ];

        // Check if phone matches any valid pattern
        const isValid = phonePatterns.some(pattern => pattern.test(cleanPhone));

        if (!isValid) {
            return 'تنسيق رقم الهاتف غير مدعوم';
        }

        return null;
    },

    // Format phone number for display
    formatPhone(phone) {
        if (!phone) return '';

        // Remove all non-digit characters
        const cleanPhone = phone.replace(/\D/g, '');

        // Format based on length and pattern
        if (cleanPhone.length === 10 && cleanPhone.startsWith('05')) {
            // Saudi mobile: 0512345678 -> 0512 345 678
            return cleanPhone.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
        } else if (cleanPhone.length === 9 && cleanPhone.startsWith('5')) {
            // Saudi mobile without 0: 512345678 -> 512 345 678
            return cleanPhone.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3');
        } else if (cleanPhone.length === 12 && cleanPhone.startsWith('966')) {
            // Saudi with country code: 966512345678 -> +966 512 345 678
            return cleanPhone.replace(/(\d{3})(\d{3})(\d{3})(\d{3})/, '+$1 $2 $3 $4');
        } else if (cleanPhone.length >= 8 && cleanPhone.length <= 15) {
            // Generic formatting for other numbers
            if (cleanPhone.length === 8) {
                return cleanPhone.replace(/(\d{4})(\d{4})/, '$1 $2');
            } else if (cleanPhone.length === 11) {
                return cleanPhone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
            } else {
                // Default: add space every 3 digits from right
                return cleanPhone.replace(/(\d{3})(?=\d)/g, '$1 ');
            }
        }

        return phone; // Return original if no pattern matches
    },

    // Clean phone number for storage
    cleanPhone(phone) {
        if (!phone) return '';

        // Remove all non-digit characters except +
        let cleaned = phone.replace(/[^\d\+]/g, '');

        // Handle different formats
        if (cleaned.startsWith('+966')) {
            // Convert +966512345678 to 0512345678
            cleaned = '0' + cleaned.substring(4);
        } else if (cleaned.startsWith('966')) {
            // Convert 966512345678 to 0512345678
            cleaned = '0' + cleaned.substring(3);
        } else if (cleaned.length === 9 && cleaned.startsWith('5')) {
            // Convert 512345678 to 0512345678
            cleaned = '0' + cleaned;
        }

        return cleaned;
    },

    // Validate email
    validateEmail(email) {
        if (!email) return null; // Email is optional
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return 'البريد الإلكتروني غير صحيح';
        }
        
        return null;
    },

    // Validate amount
    validateAmount(amount) {
        if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
            return 'المبلغ يجب أن يكون رقم موجب';
        }
        return null;
    },

    // Validate membership ID
    validateMembershipId(membershipId) {
        if (!membershipId) return 'رقم العضوية مطلوب';
        
        if (membershipId.length < 3) {
            return 'رقم العضوية يجب أن يكون 3 أحرف على الأقل';
        }
        
        return null;
    }
};

// UI utilities
const UIUtils = {
    // Show loading spinner
    showLoading() {
        document.getElementById('loading-spinner').classList.remove('d-none');
    },

    // Hide loading spinner
    hideLoading() {
        document.getElementById('loading-spinner').classList.add('d-none');
    },

    // Show notification
    showNotification(message, type = 'success', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification fade show`;
        notification.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },

    // Show confirmation dialog
    async showConfirmDialog(title, message, confirmText = 'تأكيد', cancelText = 'إلغاء') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${cancelText}</button>
                            <button type="button" class="btn btn-danger" id="confirm-btn">${confirmText}</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
            
            modal.querySelector('#confirm-btn').addEventListener('click', () => {
                bootstrapModal.hide();
                resolve(true);
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
                resolve(false);
            });
        });
    },

    // Format status badge
    formatStatusBadge(status) {
        const statusMap = {
            'active': { class: 'bg-success', text: 'نشط' },
            'inactive': { class: 'bg-danger', text: 'غير نشط' },
            'pending': { class: 'bg-warning', text: 'معلق' },
            'overdue': { class: 'bg-danger', text: 'متأخر' }
        };
        
        const statusInfo = statusMap[status] || { class: 'bg-secondary', text: status };
        return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
    },

    // Generate membership ID
    generateMembershipId() {
        const prefix = 'M';
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.floor(Math.random() * 100).toString().padStart(2, '0');
        return `${prefix}${timestamp}${random}`;
    },

    // Debounce function for search
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Export data to CSV
    exportToCSV(data, filename) {
        if (!data || data.length === 0) {
            this.showNotification('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    },

    // Print content
    printContent(elementId, title = 'طباعة') {
        const content = document.getElementById(elementId);
        if (!content) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <link href="../../node_modules/bootstrap/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
                <link href="css/styles.css" rel="stylesheet">
                <style>
                    body { font-family: 'Noto Sans Arabic', sans-serif; }
                    @media print {
                        .no-print { display: none !important; }
                    }
                </style>
            </head>
            <body>
                <div class="container-fluid">
                    <div class="text-center mb-4">
                        <h2>${title}</h2>
                        <p>تاريخ الطباعة: ${DateUtils.getCurrentDateArabic()}</p>
                    </div>
                    ${content.innerHTML}
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }
};

// File utilities
const FileUtils = {
    // Read file as base64
    readFileAsBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    },

    // Validate file type
    validateFileType(file, allowedTypes) {
        return allowedTypes.includes(file.type);
    },

    // Validate file size (in MB)
    validateFileSize(file, maxSizeMB) {
        return file.size <= maxSizeMB * 1024 * 1024;
    },

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};

// Make utilities available globally
window.DateUtils = DateUtils;
window.NumberUtils = NumberUtils;
window.ValidationUtils = ValidationUtils;
window.UIUtils = UIUtils;
window.FileUtils = FileUtils;
