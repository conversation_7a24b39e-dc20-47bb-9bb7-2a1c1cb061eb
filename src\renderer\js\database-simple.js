// Simple database module for Electron
// This module provides a fallback when the main database fails

class SimpleDatabase {
    constructor() {
        this.isReady = false;
        this.data = {
            members: [],
            payments: [],
            expenses: [],
            revenue_categories: [
                { id: 1, name: 'اشتراك شهري', description: 'الاشتراك الشهري للأعضاء' },
                { id: 2, name: 'تبرعات', description: 'التبرعات المختلفة' },
                { id: 3, name: 'دعم الفعاليات', description: 'دعم الفعاليات والأنشطة' },
                { id: 4, name: 'رسوم خدمات', description: 'رسوم الخدمات المختلفة' }
            ],
            expense_categories: [
                { id: 1, name: 'فعاليات', description: 'مصروفات الفعاليات والأنشطة' },
                { id: 2, name: 'خدما<PERSON>', description: 'مصروفات الخدمات المقدمة' },
                { id: 3, name: 'دعم الحالات', description: 'دعم الحالات المحتاجة' },
                { id: 4, name: 'فواتير', description: 'الفواتير والمصروفات الإدارية' },
                { id: 5, name: 'صيانة', description: 'مصروفات الصيانة والتطوير' }
            ],
            settings: [
                { key: 'currency', value: 'د.أ', description: 'العملة المستخدمة' },
                { key: 'organization_name', value: 'الديوان', description: 'اسم المنظمة' },
                { key: 'monthly_subscription', value: '25.000', description: 'قيمة الاشتراك الشهري' }
            ]
        };
        this.nextId = {
            members: 1,
            payments: 1,
            expenses: 1,
            revenue_categories: 5,
            expense_categories: 6,
            settings: 4
        };
        
        this.init();
    }

    async init() {
        try {
            console.log('🔄 Initializing simple database...');

            // Load data from localStorage if available
            this.loadFromStorage();

            // Add some sample data if empty
            this.ensureSampleData();

            this.isReady = true;

            // Dispatch ready event immediately and after a delay
            window.dispatchEvent(new CustomEvent('database-ready'));

            setTimeout(() => {
                window.dispatchEvent(new CustomEvent('database-ready'));
                console.log('✅ Simple database is ready and loaded');
            }, 50);

        } catch (error) {
            console.error('❌ Simple database initialization failed:', error);
            // Even if there's an error, mark as ready with default data
            this.isReady = true;
            window.dispatchEvent(new CustomEvent('database-ready'));
        }
    }

    loadFromStorage() {
        try {
            const stored = localStorage.getItem('dewan_database');
            if (stored) {
                const parsedData = JSON.parse(stored);
                this.data = { ...this.data, ...parsedData };
                console.log('📊 Data loaded from localStorage');
            }
        } catch (error) {
            console.error('Error loading from storage:', error);
        }
    }

    saveToStorage() {
        try {
            localStorage.setItem('dewan_database', JSON.stringify(this.data));
            console.log('💾 Data saved to localStorage');
        } catch (error) {
            console.error('Error saving to storage:', error);
        }
    }

    ensureSampleData() {
        // Add sample member if no members exist
        if (this.data.members.length === 0) {
            const sampleMember = {
                id: 1,
                membership_id: 'M001',
                full_name: 'أحمد محمد علي',
                phone: '00962791234567',
                email: '<EMAIL>',
                address: 'عمان، الأردن',
                join_date: '2024-01-01',
                status: 'active',
                notes: 'عضو مؤسس',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            this.data.members.push(sampleMember);
            this.nextId.members = 2;
        }

        // Add sample payment if no payments exist
        if (this.data.payments.length === 0 && this.data.members.length > 0) {
            const samplePayment = {
                id: 1,
                member_id: 1,
                category_id: 1,
                amount: 25.000,
                payment_date: '2024-01-01',
                payment_method: 'cash',
                reference_number: 'PAY001',
                notes: 'دفعة تجريبية',
                created_at: new Date().toISOString()
            };
            this.data.payments.push(samplePayment);
            this.nextId.payments = 2;
        }

        // Add sample expense if no expenses exist
        if (this.data.expenses.length === 0) {
            const sampleExpense = {
                id: 1,
                category_id: 1,
                amount: 10.000,
                expense_date: '2024-01-01',
                description: 'مصروف تجريبي',
                receipt_path: '',
                notes: 'مصروف للاختبار',
                created_at: new Date().toISOString()
            };
            this.data.expenses.push(sampleExpense);
            this.nextId.expenses = 2;
        }

        console.log('📊 Sample data ensured');
    }

    async run(sql, params = []) {
        console.log('Simple DB run:', sql, params);
        
        // Parse simple SQL commands
        const sqlLower = sql.toLowerCase().trim();
        
        if (sqlLower.startsWith('insert')) {
            return this.handleInsert(sql, params);
        } else if (sqlLower.startsWith('update')) {
            return this.handleUpdate(sql, params);
        } else if (sqlLower.startsWith('delete')) {
            return this.handleDelete(sql, params);
        } else if (sqlLower.includes('pragma')) {
            return { id: 0, changes: 0 };
        } else if (sqlLower.includes('create table')) {
            return { id: 0, changes: 0 };
        }
        
        return { id: 0, changes: 0 };
    }

    async get(sql, params = []) {
        console.log('Simple DB get:', sql, params);
        
        const sqlLower = sql.toLowerCase().trim();
        
        if (sqlLower.includes('sqlite_master')) {
            return { count: 6 }; // Simulate 6 tables
        }
        
        // Handle simple SELECT queries
        if (sqlLower.startsWith('select')) {
            return this.handleSelect(sql, params, true);
        }
        
        return null;
    }

    async all(sql, params = []) {
        console.log('Simple DB all:', sql, params);
        
        const sqlLower = sql.toLowerCase().trim();
        
        if (sqlLower.startsWith('select')) {
            return this.handleSelect(sql, params, false);
        }
        
        return [];
    }

    handleSelect(sql, params, single = false) {
        const sqlLower = sql.toLowerCase();

        try {
            // Members queries
            if (sqlLower.includes('from members')) {
                let results = this.data.members.map(member => ({
                    ...member,
                    total_paid: this.calculateMemberTotal(member.id),
                    payment_count: this.countMemberPayments(member.id),
                    last_payment_date: this.getLastPaymentDate(member.id)
                }));

                // Handle WHERE clauses
                if (sqlLower.includes('where') && params.length > 0) {
                    if (sqlLower.includes('id =')) {
                        results = results.filter(m => m.id == params[0]);
                    } else if (sqlLower.includes('membership_id =')) {
                        results = results.filter(m => m.membership_id == params[0]);
                    }
                }

                return single ? (results[0] || null) : results;
            }

            // Payments queries with JOIN support
            if (sqlLower.includes('from payments')) {
                let results = this.data.payments.map(payment => {
                    const member = this.data.members.find(m => m.id === payment.member_id);
                    const category = this.data.revenue_categories.find(c => c.id === payment.category_id);

                    return {
                        ...payment,
                        member_name: member ? member.full_name : 'غير معروف',
                        member_phone: member ? member.phone : '',
                        category_name: category ? category.name : 'غير محدد'
                    };
                });

                // Handle WHERE clauses
                if (sqlLower.includes('where') && params.length > 0) {
                    if (sqlLower.includes('member_id =')) {
                        results = results.filter(p => p.member_id == params[0]);
                    } else if (sqlLower.includes('id =')) {
                        results = results.filter(p => p.id == params[0]);
                    }
                }

                return single ? (results[0] || null) : results;
            }

            // Expenses queries with JOIN support
            if (sqlLower.includes('from expenses')) {
                let results = this.data.expenses.map(expense => {
                    const category = this.data.expense_categories.find(c => c.id === expense.category_id);

                    return {
                        ...expense,
                        category_name: category ? category.name : 'غير محدد'
                    };
                });

                // Handle WHERE clauses
                if (sqlLower.includes('where') && params.length > 0) {
                    if (sqlLower.includes('id =')) {
                        results = results.filter(e => e.id == params[0]);
                    }
                }

                return single ? (results[0] || null) : results;
            }

            // Categories queries
            if (sqlLower.includes('from revenue_categories')) {
                let results = [...this.data.revenue_categories];
                if (sqlLower.includes('where is_active = 1')) {
                    results = results.filter(c => c.is_active !== false);
                }
                return single ? (results[0] || null) : results;
            }

            if (sqlLower.includes('from expense_categories')) {
                let results = [...this.data.expense_categories];
                if (sqlLower.includes('where is_active = 1')) {
                    results = results.filter(c => c.is_active !== false);
                }
                return single ? (results[0] || null) : results;
            }

            // Settings queries
            if (sqlLower.includes('from settings')) {
                let results = [...this.data.settings];
                if (sqlLower.includes('where') && params.length > 0) {
                    if (sqlLower.includes('key =')) {
                        results = results.filter(s => s.key === params[0]);
                    }
                }
                return single ? (results[0] || null) : results;
            }

            // Aggregate queries
            if (sqlLower.includes('count(*)') || sqlLower.includes('sum(')) {
                if (sqlLower.includes('from payments')) {
                    if (sqlLower.includes('sum(amount)')) {
                        const total = this.data.payments.reduce((sum, p) => sum + parseFloat(p.amount || 0), 0);
                        return single ? { total: total } : [{ total: total }];
                    } else {
                        return single ? { count: this.data.payments.length } : [{ count: this.data.payments.length }];
                    }
                } else if (sqlLower.includes('from expenses')) {
                    if (sqlLower.includes('sum(amount)')) {
                        const total = this.data.expenses.reduce((sum, e) => sum + parseFloat(e.amount || 0), 0);
                        return single ? { total: total } : [{ total: total }];
                    } else {
                        return single ? { count: this.data.expenses.length } : [{ count: this.data.expenses.length }];
                    }
                } else if (sqlLower.includes('from members')) {
                    return single ? { count: this.data.members.length } : [{ count: this.data.members.length }];
                }
            }

            console.log('⚠️ Unhandled query:', sql);
            return single ? null : [];

        } catch (error) {
            console.error('Error in handleSelect:', error, sql);
            return single ? null : [];
        }
    }

    handleInsert(sql, params) {
        const sqlLower = sql.toLowerCase();
        
        if (sqlLower.includes('into members')) {
            const member = {
                id: this.nextId.members++,
                membership_id: params[0] || `M${String(this.nextId.members).padStart(3, '0')}`,
                full_name: params[1] || '',
                phone: params[2] || '',
                email: params[3] || '',
                address: params[4] || '',
                join_date: params[5] || new Date().toISOString().split('T')[0],
                status: params[6] || 'active',
                notes: params[7] || '',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            this.data.members.push(member);
            this.saveToStorage();
            return { id: member.id, changes: 1 };
        }
        
        if (sqlLower.includes('into payments')) {
            const payment = {
                id: this.nextId.payments++,
                member_id: params[0],
                category_id: params[1],
                amount: params[2],
                payment_date: params[3] || new Date().toISOString().split('T')[0],
                payment_method: params[4] || 'cash',
                reference_number: params[5] || '',
                notes: params[6] || '',
                created_at: new Date().toISOString()
            };
            this.data.payments.push(payment);
            this.saveToStorage();
            return { id: payment.id, changes: 1 };
        }
        
        if (sqlLower.includes('into expenses')) {
            const expense = {
                id: this.nextId.expenses++,
                category_id: params[0],
                amount: params[1],
                expense_date: params[2] || new Date().toISOString().split('T')[0],
                description: params[3] || '',
                receipt_path: params[4] || '',
                notes: params[5] || '',
                created_at: new Date().toISOString()
            };
            this.data.expenses.push(expense);
            this.saveToStorage();
            return { id: expense.id, changes: 1 };
        }
        
        return { id: 0, changes: 0 };
    }

    handleUpdate(sql, params) {
        // Simple update handling
        this.saveToStorage();
        return { id: 0, changes: 1 };
    }

    handleDelete(sql, params) {
        // Simple delete handling
        this.saveToStorage();
        return { id: 0, changes: 1 };
    }

    calculateMemberTotal(memberId) {
        return this.data.payments
            .filter(p => p.member_id === memberId)
            .reduce((total, p) => total + parseFloat(p.amount || 0), 0);
    }

    countMemberPayments(memberId) {
        return this.data.payments.filter(p => p.member_id === memberId).length;
    }

    getLastPaymentDate(memberId) {
        const payments = this.data.payments
            .filter(p => p.member_id === memberId)
            .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date));
        return payments.length > 0 ? payments[0].payment_date : null;
    }

    async close() {
        this.saveToStorage();
        console.log('🔒 Simple database closed');
    }

    async backup(backupPath) {
        try {
            const data = JSON.stringify(this.data, null, 2);
            // In a real implementation, this would save to file
            console.log('💾 Simple database backup created (localStorage)');
            return backupPath;
        } catch (error) {
            throw new Error('Backup failed: ' + error.message);
        }
    }
}

// Create simple database instance
console.log('📦 Creating simple database...');
const simpleDatabase = new SimpleDatabase();

// Attach to window
if (typeof window !== 'undefined') {
    window.database = simpleDatabase;
    window.databaseModuleLoaded = true;
    
    console.log('✅ Simple database attached to window');
    
    // Dispatch events
    window.dispatchEvent(new CustomEvent('database-module-loaded', {
        detail: { database: simpleDatabase }
    }));
}

// Export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = simpleDatabase;
}
