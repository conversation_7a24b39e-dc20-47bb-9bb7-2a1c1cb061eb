/* Arabic Font and R<PERSON> Styling */
body {
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
    background-color: #f8f9fa;
}

/* Navigation Styling */
.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

/* Card Styling */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    border: none;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
}

.card.bg-success {
    background: linear-gradient(135deg, var(--bs-success) 0%, #157347 100%);
}

.card.bg-info {
    background: linear-gradient(135deg, var(--bs-info) 0%, #087990 100%);
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning) 0%, #997404 100%);
}

/* Button Styling */
.btn {
    font-weight: 500;
    border-radius: 0.5rem;
    padding: 0.5rem 1.25rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%);
    border: none;
}

/* Form Styling */
.form-control,
.form-select {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

/* Table Styling */
.table {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody td {
    padding: 0.875rem 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badge Styling */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 500;
}

/* Modal Styling */
.modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1.25rem;
}

.modal-title {
    font-weight: 600;
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
}

/* Loading Spinner */
#loading-spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
}

/* Page Transitions */
.page {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search and Filter Styling */
.search-container {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Status Indicators */
.status-active {
    color: #198754;
    font-weight: 600;
}

.status-inactive {
    color: #dc3545;
    font-weight: 600;
}

.status-overdue {
    color: #fd7e14;
    font-weight: 600;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.75rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background-color: white;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* File Upload Styling */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #0d6efd;
    background-color: #e7f1ff;
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f1ff;
    transform: scale(1.02);
}

/* Notification Styling */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    border-radius: 0.75rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Arabic Number Styling */
.arabic-numbers {
    font-feature-settings: "lnum";
    font-variant-numeric: lining-nums;
}
