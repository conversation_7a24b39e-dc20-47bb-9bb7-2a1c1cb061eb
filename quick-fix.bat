@echo off
echo ========================================
echo    إصلاح سريع - Quick Fix
echo ========================================
echo.

echo 🔧 إصلاح مشاكل قاعدة البيانات...

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تشغيل setup.bat أولاً
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

echo 🗄️ حذف قاعدة البيانات القديمة...
if exist "%USERPROFILE%\dewan-data\dewan.db" del "%USERPROFILE%\dewan-data\dewan.db"
if exist "%USERPROFILE%\dewan-data\*.db-journal" del "%USERPROFILE%\dewan-data\*.db-journal"
if exist "%USERPROFILE%\dewan-data\*.db-wal" del "%USERPROFILE%\dewan-data\*.db-wal"
if exist "%USERPROFILE%\dewan-data\*.db-shm" del "%USERPROFILE%\dewan-data\*.db-shm"

echo 📦 إعادة تثبيت التبعيات...
npm install

echo 🔄 إعادة إنشاء قاعدة البيانات...
node reset-database.js

echo 🚀 تشغيل التطبيق...
npm start

pause
