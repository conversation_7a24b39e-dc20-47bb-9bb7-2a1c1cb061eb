// Test Reports Data - اختبار بيانات التقارير
// This script tests and fixes reports data issues

console.log('🧪 Starting reports data testing...');

// Function to test revenue data
async function testRevenueData() {
    console.log('💰 Testing revenue data...');
    
    try {
        const revenueData = await window.database.all(`
            SELECT 
                DATE(payment_date) as date,
                SUM(amount) as total,
                COUNT(*) as count,
                AVG(amount) as average
            FROM payments 
            WHERE payment_date >= date('now', '-30 days')
            GROUP BY DATE(payment_date)
            ORDER BY payment_date
        `);
        
        console.log('✅ Revenue data query successful');
        console.log('📊 Revenue records found:', revenueData.length);
        
        if (revenueData.length > 0) {
            const totalRevenue = revenueData.reduce((sum, item) => {
                const amount = parseFloat(item.total || 0);
                return sum + (isNaN(amount) ? 0 : amount);
            }, 0);
            
            console.log('💰 Total revenue calculated:', totalRevenue);
            console.log('📅 Date range:', revenueData[0]?.date, 'to', revenueData[revenueData.length - 1]?.date);
            
            // Show sample data
            console.log('📋 Sample revenue data:');
            revenueData.slice(0, 3).forEach(item => {
                console.log(`  - ${item.date}: ${item.total} (${item.count} transactions)`);
            });
        } else {
            console.log('⚠️ No revenue data found in the last 30 days');
        }
        
        return revenueData;
        
    } catch (error) {
        console.error('❌ Error testing revenue data:', error);
        return [];
    }
}

// Function to test expense data
async function testExpenseData() {
    console.log('💸 Testing expense data...');
    
    try {
        let expenseData = [];
        try {
            expenseData = await window.database.all(`
                SELECT 
                    DATE(expense_date) as date,
                    SUM(amount) as total,
                    COUNT(*) as count,
                    AVG(amount) as average
                FROM expenses 
                WHERE expense_date >= date('now', '-30 days')
                GROUP BY DATE(expense_date)
                ORDER BY expense_date
            `);
            
            console.log('✅ Expense data query successful');
            console.log('📊 Expense records found:', expenseData.length);
            
            if (expenseData.length > 0) {
                const totalExpenses = expenseData.reduce((sum, item) => {
                    const amount = parseFloat(item.total || 0);
                    return sum + (isNaN(amount) ? 0 : amount);
                }, 0);
                
                console.log('💸 Total expenses calculated:', totalExpenses);
                console.log('📅 Date range:', expenseData[0]?.date, 'to', expenseData[expenseData.length - 1]?.date);
                
                // Show sample data
                console.log('📋 Sample expense data:');
                expenseData.slice(0, 3).forEach(item => {
                    console.log(`  - ${item.date}: ${item.total} (${item.count} transactions)`);
                });
            } else {
                console.log('⚠️ No expense data found in the last 30 days');
            }
            
        } catch (error) {
            console.log('⚠️ Expenses table not found or empty, using empty data');
            expenseData = [];
        }
        
        return expenseData;
        
    } catch (error) {
        console.error('❌ Error testing expense data:', error);
        return [];
    }
}

// Function to test financial calculations
async function testFinancialCalculations() {
    console.log('🧮 Testing financial calculations...');
    
    try {
        const revenueData = await testRevenueData();
        const expenseData = await testExpenseData();
        
        // Calculate totals
        const totalRevenue = revenueData.reduce((sum, item) => {
            const amount = parseFloat(item.total || 0);
            return sum + (isNaN(amount) ? 0 : amount);
        }, 0);
        
        const totalExpenses = expenseData.reduce((sum, item) => {
            const amount = parseFloat(item.total || 0);
            return sum + (isNaN(amount) ? 0 : amount);
        }, 0);
        
        const netIncome = totalRevenue - totalExpenses;
        
        console.log('📊 Financial Summary:');
        console.log(`  💰 Total Revenue: ${totalRevenue.toFixed(3)} د.أ`);
        console.log(`  💸 Total Expenses: ${totalExpenses.toFixed(3)} د.أ`);
        console.log(`  📈 Net Income: ${netIncome.toFixed(3)} د.أ`);
        console.log(`  📊 Status: ${netIncome >= 0 ? 'Profitable ✅' : 'Loss ❌'}`);
        
        // Test NumberUtils formatting
        if (typeof NumberUtils !== 'undefined') {
            console.log('🎨 Formatted values:');
            console.log(`  💰 Revenue: ${NumberUtils.formatCurrency(totalRevenue)}`);
            console.log(`  💸 Expenses: ${NumberUtils.formatCurrency(totalExpenses)}`);
            console.log(`  📈 Net Income: ${NumberUtils.formatCurrency(netIncome)}`);
        } else {
            console.log('⚠️ NumberUtils not available for formatting');
        }
        
        return {
            totalRevenue,
            totalExpenses,
            netIncome,
            revenueData,
            expenseData
        };
        
    } catch (error) {
        console.error('❌ Error testing financial calculations:', error);
        return null;
    }
}

// Function to create sample data if none exists
async function createSampleRevenueData() {
    console.log('📊 Creating sample revenue data...');
    
    try {
        // Check if we have any payments
        const existingPayments = await window.database.get('SELECT COUNT(*) as count FROM payments');
        
        if (existingPayments.count > 0) {
            console.log('✅ Revenue data already exists');
            return true;
        }
        
        // Check if we have members
        const members = await window.database.all('SELECT id FROM members LIMIT 5');
        
        if (members.length === 0) {
            console.log('⚠️ No members found, cannot create sample revenue data');
            return false;
        }
        
        // Get revenue categories
        const categories = await window.database.all('SELECT id FROM revenue_categories LIMIT 3');
        
        if (categories.length === 0) {
            console.log('⚠️ No revenue categories found, cannot create sample revenue data');
            return false;
        }
        
        // Create sample payments for the last 30 days
        const samplePayments = [];
        const today = new Date();
        
        for (let i = 0; i < 30; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            
            // Random number of payments per day (0-3)
            const paymentsPerDay = Math.floor(Math.random() * 4);
            
            for (let j = 0; j < paymentsPerDay; j++) {
                const member = members[Math.floor(Math.random() * members.length)];
                const category = categories[Math.floor(Math.random() * categories.length)];
                const amount = 25.000; // Standard subscription
                
                samplePayments.push({
                    member_id: member.id,
                    category_id: category.id,
                    amount: amount,
                    payment_date: date.toISOString().split('T')[0],
                    payment_method: 'cash'
                });
            }
        }
        
        // Insert sample payments
        for (const payment of samplePayments) {
            await window.database.run(`
                INSERT INTO payments (member_id, category_id, amount, payment_date, payment_method)
                VALUES (?, ?, ?, ?, ?)
            `, [payment.member_id, payment.category_id, payment.amount, payment.payment_date, payment.payment_method]);
        }
        
        console.log(`✅ Created ${samplePayments.length} sample payments`);
        return true;
        
    } catch (error) {
        console.error('❌ Error creating sample revenue data:', error);
        return false;
    }
}

// Function to test reports functionality
async function testReportsFullFunctionality() {
    console.log('🔬 Testing full reports functionality...');
    
    try {
        // Test data availability
        const financialData = await testFinancialCalculations();
        
        if (!financialData) {
            console.log('⚠️ No financial data available, creating sample data...');
            const sampleCreated = await createSampleRevenueData();
            
            if (sampleCreated) {
                console.log('✅ Sample data created, retesting...');
                return await testFinancialCalculations();
            } else {
                console.log('❌ Could not create sample data');
                return null;
            }
        }
        
        // Test reports manager
        if (window.ReportsManager) {
            console.log('✅ ReportsManager is available');
            
            // Test loading a report
            try {
                const today = new Date();
                const thirtyDaysAgo = new Date(today);
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                
                const dateFrom = thirtyDaysAgo.toISOString().split('T')[0];
                const dateTo = today.toISOString().split('T')[0];
                
                console.log(`📅 Testing report for period: ${dateFrom} to ${dateTo}`);
                
                await window.ReportsManager.loadFinancialReport(dateFrom, dateTo);
                console.log('✅ Financial report loaded successfully');
                
            } catch (error) {
                console.error('❌ Error loading financial report:', error);
            }
            
        } else {
            console.log('⚠️ ReportsManager not available');
        }
        
        return financialData;
        
    } catch (error) {
        console.error('❌ Error testing full reports functionality:', error);
        return null;
    }
}

// Main test function
async function testAllReportsData() {
    console.log('🚀 Starting comprehensive reports data test...');
    
    try {
        // Wait for database to be ready
        if (!window.database || !window.database.isReady) {
            console.log('⏳ Waiting for database to be ready...');
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Database ready timeout'));
                }, 10000);
                
                const checkReady = () => {
                    if (window.database && window.database.isReady) {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                
                checkReady();
            });
        }
        
        // Run tests
        const result = await testReportsFullFunctionality();
        
        if (result) {
            console.log('🎉 All reports data tests completed successfully!');
            console.log('📋 Summary:');
            console.log(`   • Revenue: ${result.totalRevenue.toFixed(3)} د.أ`);
            console.log(`   • Expenses: ${result.totalExpenses.toFixed(3)} د.أ`);
            console.log(`   • Net Income: ${result.netIncome.toFixed(3)} د.أ`);
            console.log(`   • Status: ${result.netIncome >= 0 ? 'Profitable' : 'Loss'}`);
        } else {
            console.log('⚠️ Some tests failed, but basic functionality should work');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Reports data test failed:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.testAllReportsData = testAllReportsData;
    window.testFinancialCalculations = testFinancialCalculations;
    window.testRevenueData = testRevenueData;
    window.testExpenseData = testExpenseData;
    window.createSampleRevenueData = createSampleRevenueData;
}

// Auto-run if database is ready
if (typeof window !== 'undefined') {
    console.log('🎯 Reports data test functions available:');
    console.log('- testAllReportsData()');
    console.log('- testFinancialCalculations()');
    console.log('- testRevenueData()');
    console.log('- testExpenseData()');
    console.log('- createSampleRevenueData()');
    console.log('');
    console.log('🚀 To test all reports data: testAllReportsData()');
}
