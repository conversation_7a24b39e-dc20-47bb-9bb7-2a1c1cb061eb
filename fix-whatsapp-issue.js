// Fix WhatsApp loading issues

console.log('🔧 WhatsApp Fix Script');
console.log('='.repeat(50));

// Function to check WhatsApp module status
function checkWhatsAppStatus() {
    console.log('🔍 Checking WhatsApp module status...');
    
    const status = {
        whatsappManager: !!window.WhatsAppManager,
        database: !!window.database,
        databaseReady: window.database && window.database.isReady,
        whatsappPage: !!document.getElementById('whatsapp-page')
    };
    
    console.log('WhatsApp Status:', status);
    
    let allReady = true;
    for (const [key, value] of Object.entries(status)) {
        if (!value) {
            console.error(`❌ ${key} not ready`);
            allReady = false;
        } else {
            console.log(`✅ ${key} ready`);
        }
    }
    
    return { allReady, status };
}

// Function to test WhatsApp database tables
async function testWhatsAppTables() {
    console.log('🔍 Testing WhatsApp database tables...');
    
    try {
        if (!window.database || !window.database.db) {
            throw new Error('Database not available');
        }
        
        // Test whatsapp_messages table
        const messagesCount = await window.database.get('SELECT COUNT(*) as count FROM whatsapp_messages');
        console.log(`✅ WhatsApp messages table: ${messagesCount.count} records`);
        
        // Test whatsapp_config table (if exists)
        try {
            const configCount = await window.database.get('SELECT COUNT(*) as count FROM whatsapp_config');
            console.log(`✅ WhatsApp config table: ${configCount.count} records`);
        } catch (e) {
            console.log('⚠️ WhatsApp config table not found, will create if needed');
        }
        
        // Test complex query (same as used in WhatsApp)
        const testQuery = await window.database.all(`
            SELECT wm.*, 
                   m.full_name as member_name,
                   m.membership_id
            FROM whatsapp_messages wm
            LEFT JOIN members m ON wm.member_id = m.id
            ORDER BY wm.created_at DESC
            LIMIT 5
        `);
        console.log(`✅ Complex WhatsApp query: ${testQuery.length} records`);
        
        return true;
    } catch (error) {
        console.error('❌ WhatsApp database tables test failed:', error);
        return false;
    }
}

// Function to create WhatsApp config table if missing
async function createWhatsAppConfigTable() {
    console.log('🔧 Creating WhatsApp config table...');
    
    try {
        await window.database.run(`
            CREATE TABLE IF NOT EXISTS whatsapp_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                api_url TEXT,
                api_token TEXT,
                instance_id TEXT,
                webhook_url TEXT,
                is_active BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);
        
        console.log('✅ WhatsApp config table created');
        return true;
    } catch (error) {
        console.error('❌ Error creating WhatsApp config table:', error);
        return false;
    }
}

// Function to fix WhatsApp loading
async function fixWhatsAppLoading() {
    console.log('🚀 Starting WhatsApp loading fix...');
    
    try {
        // Step 1: Check current status
        const { allReady, status } = checkWhatsAppStatus();
        
        if (!status.database) {
            throw new Error('Database module not loaded');
        }
        
        if (!status.databaseReady) {
            console.log('⏳ Waiting for database to be ready...');
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Database ready timeout'));
                }, 10000);
                
                const checkReady = () => {
                    if (window.database && window.database.isReady) {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                
                checkReady();
            });
        }
        
        // Step 2: Test database tables
        const tablesOk = await testWhatsAppTables();
        if (!tablesOk) {
            console.warn('⚠️ Some database tables had issues, but continuing...');
        }
        
        // Step 3: Create config table if needed
        await createWhatsAppConfigTable();
        
        // Step 4: Check WhatsAppManager
        if (!window.WhatsAppManager) {
            console.log('🔧 WhatsAppManager not found, creating...');
            // Try to recreate WhatsAppManager
            if (typeof WhatsAppManager !== 'undefined') {
                window.WhatsAppManager = new WhatsAppManager();
                console.log('✅ WhatsAppManager recreated');
            } else {
                throw new Error('WhatsAppManager class not available');
            }
        }
        
        // Step 5: Try to load messages
        console.log('🔄 Attempting to load WhatsApp messages...');
        await window.WhatsAppManager.loadMessages();
        console.log('✅ WhatsApp messages loaded successfully');
        
        return true;
        
    } catch (error) {
        console.error('❌ WhatsApp loading fix failed:', error);
        return false;
    }
}

// Function to reload WhatsApp after fix
async function reloadWhatsAppAfterFix() {
    console.log('🔄 Reloading WhatsApp data...');
    
    try {
        if (window.WhatsAppManager && typeof window.WhatsAppManager.loadMessages === 'function') {
            await window.WhatsAppManager.loadMessages();
            console.log('✅ WhatsApp data reloaded successfully');
        } else {
            console.log('⚠️ WhatsAppManager not available');
        }
    } catch (error) {
        console.error('❌ Error reloading WhatsApp:', error);
    }
}

// Function to create sample WhatsApp data for testing
async function createSampleWhatsAppData() {
    console.log('🔧 Creating sample WhatsApp data...');
    
    try {
        // Check if we have members
        const membersCount = await window.database.get('SELECT COUNT(*) as count FROM members');
        
        if (membersCount.count === 0) {
            console.log('⚠️ No members found, cannot create sample WhatsApp messages');
            return false;
        }
        
        // Get first member
        const member = await window.database.get('SELECT id, phone FROM members LIMIT 1');
        
        // Check if sample message already exists
        const existingMessage = await window.database.get('SELECT COUNT(*) as count FROM whatsapp_messages');
        
        if (existingMessage.count === 0) {
            // Create sample WhatsApp message
            await window.database.run(`
                INSERT INTO whatsapp_messages (member_id, phone_number, message_type, message_content, status)
                VALUES (?, ?, ?, ?, ?)
            `, [
                member.id,
                member.phone || '0501234567',
                'announcement',
                'رسالة تجريبية من نظام إدارة الديوان',
                'sent'
            ]);
            
            console.log('✅ Sample WhatsApp message created');
            return true;
        } else {
            console.log('✅ WhatsApp messages already exist');
            return true;
        }
    } catch (error) {
        console.error('❌ Error creating sample WhatsApp data:', error);
        return false;
    }
}

// Function to test WhatsApp configuration
async function testWhatsAppConfig() {
    console.log('🔍 Testing WhatsApp configuration...');
    
    try {
        // Check if config table exists and has data
        const config = await window.database.get('SELECT * FROM whatsapp_config WHERE is_active = 1 LIMIT 1');
        
        if (!config) {
            console.log('⚠️ No active WhatsApp configuration found');
            return false;
        }
        
        console.log('✅ WhatsApp configuration found:', {
            hasApiUrl: !!config.api_url,
            hasApiToken: !!config.api_token,
            hasInstanceId: !!config.instance_id,
            isActive: config.is_active
        });
        
        return true;
    } catch (error) {
        console.error('❌ Error testing WhatsApp config:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.checkWhatsAppStatus = checkWhatsAppStatus;
    window.testWhatsAppTables = testWhatsAppTables;
    window.createWhatsAppConfigTable = createWhatsAppConfigTable;
    window.fixWhatsAppLoading = fixWhatsAppLoading;
    window.reloadWhatsAppAfterFix = reloadWhatsAppAfterFix;
    window.createSampleWhatsAppData = createSampleWhatsAppData;
    window.testWhatsAppConfig = testWhatsAppConfig;
    
    console.log('🎯 WhatsApp fix functions available:');
    console.log('- checkWhatsAppStatus()');
    console.log('- testWhatsAppTables()');
    console.log('- createWhatsAppConfigTable()');
    console.log('- fixWhatsAppLoading()');
    console.log('- reloadWhatsAppAfterFix()');
    console.log('- createSampleWhatsAppData()');
    console.log('- testWhatsAppConfig()');
}

// Auto-run fix if WhatsApp is not working
setTimeout(() => {
    if (typeof window !== 'undefined') {
        const { allReady } = checkWhatsAppStatus();
        if (!allReady) {
            console.log('🔧 Auto-running WhatsApp loading fix...');
            fixWhatsAppLoading().then((success) => {
                if (success) {
                    reloadWhatsAppAfterFix();
                }
            });
        }
    }
}, 7000); // Wait longer for WhatsApp module
