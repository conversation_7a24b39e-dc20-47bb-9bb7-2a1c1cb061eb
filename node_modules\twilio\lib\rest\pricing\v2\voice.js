"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Pricing
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const country_1 = require("./voice/country");
const number_1 = require("./voice/number");
function VoiceListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Voice`;
    Object.defineProperty(instance, "countries", {
        get: function countries() {
            if (!instance._countries) {
                instance._countries = (0, country_1.CountryListInstance)(instance._version);
            }
            return instance._countries;
        },
    });
    Object.defineProperty(instance, "numbers", {
        get: function numbers() {
            if (!instance._numbers) {
                instance._numbers = (0, number_1.NumberListInstance)(instance._version);
            }
            return instance._numbers;
        },
    });
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.VoiceListInstance = VoiceListInstance;
