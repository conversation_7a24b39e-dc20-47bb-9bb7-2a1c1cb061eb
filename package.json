{"name": "dewan-management", "version": "1.0.0", "description": "نظام إدارة الديوان - Community Organization Management System", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "reset-db": "node reset-database.js", "fresh-start": "npm run reset-db && npm start"}, "keywords": ["electron", "community", "management", "arabic", "membership", "finance"], "author": "Community Organization", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"sqlite3": "^5.1.6", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.2", "chart.js": "^4.4.0", "jspdf": "^2.5.1", "xlsx": "^0.18.5", "moment": "^2.29.4", "moment-hijri": "^2.1.2", "twilio": "^4.19.0", "node-cron": "^3.0.3", "archiver": "^6.0.1", "multer": "^1.4.5-lts.1"}, "build": {"appId": "com.dewan.management", "productName": "نظام إدارة الديوان", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة الديوان"}}}