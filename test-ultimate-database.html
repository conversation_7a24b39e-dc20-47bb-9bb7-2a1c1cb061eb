<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Ultimate Database</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(255,255,255,0.2);
        }
        .test-section h3 {
            color: #ffd700;
            margin-top: 0;
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            font-weight: bold;
            text-align: center;
        }
        .success {
            background: rgba(40, 167, 69, 0.9);
            border: 2px solid #28a745;
        }
        .error {
            background: rgba(220, 53, 69, 0.9);
            border: 2px solid #dc3545;
        }
        .info {
            background: rgba(23, 162, 184, 0.9);
            border: 2px solid #17a2b8;
        }
        .warning {
            background: rgba(255, 193, 7, 0.9);
            border: 2px solid #ffc107;
            color: #212529;
        }
        .data-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .data-item:last-child {
            border-bottom: none;
        }
        .data-card {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #ffd700;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .button-container {
            text-align: center;
            margin-top: 30px;
        }
        .test-log {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار Ultimate Database الشامل</h1>
        
        <div id="main-status" class="status info">⏳ جاري تحميل قاعدة البيانات...</div>
        
        <div class="test-grid">
            <div class="test-section">
                <h3>🔧 حالة قاعدة البيانات</h3>
                <div id="database-status"></div>
                <div class="test-log" id="database-log"></div>
            </div>
            
            <div class="test-section">
                <h3>👥 اختبار الأعضاء</h3>
                <div id="members-status"></div>
                <div id="members-data"></div>
            </div>
            
            <div class="test-section">
                <h3>💰 اختبار المدفوعات</h3>
                <div id="payments-status"></div>
                <div id="payments-data"></div>
            </div>
            
            <div class="test-section">
                <h3>💸 اختبار المصروفات</h3>
                <div id="expenses-status"></div>
                <div id="expenses-data"></div>
            </div>
            
            <div class="test-section">
                <h3>📂 اختبار الفئات</h3>
                <div id="categories-status"></div>
                <div id="categories-data"></div>
            </div>
            
            <div class="test-section">
                <h3>⚙️ اختبار العمليات</h3>
                <div id="operations-status"></div>
                <div id="operations-data"></div>
            </div>
        </div>
        
        <div class="button-container">
            <button onclick="runFullTest()">🔄 اختبار شامل</button>
            <button onclick="testOperations()">⚙️ اختبار العمليات</button>
            <button onclick="addTestData()">➕ إضافة بيانات تجريبية</button>
            <button onclick="clearAllData()">🗑️ مسح جميع البيانات</button>
            <button onclick="exportData()">📤 تصدير البيانات</button>
        </div>
    </div>

    <!-- Load Ultimate Database -->
    <script src="src/renderer/js/database-ultimate.js"></script>

    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('database-log');
            if (logElement) {
                logElement.innerHTML = testLog.slice(-10).join('\n');
                logElement.scrollTop = logElement.scrollHeight;
            }
            
            console.log(logEntry);
        }

        async function runFullTest() {
            const mainStatus = document.getElementById('main-status');
            
            try {
                mainStatus.innerHTML = '⏳ جاري تشغيل الاختبار الشامل...';
                mainStatus.className = 'status info';
                
                log('🔄 بدء الاختبار الشامل');

                // Test 1: Database Status
                await testDatabaseStatus();
                
                // Test 2: Members
                await testMembers();
                
                // Test 3: Payments
                await testPayments();
                
                // Test 4: Expenses
                await testExpenses();
                
                // Test 5: Categories
                await testCategories();
                
                // Test 6: Operations
                await testCRUDOperations();

                mainStatus.innerHTML = '✅ جميع الاختبارات نجحت بامتياز!';
                mainStatus.className = 'status success';
                log('🎉 جميع الاختبارات نجحت');

            } catch (error) {
                mainStatus.innerHTML = `❌ فشل في الاختبار: ${error.message}`;
                mainStatus.className = 'status error';
                log(`❌ خطأ في الاختبار: ${error.message}`, 'error');
            }
        }

        async function testDatabaseStatus() {
            log('🔍 اختبار حالة قاعدة البيانات');
            
            const statusDiv = document.getElementById('database-status');
            
            if (!window.database) {
                throw new Error('قاعدة البيانات غير محملة');
            }

            if (!window.database.isReady) {
                throw new Error('قاعدة البيانات غير جاهزة');
            }

            if (!window.database.initialized) {
                throw new Error('قاعدة البيانات غير مهيأة');
            }

            const stats = window.database.getStats();
            
            statusDiv.innerHTML = `
                <div class="data-item"><span>الحالة:</span><span>✅ جاهزة</span></div>
                <div class="data-item"><span>مهيأة:</span><span>✅ نعم</span></div>
                <div class="data-item"><span>الأعضاء:</span><span>${stats.members.total}</span></div>
                <div class="data-item"><span>المدفوعات:</span><span>${stats.payments.total}</span></div>
                <div class="data-item"><span>المصروفات:</span><span>${stats.expenses.total}</span></div>
                <div class="data-item"><span>التخزين:</span><span>localStorage</span></div>
            `;
            
            log('✅ قاعدة البيانات تعمل بشكل صحيح');
        }

        async function testMembers() {
            log('🔍 اختبار الأعضاء');
            
            const statusDiv = document.getElementById('members-status');
            const dataDiv = document.getElementById('members-data');
            
            try {
                const members = await window.database.all('SELECT * FROM members');
                
                statusDiv.innerHTML = '<div class="status success">✅ تم تحميل الأعضاء بنجاح</div>';
                
                if (members.length > 0) {
                    dataDiv.innerHTML = members.map(member => `
                        <div class="data-card">
                            <div class="data-item"><strong>الاسم:</strong> <span>${member.full_name}</span></div>
                            <div class="data-item"><strong>رقم العضوية:</strong> <span>${member.membership_id}</span></div>
                            <div class="data-item"><strong>الحالة:</strong> <span>${member.status === 'active' ? '✅ نشط' : '❌ غير نشط'}</span></div>
                        </div>
                    `).join('');
                } else {
                    dataDiv.innerHTML = '<div class="data-item">لا يوجد أعضاء</div>';
                }
                
                log(`✅ تم تحميل ${members.length} عضو`);
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ خطأ: ${error.message}</div>`;
                log(`❌ خطأ في تحميل الأعضاء: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testPayments() {
            log('🔍 اختبار المدفوعات');
            
            const statusDiv = document.getElementById('payments-status');
            const dataDiv = document.getElementById('payments-data');
            
            try {
                const payments = await window.database.all('SELECT * FROM payments');
                
                statusDiv.innerHTML = '<div class="status success">✅ تم تحميل المدفوعات بنجاح</div>';
                
                if (payments.length > 0) {
                    dataDiv.innerHTML = payments.map(payment => {
                        const member = window.database.data.members.find(m => m.id === payment.member_id);
                        return `
                            <div class="data-card">
                                <div class="data-item"><strong>العضو:</strong> <span>${member ? member.full_name : 'غير معروف'}</span></div>
                                <div class="data-item"><strong>المبلغ:</strong> <span>${payment.amount} د.أ</span></div>
                                <div class="data-item"><strong>التاريخ:</strong> <span>${payment.payment_date}</span></div>
                            </div>
                        `;
                    }).join('');
                } else {
                    dataDiv.innerHTML = '<div class="data-item">لا يوجد مدفوعات</div>';
                }
                
                log(`✅ تم تحميل ${payments.length} دفعة`);
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ خطأ: ${error.message}</div>`;
                log(`❌ خطأ في تحميل المدفوعات: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testExpenses() {
            log('🔍 اختبار المصروفات');
            
            const statusDiv = document.getElementById('expenses-status');
            const dataDiv = document.getElementById('expenses-data');
            
            try {
                const expenses = await window.database.all('SELECT * FROM expenses');
                
                statusDiv.innerHTML = '<div class="status success">✅ تم تحميل المصروفات بنجاح</div>';
                
                if (expenses.length > 0) {
                    dataDiv.innerHTML = expenses.map(expense => `
                        <div class="data-card">
                            <div class="data-item"><strong>الوصف:</strong> <span>${expense.description}</span></div>
                            <div class="data-item"><strong>المبلغ:</strong> <span>${expense.amount} د.أ</span></div>
                            <div class="data-item"><strong>التاريخ:</strong> <span>${expense.expense_date}</span></div>
                        </div>
                    `).join('');
                } else {
                    dataDiv.innerHTML = '<div class="data-item">لا يوجد مصروفات</div>';
                }
                
                log(`✅ تم تحميل ${expenses.length} مصروف`);
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ خطأ: ${error.message}</div>`;
                log(`❌ خطأ في تحميل المصروفات: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testCategories() {
            log('🔍 اختبار الفئات');
            
            const statusDiv = document.getElementById('categories-status');
            const dataDiv = document.getElementById('categories-data');
            
            try {
                const revenueCategories = await window.database.all('SELECT * FROM revenue_categories');
                const expenseCategories = await window.database.all('SELECT * FROM expense_categories');
                
                statusDiv.innerHTML = '<div class="status success">✅ تم تحميل الفئات بنجاح</div>';
                
                dataDiv.innerHTML = `
                    <div class="data-item"><strong>فئات الإيرادات:</strong> <span>${revenueCategories.length}</span></div>
                    <div class="data-item"><strong>فئات المصروفات:</strong> <span>${expenseCategories.length}</span></div>
                `;
                
                log(`✅ تم تحميل ${revenueCategories.length} فئة إيرادات و ${expenseCategories.length} فئة مصروفات`);
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ خطأ: ${error.message}</div>`;
                log(`❌ خطأ في تحميل الفئات: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testCRUDOperations() {
            log('🔍 اختبار العمليات CRUD');
            
            const statusDiv = document.getElementById('operations-status');
            const dataDiv = document.getElementById('operations-data');
            
            try {
                // Test INSERT
                const insertResult = await window.database.run(
                    'INSERT INTO members (membership_id, full_name, phone, address, join_date, status, notes) VALUES (?, ?, ?, ?, ?, ?, ?)',
                    ['TEST001', 'عضو اختبار', '00962791234567', 'عمان', '2024-01-01', 'active', 'اختبار']
                );
                
                // Test SELECT
                const testMember = await window.database.get('SELECT * FROM members WHERE membership_id = ?', ['TEST001']);
                
                // Test UPDATE
                await window.database.run('UPDATE members SET notes = ? WHERE id = ?', ['تم التحديث', testMember.id]);
                
                // Test DELETE
                await window.database.run('DELETE FROM members WHERE id = ?', [testMember.id]);
                
                statusDiv.innerHTML = '<div class="status success">✅ جميع العمليات نجحت</div>';
                dataDiv.innerHTML = `
                    <div class="data-item"><strong>إضافة:</strong> <span>✅ نجح</span></div>
                    <div class="data-item"><strong>قراءة:</strong> <span>✅ نجح</span></div>
                    <div class="data-item"><strong>تحديث:</strong> <span>✅ نجح</span></div>
                    <div class="data-item"><strong>حذف:</strong> <span>✅ نجح</span></div>
                `;
                
                log('✅ جميع عمليات CRUD نجحت');
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ خطأ: ${error.message}</div>`;
                log(`❌ خطأ في العمليات: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testOperations() {
            await testCRUDOperations();
        }

        function addTestData() {
            // Add test member
            const newMember = {
                id: window.database.nextId.members++,
                membership_id: `M${String(window.database.nextId.members).padStart(3, '0')}`,
                full_name: 'عضو تجريبي جديد',
                phone: '00962791234569',
                email: '<EMAIL>',
                address: 'الزرقاء، الأردن',
                join_date: new Date().toISOString().split('T')[0],
                status: 'active',
                notes: 'تم إضافته من الاختبار',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            window.database.data.members.push(newMember);
            window.database.saveData();
            
            log('➕ تم إضافة عضو تجريبي جديد');
            alert('تم إضافة عضو تجريبي جديد!');
            runFullTest();
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('dewan_ultimate_db');
                log('🗑️ تم مسح جميع البيانات');
                location.reload();
            }
        }

        function exportData() {
            const data = JSON.stringify(window.database.data, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dewan-backup-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📤 تم تصدير البيانات');
            alert('تم تصدير البيانات بنجاح!');
        }

        // Auto-run test when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🚀 بدء الاختبار التلقائي');
                runFullTest();
            }, 1000);
        });
    </script>
</body>
</html>
