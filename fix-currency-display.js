// Fix Currency Display - إصلاح عرض العملة
// This script ensures all currency displays show Jordanian Dinar

console.log('💰 Starting currency display fix...');

// Function to update all currency displays to JOD
function updateAllCurrencyDisplays() {
    console.log('🔄 Updating all currency displays to Jordanian Dinar...');
    
    try {
        // Update dashboard statistics
        const monthlyRevenue = document.getElementById('monthly-revenue');
        if (monthlyRevenue && monthlyRevenue.textContent.includes('ر.س')) {
            monthlyRevenue.textContent = monthlyRevenue.textContent.replace('ر.س', 'د.أ');
            console.log('✅ Updated monthly revenue display');
        }
        
        const overdueAmount = document.getElementById('overdue-amount');
        if (overdueAmount && overdueAmount.textContent.includes('ر.س')) {
            overdueAmount.textContent = overdueAmount.textContent.replace('ر.س', 'د.أ');
            console.log('✅ Updated overdue amount display');
        }
        
        // Update payments page statistics
        const totalRevenue = document.getElementById('total-revenue');
        if (totalRevenue && totalRevenue.textContent.includes('ر.س')) {
            totalRevenue.textContent = totalRevenue.textContent.replace('ر.س', 'د.أ');
            console.log('✅ Updated total revenue display');
        }
        
        const avgPayment = document.getElementById('avg-payment');
        if (avgPayment && avgPayment.textContent.includes('ر.س')) {
            avgPayment.textContent = avgPayment.textContent.replace('ر.س', 'د.أ');
            console.log('✅ Updated average payment display');
        }
        
        // Update expenses page statistics
        const totalExpenses = document.getElementById('total-expenses');
        if (totalExpenses && totalExpenses.textContent.includes('ر.س')) {
            totalExpenses.textContent = totalExpenses.textContent.replace('ر.س', 'د.أ');
            console.log('✅ Updated total expenses display');
        }
        
        const monthlyExpenses = document.getElementById('monthly-expenses');
        if (monthlyExpenses && monthlyExpenses.textContent.includes('ر.س')) {
            monthlyExpenses.textContent = monthlyExpenses.textContent.replace('ر.س', 'د.أ');
            console.log('✅ Updated monthly expenses display');
        }
        
        const avgExpense = document.getElementById('avg-expense');
        if (avgExpense && avgExpense.textContent.includes('ر.س')) {
            avgExpense.textContent = avgExpense.textContent.replace('ر.س', 'د.أ');
            console.log('✅ Updated average expense display');
        }
        
        // Update all input group texts
        const inputGroupTexts = document.querySelectorAll('.input-group-text');
        inputGroupTexts.forEach(element => {
            if (element.textContent.includes('ر.س')) {
                element.textContent = element.textContent.replace('ر.س', 'د.أ');
                console.log('✅ Updated input group currency symbol');
            }
        });
        
        // Update all currency displays in tables
        const currencyElements = document.querySelectorAll('[data-currency], .currency');
        currencyElements.forEach(element => {
            if (element.textContent.includes('ر.س')) {
                element.textContent = element.textContent.replace('ر.س', 'د.أ');
                console.log('✅ Updated table currency display');
            }
        });
        
        console.log('✅ All currency displays updated to Jordanian Dinar');
        return true;
        
    } catch (error) {
        console.error('❌ Error updating currency displays:', error);
        return false;
    }
}

// Function to set default currency values
function setDefaultCurrencyValues() {
    console.log('🔧 Setting default currency values...');
    
    try {
        // Set default values for dashboard
        const monthlyRevenue = document.getElementById('monthly-revenue');
        if (monthlyRevenue && (monthlyRevenue.textContent === '0 ر.س' || monthlyRevenue.textContent.trim() === '0')) {
            monthlyRevenue.textContent = '0.000 د.أ';
        }
        
        const overdueAmount = document.getElementById('overdue-amount');
        if (overdueAmount && (overdueAmount.textContent === '0 ر.س' || overdueAmount.textContent.trim() === '0')) {
            overdueAmount.textContent = '0.000 د.أ';
        }
        
        // Set default values for payments page
        const totalRevenue = document.getElementById('total-revenue');
        if (totalRevenue && (totalRevenue.textContent === '0 ر.س' || totalRevenue.textContent.trim() === '0')) {
            totalRevenue.textContent = '0.000 د.أ';
        }
        
        const avgPayment = document.getElementById('avg-payment');
        if (avgPayment && (avgPayment.textContent === '0 ر.س' || avgPayment.textContent.trim() === '0')) {
            avgPayment.textContent = '0.000 د.أ';
        }
        
        // Set default values for expenses page
        const totalExpenses = document.getElementById('total-expenses');
        if (totalExpenses && (totalExpenses.textContent === '0 ر.س' || totalExpenses.textContent.trim() === '0')) {
            totalExpenses.textContent = '0.000 د.أ';
        }
        
        const monthlyExpenses = document.getElementById('monthly-expenses');
        if (monthlyExpenses && (monthlyExpenses.textContent === '0 ر.س' || monthlyExpenses.textContent.trim() === '0')) {
            monthlyExpenses.textContent = '0.000 د.أ';
        }
        
        const avgExpense = document.getElementById('avg-expense');
        if (avgExpense && (avgExpense.textContent === '0 ر.س' || avgExpense.textContent.trim() === '0')) {
            avgExpense.textContent = '0.000 د.أ';
        }
        
        console.log('✅ Default currency values set');
        return true;
        
    } catch (error) {
        console.error('❌ Error setting default currency values:', error);
        return false;
    }
}

// Function to update currency symbols in forms
function updateFormCurrencySymbols() {
    console.log('📝 Updating form currency symbols...');
    
    try {
        // Update all input group texts that show currency
        const inputGroupTexts = document.querySelectorAll('.input-group-text');
        inputGroupTexts.forEach(element => {
            if (element.textContent === 'ر.س' || element.textContent === 'SAR') {
                element.textContent = 'د.أ';
                console.log('✅ Updated form currency symbol');
            }
        });
        
        // Update placeholder texts
        const amountInputs = document.querySelectorAll('input[type="number"][placeholder*="ر.س"], input[type="number"][placeholder*="SAR"]');
        amountInputs.forEach(input => {
            if (input.placeholder.includes('ر.س')) {
                input.placeholder = input.placeholder.replace('ر.س', 'د.أ');
            }
            if (input.placeholder.includes('SAR')) {
                input.placeholder = input.placeholder.replace('SAR', 'JOD');
            }
        });
        
        console.log('✅ Form currency symbols updated');
        return true;
        
    } catch (error) {
        console.error('❌ Error updating form currency symbols:', error);
        return false;
    }
}

// Function to monitor and auto-fix currency displays
function startCurrencyMonitoring() {
    console.log('👁️ Starting currency display monitoring...');
    
    // Create a mutation observer to watch for changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' || mutation.type === 'characterData') {
                // Check if any new content contains SAR currency
                const target = mutation.target;
                if (target.textContent && target.textContent.includes('ر.س')) {
                    target.textContent = target.textContent.replace(/ر\.س/g, 'د.أ');
                    console.log('🔄 Auto-fixed currency display');
                }
            }
        });
    });
    
    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });
    
    console.log('✅ Currency monitoring started');
    return observer;
}

// Main function to fix all currency displays
function fixAllCurrencyDisplays() {
    console.log('🚀 Starting comprehensive currency display fix...');
    
    try {
        // Step 1: Update existing displays
        const displaysUpdated = updateAllCurrencyDisplays();
        
        // Step 2: Set default values
        const defaultsSet = setDefaultCurrencyValues();
        
        // Step 3: Update form symbols
        const formsUpdated = updateFormCurrencySymbols();
        
        // Step 4: Start monitoring
        const observer = startCurrencyMonitoring();
        
        if (displaysUpdated && defaultsSet && formsUpdated) {
            console.log('🎉 Currency display fix completed successfully!');
            console.log('📋 Summary:');
            console.log('   • Existing displays: Updated to د.أ');
            console.log('   • Default values: Set to د.أ format');
            console.log('   • Form symbols: Updated to د.أ');
            console.log('   • Auto-monitoring: Active');
            
            // Store observer globally for cleanup if needed
            window.currencyObserver = observer;
            
            return true;
        } else {
            console.log('⚠️ Some currency fixes may have failed, but continuing...');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Currency display fix failed:', error);
        return false;
    }
}

// Function to refresh statistics with correct currency
function refreshStatisticsWithJOD() {
    console.log('📊 Refreshing statistics with JOD currency...');
    
    try {
        // Trigger refresh of payments statistics
        if (window.PaymentsManager && typeof window.PaymentsManager.loadStatistics === 'function') {
            window.PaymentsManager.loadStatistics();
            console.log('✅ Payments statistics refreshed');
        }
        
        // Trigger refresh of expenses statistics
        if (window.ExpensesManager && typeof window.ExpensesManager.loadStatistics === 'function') {
            window.ExpensesManager.loadStatistics();
            console.log('✅ Expenses statistics refreshed');
        }
        
        // Trigger refresh of dashboard statistics
        if (window.DashboardManager && typeof window.DashboardManager.loadDashboard === 'function') {
            window.DashboardManager.loadDashboard();
            console.log('✅ Dashboard statistics refreshed');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Error refreshing statistics:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.fixAllCurrencyDisplays = fixAllCurrencyDisplays;
    window.updateAllCurrencyDisplays = updateAllCurrencyDisplays;
    window.setDefaultCurrencyValues = setDefaultCurrencyValues;
    window.updateFormCurrencySymbols = updateFormCurrencySymbols;
    window.refreshStatisticsWithJOD = refreshStatisticsWithJOD;
}

// Auto-run when page loads
if (typeof window !== 'undefined') {
    console.log('🎯 Currency display fix functions available:');
    console.log('- fixAllCurrencyDisplays()');
    console.log('- updateAllCurrencyDisplays()');
    console.log('- setDefaultCurrencyValues()');
    console.log('- refreshStatisticsWithJOD()');
    console.log('');
    console.log('🚀 To fix all currency displays: fixAllCurrencyDisplays()');
    
    // Auto-run after a delay to ensure page is loaded
    setTimeout(() => {
        fixAllCurrencyDisplays();
        
        // Refresh statistics after fixing displays
        setTimeout(() => {
            refreshStatisticsWithJOD();
        }, 1000);
    }, 2000);
}
