// WhatsApp integration module

class WhatsAppManager {
    constructor() {
        this.messages = [];
        this.filteredMessages = [];
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.searchTerm = '';
        this.statusFilter = 'all';
        this.typeFilter = 'all';
        this.isConfigured = false;
        
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
            this.checkConfiguration();
        });
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('whatsapp-search');
        if (searchInput) {
            searchInput.addEventListener('input', UIUtils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.filterMessages();
            }, 300));
        }

        // Filters
        const statusFilter = document.getElementById('message-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value;
                this.filterMessages();
            });
        }

        const typeFilter = document.getElementById('message-type-filter');
        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.typeFilter = e.target.value;
                this.filterMessages();
            });
        }
    }

    async loadMessages() {
        try {
            UIUtils.showLoading();

            // Wait for database module to be loaded
            await this.waitForDatabaseModule();

            // Check if database is available
            if (!window.database || !window.database.db) {
                throw new Error('قاعدة البيانات غير متاحة');
            }

            // Load WhatsApp page content if not already loaded
            await this.loadWhatsAppPageContent();

            // Check configuration
            await this.checkConfiguration();

            // Fetch messages from database with error handling
            try {
                await this.fetchMessages();
            } catch (dbError) {
                console.error('Database fetch error:', dbError);
                // Set empty messages if fetch fails
                this.messages = [];
            }

            this.filteredMessages = [...this.messages];
            this.renderMessages();
            this.updateMessageStats();

        } catch (error) {
            console.error('Error loading WhatsApp messages:', error);
            UIUtils.showNotification(`خطأ في تحميل بيانات الواتساب: ${error.message}`, 'danger');

            // Show empty state
            this.messages = [];
            this.filteredMessages = [];
            this.renderMessages();
            this.updateMessageStats();
        } finally {
            UIUtils.hideLoading();
        }
    }

    async waitForDatabaseModule() {
        console.log('🔍 Waiting for database module (WhatsApp)...');

        // If already loaded, return immediately
        if (window.database && window.databaseModuleLoaded) {
            console.log('✅ Database module already loaded (WhatsApp)');
            return;
        }

        // Wait for database module to load
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Database module loading timeout (WhatsApp)'));
            }, 10000); // 10 second timeout

            const checkDatabase = () => {
                if (window.database && window.databaseModuleLoaded) {
                    clearTimeout(timeout);
                    console.log('✅ Database module loaded successfully (WhatsApp)');
                    resolve();
                } else {
                    setTimeout(checkDatabase, 100);
                }
            };

            // Also listen for the database module loaded event
            window.addEventListener('database-module-loaded', () => {
                clearTimeout(timeout);
                console.log('✅ Database module loaded via event (WhatsApp)');
                resolve();
            }, { once: true });

            checkDatabase();
        });
    }

    async checkConfiguration() {
        try {
            const apiKeySetting = await database.get(
                'SELECT value FROM settings WHERE key = "whatsapp_api_key"'
            );
            
            this.isConfigured = apiKeySetting && apiKeySetting.value && apiKeySetting.value.trim() !== '';
            
            // Update UI based on configuration status
            this.updateConfigurationStatus();
        } catch (error) {
            console.error('Error checking WhatsApp configuration:', error);
            this.isConfigured = false;
        }
    }

    updateConfigurationStatus() {
        const configStatus = document.getElementById('whatsapp-config-status');
        const configAlert = document.getElementById('whatsapp-config-alert');
        
        if (configStatus) {
            if (this.isConfigured) {
                configStatus.innerHTML = '<span class="badge bg-success">مُكوَّن</span>';
            } else {
                configStatus.innerHTML = '<span class="badge bg-warning">غير مُكوَّن</span>';
            }
        }
        
        if (configAlert) {
            configAlert.style.display = this.isConfigured ? 'none' : 'block';
        }
    }

    async fetchMessages() {
        this.messages = await database.all(`
            SELECT wm.*, 
                   m.full_name as member_name,
                   m.membership_id
            FROM whatsapp_messages wm
            LEFT JOIN members m ON wm.member_id = m.id
            ORDER BY wm.created_at DESC
        `);
    }

    async loadWhatsAppPageContent() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        const pageElement = document.getElementById('whatsapp-page');
        if (!pageElement) {
            console.error('WhatsApp page element not found, available elements:',
                Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            throw new Error('عنصر صفحة الواتساب غير موجود في HTML');
        }

        if (pageElement.innerHTML.trim()) {
            console.log('WhatsApp page content already loaded');
            return;
        }

        pageElement.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="bi bi-whatsapp"></i>
                            إدارة الواتساب
                            <span id="whatsapp-config-status" class="ms-2"></span>
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="window.WhatsAppManager.showSendMessageModal()">
                                <i class="bi bi-send"></i>
                                إرسال رسالة
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.WhatsAppManager.showConfigModal()">
                                <i class="bi bi-gear"></i>
                                الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Alert -->
            <div id="whatsapp-config-alert" class="alert alert-warning" style="display: none;">
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle fs-4 me-3"></i>
                    <div>
                        <h5 class="alert-heading">تكوين الواتساب مطلوب</h5>
                        <p class="mb-2">يجب تكوين إعدادات الواتساب أولاً لتتمكن من إرسال الرسائل.</p>
                        <button class="btn btn-warning btn-sm" onclick="window.WhatsAppManager.showConfigModal()">
                            <i class="bi bi-gear"></i>
                            تكوين الآن
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-messages">0</h4>
                                    <p class="mb-0">إجمالي الرسائل</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-chat-dots fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="sent-messages">0</h4>
                                    <p class="mb-0">رسائل مُرسلة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-check-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="pending-messages">0</h4>
                                    <p class="mb-0">رسائل معلقة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-clock fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="failed-messages">0</h4>
                                    <p class="mb-0">رسائل فاشلة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-x-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="bi bi-bell text-warning fs-1"></i>
                            <h5 class="mt-2">تذكيرات الدفع</h5>
                            <p class="text-muted">إرسال تذكيرات للأعضاء المتأخرين</p>
                            <button class="btn btn-warning" onclick="window.WhatsAppManager.sendPaymentReminders()">
                                <i class="bi bi-send"></i>
                                إرسال التذكيرات
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="bi bi-megaphone text-info fs-1"></i>
                            <h5 class="mt-2">إعلان جماعي</h5>
                            <p class="text-muted">إرسال إعلان لجميع الأعضاء النشطين</p>
                            <button class="btn btn-info" onclick="window.WhatsAppManager.showBroadcastModal()">
                                <i class="bi bi-broadcast"></i>
                                إرسال إعلان
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="bi bi-check-circle text-success fs-1"></i>
                            <h5 class="mt-2">تأكيدات الدفع</h5>
                            <p class="text-muted">إرسال تأكيدات تلقائية للمدفوعات</p>
                            <div class="form-check form-switch d-flex justify-content-center">
                                <input class="form-check-input" type="checkbox" id="auto-payment-confirmations">
                                <label class="form-check-label ms-2" for="auto-payment-confirmations">
                                    تفعيل التأكيدات التلقائية
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="whatsapp-search" class="form-label">البحث</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="whatsapp-search" 
                                       placeholder="البحث في الرسائل...">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="message-status-filter" class="form-label">حالة الرسالة</label>
                            <select class="form-select" id="message-status-filter">
                                <option value="all">جميع الحالات</option>
                                <option value="sent">مُرسلة</option>
                                <option value="pending">معلقة</option>
                                <option value="failed">فاشلة</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="message-type-filter" class="form-label">نوع الرسالة</label>
                            <select class="form-select" id="message-type-filter">
                                <option value="all">جميع الأنواع</option>
                                <option value="payment_confirmation">تأكيد دفع</option>
                                <option value="reminder">تذكير</option>
                                <option value="announcement">إعلان</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2 d-flex align-items-end">
                            <button class="btn btn-outline-secondary w-100" onclick="window.WhatsAppManager.clearFilters()">
                                <i class="bi bi-x-circle"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">سجل الرسائل</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المستقبل</th>
                                    <th>نوع الرسالة</th>
                                    <th>المحتوى</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="whatsapp-messages-table-body">
                                <!-- Messages will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center" id="whatsapp-messages-pagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        `;

        // Set up event listeners after content is loaded
        setTimeout(() => this.setupEventListeners(), 100);
    }

    filterMessages() {
        this.filteredMessages = this.messages.filter(message => {
            const matchesSearch = !this.searchTerm ||
                message.message_content.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                (message.member_name && message.member_name.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
                message.phone_number.includes(this.searchTerm);

            const matchesStatus = this.statusFilter === 'all' || message.status === this.statusFilter;
            const matchesType = this.typeFilter === 'all' || message.message_type === this.typeFilter;

            return matchesSearch && matchesStatus && matchesType;
        });

        this.currentPage = 1;
        this.renderMessages();
    }

    renderMessages() {
        const tbody = document.getElementById('whatsapp-messages-table-body');
        if (!tbody) return;

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedMessages = this.filteredMessages.slice(startIndex, endIndex);

        if (paginatedMessages.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="bi bi-chat-dots fs-1 d-block mb-2"></i>
                        لا توجد رسائل مطابقة للبحث
                    </td>
                </tr>
            `;
        } else {
            tbody.innerHTML = paginatedMessages.map(message => `
                <tr>
                    <td>${DateUtils.formatDate(message.created_at)}</td>
                    <td>
                        ${message.member_name ? `
                            <strong>${message.member_name}</strong><br>
                            <small class="text-muted">${message.membership_id}</small>
                        ` : `
                            <span class="text-muted">رقم مباشر</span>
                        `}<br>
                        <small class="text-info">${message.phone_number}</small>
                    </td>
                    <td>${this.getMessageTypeText(message.message_type)}</td>
                    <td>
                        <div class="message-preview" style="max-width: 200px;">
                            ${message.message_content.length > 50 ?
                                message.message_content.substring(0, 50) + '...' :
                                message.message_content}
                        </div>
                    </td>
                    <td>${this.getStatusBadge(message.status)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.WhatsAppManager.viewMessage(${message.id})" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            ${message.status === 'failed' ? `
                                <button class="btn btn-outline-warning" onclick="window.WhatsAppManager.resendMessage(${message.id})" title="إعادة إرسال">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        this.renderPagination();
    }

    renderPagination() {
        const pagination = document.getElementById('whatsapp-messages-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredMessages.length / this.itemsPerPage);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.WhatsAppManager.goToPage(${this.currentPage - 1})">السابق</a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="window.WhatsAppManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.WhatsAppManager.goToPage(${this.currentPage + 1})">التالي</a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredMessages.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderMessages();
        }
    }

    updateMessageStats() {
        const totalCount = this.messages.length;
        const sentCount = this.messages.filter(m => m.status === 'sent').length;
        const pendingCount = this.messages.filter(m => m.status === 'pending').length;
        const failedCount = this.messages.filter(m => m.status === 'failed').length;

        document.getElementById('total-messages').textContent = NumberUtils.formatNumber(totalCount);
        document.getElementById('sent-messages').textContent = NumberUtils.formatNumber(sentCount);
        document.getElementById('pending-messages').textContent = NumberUtils.formatNumber(pendingCount);
        document.getElementById('failed-messages').textContent = NumberUtils.formatNumber(failedCount);
    }

    getMessageTypeText(type) {
        const types = {
            'payment_confirmation': 'تأكيد دفع',
            'reminder': 'تذكير',
            'announcement': 'إعلان'
        };
        return types[type] || type;
    }

    getStatusBadge(status) {
        const statusMap = {
            'sent': { class: 'bg-success', text: 'مُرسلة' },
            'pending': { class: 'bg-warning', text: 'معلقة' },
            'failed': { class: 'bg-danger', text: 'فاشلة' }
        };

        const statusInfo = statusMap[status] || { class: 'bg-secondary', text: status };
        return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
    }

    clearFilters() {
        document.getElementById('whatsapp-search').value = '';
        document.getElementById('message-status-filter').value = 'all';
        document.getElementById('message-type-filter').value = 'all';

        this.searchTerm = '';
        this.statusFilter = 'all';
        this.typeFilter = 'all';

        this.filterMessages();
    }

    showConfigModal() {
        const modalId = `whatsapp-config-modal-${Date.now()}`;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-gear"></i>
                            إعدادات الواتساب
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">معلومات مهمة</h6>
                            <p class="mb-0">
                                لاستخدام ميزة الواتساب، تحتاج إلى الحصول على مفتاح API من خدمة مثل Twilio أو WhatsApp Business API.
                                يمكنك أيضاً استخدام خدمات أخرى متوافقة.
                            </p>
                        </div>

                        <form id="whatsapp-config-form">
                            <div class="mb-3">
                                <label for="whatsapp-api-key" class="form-label">مفتاح API</label>
                                <input type="password" class="form-control" id="whatsapp-api-key"
                                       placeholder="أدخل مفتاح API الخاص بك">
                                <div class="form-text">سيتم تشفير وحفظ المفتاح بشكل آمن</div>
                            </div>

                            <div class="mb-3">
                                <label for="whatsapp-api-url" class="form-label">رابط API</label>
                                <input type="url" class="form-control" id="whatsapp-api-url"
                                       placeholder="https://api.twilio.com/2010-04-01/"
                                       value="https://api.twilio.com/2010-04-01/">
                            </div>

                            <div class="mb-3">
                                <label for="whatsapp-sender-number" class="form-label">رقم المرسل</label>
                                <input type="tel" class="form-control" id="whatsapp-sender-number"
                                       placeholder="+966xxxxxxxxx">
                                <div class="form-text">رقم الواتساب المُسجل في الخدمة</div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-auto-confirmations">
                                    <label class="form-check-label" for="enable-auto-confirmations">
                                        تفعيل تأكيدات الدفع التلقائية
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-monthly-reminders">
                                    <label class="form-check-label" for="enable-monthly-reminders">
                                        تفعيل التذكيرات الشهرية التلقائية
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="window.WhatsAppManager.testConnection('${modalId}')">
                            <i class="bi bi-wifi"></i>
                            اختبار الاتصال
                        </button>
                        <button type="button" class="btn btn-primary" onclick="window.WhatsAppManager.saveConfig('${modalId}')">
                            <i class="bi bi-check-lg"></i>
                            حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Load current settings
        this.loadCurrentSettings(modalId);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    async loadCurrentSettings(modalId) {
        try {
            const settings = await database.all('SELECT key, value FROM settings WHERE key LIKE "whatsapp_%"');

            settings.forEach(setting => {
                const input = document.getElementById(setting.key.replace('_', '-'));
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = setting.value === '1' || setting.value === 'true';
                    } else {
                        input.value = setting.value || '';
                    }
                }
            });
        } catch (error) {
            console.error('Error loading WhatsApp settings:', error);
        }
    }

    async saveConfig(modalId) {
        try {
            const apiKey = document.getElementById('whatsapp-api-key').value.trim();
            const apiUrl = document.getElementById('whatsapp-api-url').value.trim();
            const senderNumber = document.getElementById('whatsapp-sender-number').value.trim();
            const autoConfirmations = document.getElementById('enable-auto-confirmations').checked;
            const monthlyReminders = document.getElementById('enable-monthly-reminders').checked;

            if (!apiKey) {
                UIUtils.showNotification('مفتاح API مطلوب', 'warning');
                return;
            }

            UIUtils.showLoading();

            // Save settings to database
            const settings = [
                ['whatsapp_api_key', apiKey],
                ['whatsapp_api_url', apiUrl],
                ['whatsapp_sender_number', senderNumber],
                ['whatsapp_auto_confirmations', autoConfirmations ? '1' : '0'],
                ['whatsapp_monthly_reminders', monthlyReminders ? '1' : '0']
            ];

            for (const [key, value] of settings) {
                await database.run(`
                    INSERT OR REPLACE INTO settings (key, value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                `, [key, value]);
            }

            UIUtils.showNotification('تم حفظ إعدادات الواتساب بنجاح', 'success');

            // Close modal and refresh configuration status
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            modal.hide();

            await this.checkConfiguration();

        } catch (error) {
            console.error('Error saving WhatsApp config:', error);
            UIUtils.showNotification('خطأ في حفظ الإعدادات', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    async testConnection(modalId) {
        UIUtils.showNotification('سيتم تطوير اختبار الاتصال قريباً', 'info');
    }

    showSendMessageModal() {
        if (!this.isConfigured) {
            UIUtils.showNotification('يجب تكوين إعدادات الواتساب أولاً', 'warning');
            this.showConfigModal();
            return;
        }

        UIUtils.showNotification('سيتم تطوير إرسال الرسائل قريباً', 'info');
    }

    showBroadcastModal() {
        if (!this.isConfigured) {
            UIUtils.showNotification('يجب تكوين إعدادات الواتساب أولاً', 'warning');
            this.showConfigModal();
            return;
        }

        UIUtils.showNotification('سيتم تطوير الإرسال الجماعي قريباً', 'info');
    }

    async sendPaymentReminders() {
        if (!this.isConfigured) {
            UIUtils.showNotification('يجب تكوين إعدادات الواتساب أولاً', 'warning');
            this.showConfigModal();
            return;
        }

        UIUtils.showNotification('سيتم تطوير تذكيرات الدفع قريباً', 'info');
    }

    viewMessage(messageId) {
        UIUtils.showNotification('سيتم تطوير عرض تفاصيل الرسالة قريباً', 'info');
    }

    resendMessage(messageId) {
        UIUtils.showNotification('سيتم تطوير إعادة الإرسال قريباً', 'info');
    }

    // Method to send payment confirmation (called from payments module)
    async sendPaymentConfirmation(memberId, paymentAmount, paymentDate) {
        if (!this.isConfigured) {
            console.log('WhatsApp not configured, skipping payment confirmation');
            return;
        }

        try {
            const member = await database.get('SELECT * FROM members WHERE id = ?', [memberId]);
            if (!member || !member.phone) {
                console.log('Member not found or no phone number');
                return;
            }

            const message = `
مرحباً ${member.full_name}،

تم استلام دفعتك بنجاح:
💰 المبلغ: ${NumberUtils.formatCurrency(paymentAmount)}
📅 التاريخ: ${DateUtils.formatDate(paymentDate)}

شكراً لك على التزامك.

نظام إدارة الديوان
            `.trim();

            // Save message to database (will be sent by background service)
            await database.run(`
                INSERT INTO whatsapp_messages (member_id, phone_number, message_type, message_content, status)
                VALUES (?, ?, ?, ?, ?)
            `, [memberId, member.phone, 'payment_confirmation', message, 'pending']);

            console.log('Payment confirmation message queued for sending');

        } catch (error) {
            console.error('Error queuing payment confirmation:', error);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.WhatsAppManager = new WhatsAppManager();
        console.log('WhatsAppManager initialized successfully');

        // Try to load messages if database is ready
        setTimeout(() => {
            if (window.database && window.database.isReady) {
                console.log('Database is ready, loading WhatsApp messages...');
                window.WhatsAppManager.loadMessages().catch(error => {
                    console.log('Initial WhatsApp messages load failed, will retry when database is ready');
                });
            }
        }, 1000);

    } catch (error) {
        console.error('Error initializing WhatsAppManager:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة نظام الواتساب', 'danger');
        }
    }
});

// Add database ready listener
window.addEventListener('database-ready', () => {
    console.log('Database ready, WhatsAppManager can now load data');
    if (window.WhatsAppManager) {
        setTimeout(() => {
            window.WhatsAppManager.loadMessages().catch(error => {
                console.error('Error loading WhatsApp messages after database ready:', error);
            });
        }, 500);
    }
});
