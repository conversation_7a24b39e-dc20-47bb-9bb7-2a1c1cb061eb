"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Voice
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionPolicyTargetPage = exports.ConnectionPolicyTargetListInstance = exports.ConnectionPolicyTargetInstance = exports.ConnectionPolicyTargetContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class ConnectionPolicyTargetContextImpl {
    constructor(_version, connectionPolicySid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(connectionPolicySid)) {
            throw new Error("Parameter 'connectionPolicySid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { connectionPolicySid, sid };
        this._uri = `/ConnectionPolicies/${connectionPolicySid}/Targets/${sid}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new ConnectionPolicyTargetInstance(operationVersion, payload, instance._solution.connectionPolicySid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["target"] !== undefined)
            data["Target"] = params["target"];
        if (params["priority"] !== undefined)
            data["Priority"] = params["priority"];
        if (params["weight"] !== undefined)
            data["Weight"] = params["weight"];
        if (params["enabled"] !== undefined)
            data["Enabled"] = serialize.bool(params["enabled"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ConnectionPolicyTargetInstance(operationVersion, payload, instance._solution.connectionPolicySid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConnectionPolicyTargetContextImpl = ConnectionPolicyTargetContextImpl;
class ConnectionPolicyTargetInstance {
    constructor(_version, payload, connectionPolicySid, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.connectionPolicySid = payload.connection_policy_sid;
        this.sid = payload.sid;
        this.friendlyName = payload.friendly_name;
        this.target = payload.target;
        this.priority = deserialize.integer(payload.priority);
        this.weight = deserialize.integer(payload.weight);
        this.enabled = payload.enabled;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this._solution = { connectionPolicySid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ConnectionPolicyTargetContextImpl(this._version, this._solution.connectionPolicySid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a ConnectionPolicyTargetInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a ConnectionPolicyTargetInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConnectionPolicyTargetInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            connectionPolicySid: this.connectionPolicySid,
            sid: this.sid,
            friendlyName: this.friendlyName,
            target: this.target,
            priority: this.priority,
            weight: this.weight,
            enabled: this.enabled,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConnectionPolicyTargetInstance = ConnectionPolicyTargetInstance;
function ConnectionPolicyTargetListInstance(version, connectionPolicySid) {
    if (!(0, utility_1.isValidPathParam)(connectionPolicySid)) {
        throw new Error("Parameter 'connectionPolicySid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new ConnectionPolicyTargetContextImpl(version, connectionPolicySid, sid);
    };
    instance._version = version;
    instance._solution = { connectionPolicySid };
    instance._uri = `/ConnectionPolicies/${connectionPolicySid}/Targets`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["target"] === null || params["target"] === undefined) {
            throw new Error("Required parameter \"params['target']\" missing.");
        }
        let data = {};
        data["Target"] = params["target"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["priority"] !== undefined)
            data["Priority"] = params["priority"];
        if (params["weight"] !== undefined)
            data["Weight"] = params["weight"];
        if (params["enabled"] !== undefined)
            data["Enabled"] = serialize.bool(params["enabled"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ConnectionPolicyTargetInstance(operationVersion, payload, instance._solution.connectionPolicySid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ConnectionPolicyTargetPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ConnectionPolicyTargetPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ConnectionPolicyTargetListInstance = ConnectionPolicyTargetListInstance;
class ConnectionPolicyTargetPage extends Page_1.default {
    /**
     * Initialize the ConnectionPolicyTargetPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ConnectionPolicyTargetInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ConnectionPolicyTargetInstance(this._version, payload, this._solution.connectionPolicySid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConnectionPolicyTargetPage = ConnectionPolicyTargetPage;
