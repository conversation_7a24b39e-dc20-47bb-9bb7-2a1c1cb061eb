@echo off
echo ========================================
echo     بناء تطبيق نظام إدارة الديوان
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت على النظام
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo ❌ المكتبات غير مثبتة
    echo يرجى تشغيل install-dependencies.bat أولاً
    pause
    exit /b 1
)

REM Check if electron-builder is installed
if not exist "node_modules\electron-builder" (
    echo ❌ electron-builder غير مثبت
    echo يرجى تشغيل: npm install electron-builder --save-dev
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🏗️ بدء بناء التطبيق...
echo هذا قد يستغرق عدة دقائق...
echo.

REM Clean dist directory
if exist "dist" (
    echo 🧹 تنظيف مجلد البناء السابق...
    rmdir /s /q "dist"
)

echo 📦 بناء التطبيق للويندوز...
npm run build-win

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق
    echo.
    echo الأخطاء المحتملة:
    echo - تأكد من وجود جميع الملفات المطلوبة
    echo - تأكد من صحة ملف package.json
    echo - تأكد من تثبيت electron-builder
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم بناء التطبيق بنجاح!
echo ========================================
echo.

REM Check what was built
if exist "dist" (
    echo 📁 الملفات المبنية في مجلد dist:
    dir "dist" /b
    echo.
    
    REM Look for installer
    if exist "dist\*.exe" (
        echo 🎉 ملف التثبيت جاهز:
        for %%f in (dist\*.exe) do echo   - %%f
        echo.
        echo يمكنك الآن توزيع ملف التثبيت على أجهزة أخرى
    )
    
    REM Look for portable version
    if exist "dist\win-unpacked" (
        echo 📱 النسخة المحمولة جاهزة في:
        echo   - dist\win-unpacked\
        echo.
        echo يمكنك نسخ هذا المجلد وتشغيل التطبيق مباشرة
    )
) else (
    echo ⚠️ لم يتم العثور على مجلد dist
)

echo.
echo الخطوات التالية:
echo 1. اختبار التطبيق المبني
echo 2. توزيع ملف التثبيت
echo 3. إنشاء نسخة احتياطية من الكود
echo.

REM Ask if user wants to open dist folder
set /p choice="هل تريد فتح مجلد الملفات المبنية؟ (y/n): "
if /i "%choice%"=="y" (
    if exist "dist" (
        explorer "dist"
    )
)

echo.
pause
