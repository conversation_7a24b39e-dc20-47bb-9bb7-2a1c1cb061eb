"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const composition_1 = require("./v1/composition");
const compositionHook_1 = require("./v1/compositionHook");
const compositionSettings_1 = require("./v1/compositionSettings");
const recording_1 = require("./v1/recording");
const recordingSettings_1 = require("./v1/recordingSettings");
const room_1 = require("./v1/room");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Video
     *
     * @param domain - The Twilio (Twilio.Video) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for compositions resource */
    get compositions() {
        this._compositions = this._compositions || (0, composition_1.CompositionListInstance)(this);
        return this._compositions;
    }
    /** Getter for compositionHooks resource */
    get compositionHooks() {
        this._compositionHooks =
            this._compositionHooks || (0, compositionHook_1.CompositionHookListInstance)(this);
        return this._compositionHooks;
    }
    /** Getter for compositionSettings resource */
    get compositionSettings() {
        this._compositionSettings =
            this._compositionSettings || (0, compositionSettings_1.CompositionSettingsListInstance)(this);
        return this._compositionSettings;
    }
    /** Getter for recordings resource */
    get recordings() {
        this._recordings = this._recordings || (0, recording_1.RecordingListInstance)(this);
        return this._recordings;
    }
    /** Getter for recordingSettings resource */
    get recordingSettings() {
        this._recordingSettings =
            this._recordingSettings || (0, recordingSettings_1.RecordingSettingsListInstance)(this);
        return this._recordingSettings;
    }
    /** Getter for rooms resource */
    get rooms() {
        this._rooms = this._rooms || (0, room_1.RoomListInstance)(this);
        return this._rooms;
    }
}
exports.default = V1;
