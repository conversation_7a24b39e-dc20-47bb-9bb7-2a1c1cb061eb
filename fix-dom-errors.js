// Fix DOM-related errors

console.log('🔧 DOM Error Fix Script');
console.log('='.repeat(50));

// Function to check if DOM is ready
function waitForDOM() {
    return new Promise((resolve) => {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', resolve);
        } else {
            resolve();
        }
    });
}

// Function to check required elements
function checkRequiredElements() {
    const requiredElements = [
        'main-content',
        'dashboard-page', 
        'members-page',
        'payments-page',
        'expenses-page',
        'reports-page',
        'whatsapp-page',
        'settings-page',
        'loading-spinner'
    ];

    console.log('Checking required elements...');
    
    const results = {};
    let allFound = true;

    requiredElements.forEach(id => {
        const element = document.getElementById(id);
        results[id] = !!element;
        if (!element) {
            allFound = false;
            console.error(`❌ Missing element: ${id}`);
        } else {
            console.log(`✅ Found element: ${id}`);
        }
    });

    return { allFound, results };
}

// Function to create missing elements
function createMissingElements() {
    console.log('Creating missing elements...');
    
    // Check if main-content exists
    let mainContent = document.getElementById('main-content');
    if (!mainContent) {
        mainContent = document.createElement('div');
        mainContent.id = 'main-content';
        mainContent.className = 'container-fluid mt-3';
        document.body.appendChild(mainContent);
        console.log('✅ Created main-content');
    }

    // Create page elements if missing
    const pages = [
        'dashboard-page',
        'members-page', 
        'payments-page',
        'expenses-page',
        'reports-page',
        'whatsapp-page',
        'settings-page'
    ];

    pages.forEach(pageId => {
        if (!document.getElementById(pageId)) {
            const pageElement = document.createElement('div');
            pageElement.id = pageId;
            pageElement.className = 'page';
            pageElement.style.display = 'none';
            mainContent.appendChild(pageElement);
            console.log(`✅ Created ${pageId}`);
        }
    });

    // Create loading spinner if missing
    if (!document.getElementById('loading-spinner')) {
        const spinner = document.createElement('div');
        spinner.id = 'loading-spinner';
        spinner.className = 'd-none';
        spinner.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        `;
        document.body.appendChild(spinner);
        console.log('✅ Created loading-spinner');
    }
}

// Main fix function
async function fixDOMErrors() {
    try {
        console.log('🔧 Starting DOM error fix...');
        
        // Wait for DOM to be ready
        await waitForDOM();
        console.log('✅ DOM is ready');

        // Check existing elements
        const { allFound } = checkRequiredElements();
        
        if (!allFound) {
            console.log('🔧 Creating missing elements...');
            createMissingElements();
            
            // Check again
            const { allFound: allFoundAfter } = checkRequiredElements();
            if (allFoundAfter) {
                console.log('✅ All elements created successfully');
            } else {
                console.error('❌ Some elements still missing');
            }
        } else {
            console.log('✅ All required elements already exist');
        }

        console.log('🎉 DOM fix completed');
        
    } catch (error) {
        console.error('❌ DOM fix failed:', error);
    }
}

// Run the fix
if (typeof window !== 'undefined') {
    // Browser environment
    fixDOMErrors();
} else {
    // Node.js environment
    console.log('This script should be run in a browser environment');
}
