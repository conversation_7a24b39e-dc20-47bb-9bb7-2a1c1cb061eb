"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Supersim
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsageRecordPage = exports.UsageRecordInstance = exports.UsageRecordListInstance = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
function UsageRecordListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/UsageRecords`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["sim"] !== undefined)
            data["Sim"] = params["sim"];
        if (params["fleet"] !== undefined)
            data["Fleet"] = params["fleet"];
        if (params["network"] !== undefined)
            data["Network"] = params["network"];
        if (params["isoCountry"] !== undefined)
            data["IsoCountry"] = params["isoCountry"];
        if (params["group"] !== undefined)
            data["Group"] = params["group"];
        if (params["granularity"] !== undefined)
            data["Granularity"] = params["granularity"];
        if (params["startTime"] !== undefined)
            data["StartTime"] = serialize.iso8601DateTime(params["startTime"]);
        if (params["endTime"] !== undefined)
            data["EndTime"] = serialize.iso8601DateTime(params["endTime"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UsageRecordPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new UsageRecordPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.UsageRecordListInstance = UsageRecordListInstance;
class UsageRecordInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.simSid = payload.sim_sid;
        this.networkSid = payload.network_sid;
        this.fleetSid = payload.fleet_sid;
        this.isoCountry = payload.iso_country;
        this.period = payload.period;
        this.dataUpload = payload.data_upload;
        this.dataDownload = payload.data_download;
        this.dataTotal = payload.data_total;
        this.dataTotalBilled = payload.data_total_billed;
        this.billedUnit = payload.billed_unit;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            simSid: this.simSid,
            networkSid: this.networkSid,
            fleetSid: this.fleetSid,
            isoCountry: this.isoCountry,
            period: this.period,
            dataUpload: this.dataUpload,
            dataDownload: this.dataDownload,
            dataTotal: this.dataTotal,
            dataTotalBilled: this.dataTotalBilled,
            billedUnit: this.billedUnit,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UsageRecordInstance = UsageRecordInstance;
class UsageRecordPage extends Page_1.default {
    /**
     * Initialize the UsageRecordPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of UsageRecordInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new UsageRecordInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UsageRecordPage = UsageRecordPage;
