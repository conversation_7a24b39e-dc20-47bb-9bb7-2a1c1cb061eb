@echo off
title نظام إدارة الديوان - إصدار محسن

echo ========================================
echo    نظام إدارة الديوان - إصدار محسن
echo ========================================
echo.

REM Clean up any existing processes
echo 🧹 تنظيف العمليات السابقة...
taskkill /f /im electron.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

REM Check Node.js
echo 🔍 فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

node --version
echo ✅ Node.js متاح
echo.

REM Check if dependencies are installed
echo 🔍 فحص المكتبات...
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    npm install electron --save-dev
)

echo ✅ المكتبات جاهزة
echo.

REM Create database directory
if not exist "database" mkdir database

echo 🚀 تشغيل النظام...
echo.
echo ملاحظة: النظام يستخدم قاعدة بيانات مبسطة محفوظة في المتصفح
echo جميع البيانات ستبقى محفوظة بين الجلسات
echo.

REM Start the application
npm start

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في التشغيل، جاري المحاولة مع electron مباشرة...
    npx electron .
    
    if %errorlevel% neq 0 (
        echo.
        echo ❌ فشل في التشغيل
        echo.
        echo الحلول المقترحة:
        echo 1. أعد تشغيل الكمبيوتر
        echo 2. شغل: npm cache clean --force
        echo 3. احذف مجلد node_modules وأعد تشغيل install-dependencies.bat
        echo.
        pause
        exit /b 1
    )
)

echo.
echo 👋 تم إغلاق النظام بنجاح
echo البيانات محفوظة تلقائياً
pause
