@echo off
title نظام إدارة الديوان - مُصلح ونهائي

echo ========================================
echo    نظام إدارة الديوان - مُصلح ونهائي
echo ========================================
echo.
echo ✅ قاعدة بيانات مدمجة في HTML
echo ✅ إصلاح شامل لجميع المشاكل
echo ✅ بيانات تجريبية جاهزة
echo ✅ يعمل بدون أخطاء
echo.

REM Clean processes
echo 🧹 تنظيف العمليات...
taskkill /f /im electron.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 1 /nobreak >nul

REM Check Node.js
echo 🔍 فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js متاح
echo.

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    npm install electron --save-dev
)

echo.
echo 🚀 تشغيل النظام المُصلح...
echo.
echo 📊 الإصلاحات المطبقة:
echo - قاعدة بيانات مدمجة مباشرة في HTML
echo - إصلاح جميع دوال إدارة الأعضاء
echo - حفظ تلقائي في localStorage
echo - بيانات تجريبية: أحمد محمد علي + دفعة + مصروف
echo - عدم وجود أخطاء في التحميل
echo.
echo ⏳ جاري التحميل...

npm start

if %errorlevel% neq 0 (
    echo ❌ فشل npm start، جاري المحاولة مع electron...
    npx electron .
)

echo.
echo 👋 تم إغلاق النظام
echo 💾 البيانات محفوظة تلقائياً
pause
