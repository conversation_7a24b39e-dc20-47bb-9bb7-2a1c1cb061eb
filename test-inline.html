<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات المدمجة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 15px;
            font-weight: bold;
            text-align: center;
            font-size: 1.2em;
        }
        .success {
            background: rgba(40, 167, 69, 0.9);
            border: 3px solid #28a745;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        .error {
            background: rgba(220, 53, 69, 0.9);
            border: 3px solid #dc3545;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }
        .info {
            background: rgba(23, 162, 184, 0.9);
            border: 3px solid #17a2b8;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        }
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .data-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(255,255,255,0.2);
        }
        .data-card h3 {
            margin-top: 0;
            color: #ffd700;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .data-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .data-item:last-child {
            border-bottom: none;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .button-container {
            text-align: center;
            margin-top: 30px;
        }
        .loading {
            text-align: center;
            font-size: 1.3em;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار قاعدة البيانات المدمجة</h1>
        
        <div id="status" class="status loading">⏳ جاري فحص قاعدة البيانات...</div>
        
        <div id="results" style="display: none;">
            <div class="data-grid">
                <div class="data-card">
                    <h3>📊 الإحصائيات</h3>
                    <div id="stats"></div>
                </div>
                
                <div class="data-card">
                    <h3>👥 الأعضاء</h3>
                    <div id="members"></div>
                </div>
                
                <div class="data-card">
                    <h3>💰 المدفوعات</h3>
                    <div id="payments"></div>
                </div>
                
                <div class="data-card">
                    <h3>💸 المصروفات</h3>
                    <div id="expenses"></div>
                </div>
            </div>
        </div>
        
        <div class="button-container">
            <button onclick="runTest()">🔄 إعادة الاختبار</button>
            <button onclick="addTestData()">➕ إضافة بيانات</button>
            <button onclick="clearAllData()">🗑️ مسح الكل</button>
        </div>
    </div>

    <!-- Inline Database - Same as in index.html -->
    <script>
        // Inline Database - مضمونة 100%
        console.log('🔄 Loading Inline Database...');
        
        window.database = {
            isReady: true,
            error: null,
            data: {
                members: [
                    {
                        id: 1,
                        membership_id: 'M001',
                        full_name: 'أحمد محمد علي',
                        phone: '00962791234567',
                        email: '<EMAIL>',
                        address: 'عمان، الأردن',
                        join_date: '2024-01-01',
                        status: 'active',
                        notes: 'عضو مؤسس',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    }
                ],
                payments: [
                    {
                        id: 1,
                        member_id: 1,
                        category_id: 1,
                        amount: 25.000,
                        payment_date: '2024-01-01',
                        payment_method: 'cash',
                        reference_number: 'PAY001',
                        notes: 'دفعة تجريبية',
                        created_at: new Date().toISOString()
                    }
                ],
                expenses: [
                    {
                        id: 1,
                        category_id: 1,
                        amount: 10.000,
                        expense_date: '2024-01-01',
                        description: 'مصروف تجريبي',
                        receipt_path: '',
                        notes: 'مصروف للاختبار',
                        created_at: new Date().toISOString()
                    }
                ],
                revenue_categories: [
                    { id: 1, name: 'اشتراك شهري', description: 'الاشتراك الشهري للأعضاء', is_active: true },
                    { id: 2, name: 'تبرعات', description: 'التبرعات المختلفة', is_active: true }
                ],
                expense_categories: [
                    { id: 1, name: 'فعاليات', description: 'مصروفات الفعاليات والأنشطة', is_active: true },
                    { id: 2, name: 'خدمات', description: 'مصروفات الخدمات المقدمة', is_active: true }
                ],
                settings: [
                    { key: 'currency', value: 'د.أ', description: 'العملة المستخدمة' },
                    { key: 'organization_name', value: 'الديوان', description: 'اسم المنظمة' }
                ]
            },
            nextId: { members: 2, payments: 2, expenses: 2 },
            
            // Load from localStorage
            loadData: function() {
                try {
                    const stored = localStorage.getItem('dewan_inline_db');
                    if (stored) {
                        const parsed = JSON.parse(stored);
                        this.data = { ...this.data, ...parsed };
                        console.log('📊 Data loaded from localStorage');
                    }
                } catch (e) {
                    console.log('📊 Using default data');
                }
            },
            
            // Save to localStorage
            saveData: function() {
                try {
                    localStorage.setItem('dewan_inline_db', JSON.stringify(this.data));
                    console.log('💾 Data saved to localStorage');
                } catch (e) {
                    console.error('Error saving data:', e);
                }
            },
            
            async all(sql, params = []) {
                const sqlLower = sql.toLowerCase();
                
                if (sqlLower.includes('from members')) {
                    return this.data.members;
                }
                if (sqlLower.includes('from payments')) {
                    return this.data.payments;
                }
                if (sqlLower.includes('from expenses')) {
                    return this.data.expenses;
                }
                if (sqlLower.includes('from revenue_categories')) {
                    return this.data.revenue_categories;
                }
                if (sqlLower.includes('from expense_categories')) {
                    return this.data.expense_categories;
                }
                
                return [];
            },
            
            async run(sql, params = []) {
                // Simple implementation for testing
                this.saveData();
                return { id: this.nextId.members++, changes: 1 };
            }
        };
        
        // Load data immediately
        window.database.loadData();
        
        console.log('✅ Inline Database loaded successfully!');
    </script>

    <script>
        async function runTest() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            try {
                statusDiv.innerHTML = '⏳ جاري فحص قاعدة البيانات...';
                statusDiv.className = 'status info';
                resultsDiv.style.display = 'none';

                // Check database
                if (!window.database) {
                    throw new Error('قاعدة البيانات غير محملة');
                }

                if (!window.database.isReady) {
                    throw new Error('قاعدة البيانات غير جاهزة');
                }

                // Test operations
                const members = await window.database.all('SELECT * FROM members');
                const payments = await window.database.all('SELECT * FROM payments');
                const expenses = await window.database.all('SELECT * FROM expenses');
                const revenueCategories = await window.database.all('SELECT * FROM revenue_categories');

                // Show success
                statusDiv.innerHTML = '✅ قاعدة البيانات تعمل بنجاح!';
                statusDiv.className = 'status success';

                // Display data
                document.getElementById('stats').innerHTML = `
                    <div class="data-item"><span>👥 الأعضاء:</span><span>${members.length}</span></div>
                    <div class="data-item"><span>💰 المدفوعات:</span><span>${payments.length}</span></div>
                    <div class="data-item"><span>💸 المصروفات:</span><span>${expenses.length}</span></div>
                    <div class="data-item"><span>📂 الفئات:</span><span>${revenueCategories.length}</span></div>
                `;

                document.getElementById('members').innerHTML = members.map(m => `
                    <div class="data-item"><span>${m.full_name}</span><span>${m.membership_id}</span></div>
                `).join('') || '<div class="data-item"><span>لا يوجد أعضاء</span></div>';

                document.getElementById('payments').innerHTML = payments.map(p => `
                    <div class="data-item"><span>${p.amount} د.أ</span><span>${p.payment_date}</span></div>
                `).join('') || '<div class="data-item"><span>لا يوجد مدفوعات</span></div>';

                document.getElementById('expenses').innerHTML = expenses.map(e => `
                    <div class="data-item"><span>${e.amount} د.أ</span><span>${e.description}</span></div>
                `).join('') || '<div class="data-item"><span>لا يوجد مصروفات</span></div>';

                resultsDiv.style.display = 'block';

            } catch (error) {
                statusDiv.innerHTML = `❌ خطأ: ${error.message}`;
                statusDiv.className = 'status error';
                resultsDiv.style.display = 'none';
            }
        }

        function addTestData() {
            // Add test member
            window.database.data.members.push({
                id: window.database.nextId.members++,
                membership_id: `M${String(window.database.nextId.members).padStart(3, '0')}`,
                full_name: 'عضو تجريبي جديد',
                phone: '00962791234569',
                email: '<EMAIL>',
                address: 'الزرقاء، الأردن',
                join_date: new Date().toISOString().split('T')[0],
                status: 'active',
                notes: 'تم إضافته من الاختبار',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            });

            window.database.saveData();
            alert('تم إضافة عضو تجريبي جديد!');
            runTest();
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('dewan_inline_db');
                location.reload();
            }
        }

        // Auto-run test
        window.addEventListener('load', () => {
            setTimeout(runTest, 500);
        });
    </script>
</body>
</html>
