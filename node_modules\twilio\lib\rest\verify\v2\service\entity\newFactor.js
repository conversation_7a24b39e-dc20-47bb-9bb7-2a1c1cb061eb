"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewFactorInstance = exports.NewFactorListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
function NewFactorListInstance(version, serviceSid, identity) {
    if (!(0, utility_1.isValidPathParam)(serviceSid)) {
        throw new Error("Parameter 'serviceSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(identity)) {
        throw new Error("Parameter 'identity' is not valid.");
    }
    const instance = {};
    instance._version = version;
    instance._solution = { serviceSid, identity };
    instance._uri = `/Services/${serviceSid}/Entities/${identity}/Factors`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["friendlyName"] === null ||
            params["friendlyName"] === undefined) {
            throw new Error("Required parameter \"params['friendlyName']\" missing.");
        }
        if (params["factorType"] === null || params["factorType"] === undefined) {
            throw new Error("Required parameter \"params['factorType']\" missing.");
        }
        let data = {};
        data["FriendlyName"] = params["friendlyName"];
        data["FactorType"] = params["factorType"];
        if (params["binding.alg"] !== undefined)
            data["Binding.Alg"] = params["binding.alg"];
        if (params["binding.publicKey"] !== undefined)
            data["Binding.PublicKey"] = params["binding.publicKey"];
        if (params["config.appId"] !== undefined)
            data["Config.AppId"] = params["config.appId"];
        if (params["config.notificationPlatform"] !== undefined)
            data["Config.NotificationPlatform"] =
                params["config.notificationPlatform"];
        if (params["config.notificationToken"] !== undefined)
            data["Config.NotificationToken"] = params["config.notificationToken"];
        if (params["config.sdkVersion"] !== undefined)
            data["Config.SdkVersion"] = params["config.sdkVersion"];
        if (params["binding.secret"] !== undefined)
            data["Binding.Secret"] = params["binding.secret"];
        if (params["config.timeStep"] !== undefined)
            data["Config.TimeStep"] = params["config.timeStep"];
        if (params["config.skew"] !== undefined)
            data["Config.Skew"] = params["config.skew"];
        if (params["config.codeLength"] !== undefined)
            data["Config.CodeLength"] = params["config.codeLength"];
        if (params["config.alg"] !== undefined)
            data["Config.Alg"] = params["config.alg"];
        if (params["metadata"] !== undefined)
            data["Metadata"] = serialize.object(params["metadata"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new NewFactorInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.identity));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.NewFactorListInstance = NewFactorListInstance;
class NewFactorInstance {
    constructor(_version, payload, serviceSid, identity) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.serviceSid = payload.service_sid;
        this.entitySid = payload.entity_sid;
        this.identity = payload.identity;
        this.binding = payload.binding;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.friendlyName = payload.friendly_name;
        this.status = payload.status;
        this.factorType = payload.factor_type;
        this.config = payload.config;
        this.metadata = payload.metadata;
        this.url = payload.url;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            serviceSid: this.serviceSid,
            entitySid: this.entitySid,
            identity: this.identity,
            binding: this.binding,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            friendlyName: this.friendlyName,
            status: this.status,
            factorType: this.factorType,
            config: this.config,
            metadata: this.metadata,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.NewFactorInstance = NewFactorInstance;
