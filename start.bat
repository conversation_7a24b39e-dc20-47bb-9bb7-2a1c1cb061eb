@echo off
chcp 65001 >nul
echo ========================================
echo    نظام إدارة الديوان - Dewan Management
echo ========================================
echo.

echo 🔍 فحص المتطلبات الأساسية...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo.
    echo 📋 يرجى تثبيت Node.js أولاً:
    echo 1. اذهب إلى: https://nodejs.org/
    echo 2. حمل النسخة LTS
    echo 3. قم بتثبيتها
    echo 4. أعد تشغيل الكمبيوتر
    echo 5. شغل هذا الملف مرة أخرى
    echo.
    echo 🌐 فتح موقع Node.js...
    start https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
node --version
npm --version
echo.

REM Clean up any corrupted files
echo 🧹 تنظيف الملفات المؤقتة...
if exist "%USERPROFILE%\dewan-data\*.db-journal" del "%USERPROFILE%\dewan-data\*.db-journal" >nul 2>&1
if exist "%USERPROFILE%\dewan-data\*.db-wal" del "%USERPROFILE%\dewan-data\*.db-wal" >nul 2>&1
if exist "%USERPROFILE%\dewan-data\*.db-shm" del "%USERPROFILE%\dewan-data\*.db-shm" >nul 2>&1

REM Check if node_modules exists and is valid
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت التبعيات
        echo 🔧 محاولة إصلاح المشكلة...
        npm cache clean --force
        npm install
        if %errorlevel% neq 0 (
            echo ❌ فشل في تثبيت التبعيات
            echo يرجى التحقق من اتصال الإنترنت
            pause
            exit /b 1
        )
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
) else (
    echo ✅ التبعيات موجودة
    echo.
)

REM Check if database exists, if not create it
if not exist "%USERPROFILE%\dewan-data\dewan.db" (
    echo 🗄️ إنشاء قاعدة البيانات...
    node reset-database.js >nul 2>&1
    echo ✅ تم إنشاء قاعدة البيانات
    echo.
)

echo 🚀 تشغيل نظام إدارة الديوان...
echo.
echo 📝 ملاحظة: إذا ظهرت أخطاء، اضغط F12 في المتصفح وشغل: fixDatabaseIssue()
echo.

npm start

if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في تشغيل التطبيق
    echo 🔧 محاولة الإصلاح...
    echo.
    call quick-fix.bat
)

pause
