// Member management module

class MembersManager {
    constructor() {
        this.members = [];
        this.filteredMembers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        this.statusFilter = 'all';
        this.sortBy = 'full_name';
        this.sortOrder = 'asc';
        
        this.init();
    }

    init() {
        // Set up event listeners when the members page is loaded
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
    }

    async waitForDatabaseModule() {
        console.log('🔍 Waiting for database module...');

        // If already loaded, return immediately
        if (window.database && window.databaseModuleLoaded) {
            console.log('✅ Database module already loaded');
            return;
        }

        // Wait for database module to load
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Database module loading timeout'));
            }, 10000); // 10 second timeout

            const checkDatabase = () => {
                if (window.database && window.databaseModuleLoaded) {
                    clearTimeout(timeout);
                    console.log('✅ Database module loaded successfully');
                    resolve();
                } else {
                    setTimeout(checkDatabase, 100);
                }
            };

            // Also listen for the database module loaded event
            window.addEventListener('database-module-loaded', () => {
                clearTimeout(timeout);
                console.log('✅ Database module loaded via event');
                resolve();
            }, { once: true });

            checkDatabase();
        });
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('member-search');
        if (searchInput) {
            searchInput.addEventListener('input', UIUtils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.filterMembers();
            }, 300));
        }

        // Status filter
        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value;
                this.filterMembers();
            });
        }

        // Sort functionality
        const sortSelect = document.getElementById('sort-members');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                this.sortBy = sortBy;
                this.sortOrder = sortOrder;
                this.sortMembers();
            });
        }

        // Items per page
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', (e) => {
                this.itemsPerPage = parseInt(e.target.value);
                this.currentPage = 1;
                this.renderMembers();
            });
        }
    }

    async loadMembers() {
        try {
            UIUtils.showLoading();

            // Wait for database module to be loaded
            await this.waitForDatabaseModule();

            // Check if database is available
            if (!window.database) {
                throw new Error('وحدة قاعدة البيانات غير محملة');
            }

            if (!window.database.db) {
                console.log('Database not ready, attempting to initialize...');
                // Try to initialize database
                if (typeof window.database.init === 'function') {
                    await window.database.init();
                    // Wait a bit for initialization
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }

                if (!window.database.db) {
                    throw new Error('قاعدة البيانات غير متاحة - فشل في التهيئة');
                }
            }

            // Load members page content if not already loaded
            await this.loadMembersPageContent();

            // Fetch members from database with error handling
            try {
                this.members = await database.all(`
                    SELECT m.*,
                           COUNT(p.id) as payment_count,
                           COALESCE(SUM(p.amount), 0) as total_paid,
                           MAX(p.payment_date) as last_payment_date
                    FROM members m
                    LEFT JOIN payments p ON m.id = p.member_id
                    GROUP BY m.id
                    ORDER BY m.created_at DESC
                `);
            } catch (dbError) {
                console.error('Database query error:', dbError);
                // Try simpler query if complex one fails
                this.members = await database.all('SELECT * FROM members ORDER BY created_at DESC');
                // Add default values for missing fields
                this.members = this.members.map(member => ({
                    ...member,
                    payment_count: 0,
                    total_paid: 0,
                    last_payment_date: null
                }));
            }

            this.filteredMembers = [...this.members];
            this.renderMembers();
            this.updateMemberStats();

        } catch (error) {
            console.error('Error loading members:', error);
            UIUtils.showNotification(`خطأ في تحميل بيانات الأعضاء: ${error.message}`, 'danger');

            // Show empty state
            this.members = [];
            this.filteredMembers = [];
            this.renderMembers();
            this.updateMemberStats();
        } finally {
            UIUtils.hideLoading();
        }
    }

    async loadMembersPageContent() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        const pageElement = document.getElementById('members-page');
        if (!pageElement) {
            console.error('Members page element not found, available elements:',
                Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            throw new Error('عنصر صفحة الأعضاء غير موجود في HTML');
        }

        if (pageElement.innerHTML.trim()) {
            console.log('Members page content already loaded');
            return;
        }

        pageElement.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="bi bi-people"></i>
                            إدارة الأعضاء
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="window.MembersManager.showAddMemberModal()">
                                <i class="bi bi-person-plus"></i>
                                إضافة عضو جديد
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.PrintManager.showPrintSettings()" title="إعدادات الطباعة">
                                <i class="bi bi-gear"></i>
                            </button>
                            <button class="btn btn-outline-primary" onclick="window.MembersManager.printMembersList()" title="طباعة قائمة الأعضاء">
                                <i class="bi bi-printer"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-members-count">0</h4>
                                    <p class="mb-0">إجمالي الأعضاء</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="active-members-count">0</h4>
                                    <p class="mb-0">الأعضاء النشطون</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-person-check fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="inactive-members-count">0</h4>
                                    <p class="mb-0">الأعضاء غير النشطين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-person-x fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="new-members-month">0</h4>
                                    <p class="mb-0">أعضاء جدد هذا الشهر</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-person-plus fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="member-search" class="form-label">البحث</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="member-search" 
                                       placeholder="البحث بالاسم، الهاتف، أو رقم العضوية...">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="status-filter" class="form-label">الحالة</label>
                            <select class="form-select" id="status-filter">
                                <option value="all">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="sort-members" class="form-label">ترتيب حسب</label>
                            <select class="form-select" id="sort-members">
                                <option value="full_name-asc">الاسم (أ-ي)</option>
                                <option value="full_name-desc">الاسم (ي-أ)</option>
                                <option value="join_date-desc">تاريخ الانضمام (الأحدث)</option>
                                <option value="join_date-asc">تاريخ الانضمام (الأقدم)</option>
                                <option value="membership_id-asc">رقم العضوية</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="items-per-page" class="form-label">عدد العناصر</label>
                            <select class="form-select" id="items-per-page">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        
                        <div class="col-md-1 d-flex align-items-end">
                            <button class="btn btn-outline-secondary" onclick="window.MembersManager.exportMembers()">
                                <i class="bi bi-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Members Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم العضوية</th>
                                    <th>الاسم الكامل</th>
                                    <th>رقم الهاتف</th>
                                    <th>تاريخ الانضمام</th>
                                    <th>الحالة</th>
                                    <th>إجمالي المدفوعات</th>
                                    <th>آخر دفعة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="members-table-body">
                                <!-- Members will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center" id="members-pagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        `;

        // Set up event listeners after content is loaded
        setTimeout(() => this.setupEventListeners(), 100);
    }

    filterMembers() {
        this.filteredMembers = this.members.filter(member => {
            const matchesSearch = !this.searchTerm || 
                member.full_name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                member.phone?.includes(this.searchTerm) ||
                member.membership_id.toLowerCase().includes(this.searchTerm.toLowerCase());
            
            const matchesStatus = this.statusFilter === 'all' || member.status === this.statusFilter;
            
            return matchesSearch && matchesStatus;
        });
        
        this.currentPage = 1;
        this.renderMembers();
    }

    sortMembers() {
        this.filteredMembers.sort((a, b) => {
            let aValue = a[this.sortBy];
            let bValue = b[this.sortBy];
            
            // Handle different data types
            if (this.sortBy.includes('date')) {
                aValue = new Date(aValue || 0);
                bValue = new Date(bValue || 0);
            } else if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            
            if (this.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        this.renderMembers();
    }

    renderMembers() {
        const tbody = document.getElementById('members-table-body');
        if (!tbody) {
            console.warn('Members table body not found, skipping render');
            return;
        }

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedMembers = this.filteredMembers.slice(startIndex, endIndex);

        if (paginatedMembers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted py-4">
                        <i class="bi bi-people fs-1 d-block mb-2"></i>
                        لا توجد أعضاء مطابقة للبحث
                    </td>
                </tr>
            `;
        } else {
            tbody.innerHTML = paginatedMembers.map(member => `
                <tr>
                    <td><strong>${member.membership_id}</strong></td>
                    <td>${member.full_name}</td>
                    <td>${member.phone ? ValidationUtils.formatPhone(member.phone) : '-'}</td>
                    <td>${DateUtils.formatDate(member.join_date)}</td>
                    <td>${UIUtils.formatStatusBadge(member.status)}</td>
                    <td>${NumberUtils.formatCurrency(member.total_paid)}</td>
                    <td>${member.last_payment_date ? DateUtils.formatDate(member.last_payment_date) : '-'}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="window.MembersManager.viewMember(${member.id})" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.MembersManager.editMember(${member.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="window.MembersManager.deleteMember(${member.id})" title="حذف">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        this.renderPagination();
    }

    renderPagination() {
        const pagination = document.getElementById('members-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredMembers.length / this.itemsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.MembersManager.goToPage(${this.currentPage - 1})">السابق</a>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="window.MembersManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.MembersManager.goToPage(${this.currentPage + 1})">التالي</a>
            </li>
        `;
        
        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredMembers.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderMembers();
        }
    }

    updateMemberStats() {
        const totalCount = this.members.length;
        const activeCount = this.members.filter(m => m.status === 'active').length;
        const inactiveCount = this.members.filter(m => m.status === 'inactive').length;

        // New members this month
        const currentMonth = moment().format('YYYY-MM');
        const newThisMonth = this.members.filter(m =>
            moment(m.join_date).format('YYYY-MM') === currentMonth
        ).length;

        // Safely update elements
        const updateElement = (id, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = NumberUtils.formatNumber(value);
            } else {
                console.warn(`Element ${id} not found for stats update`);
            }
        };

        updateElement('total-members-count', totalCount);
        updateElement('active-members-count', activeCount);
        updateElement('inactive-members-count', inactiveCount);
        updateElement('new-members-month', newThisMonth);
    }

    // Member CRUD operations
    showAddMemberModal() {
        const modal = this.createMemberModal('add');
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    async viewMember(memberId) {
        try {
            const member = await database.get('SELECT * FROM members WHERE id = ?', [memberId]);
            if (!member) {
                UIUtils.showNotification('العضو غير موجود', 'warning');
                return;
            }

            // Get member's payment history
            const payments = await database.all(`
                SELECT p.*, rc.name as category_name
                FROM payments p
                JOIN revenue_categories rc ON p.category_id = rc.id
                WHERE p.member_id = ?
                ORDER BY p.payment_date DESC
            `, [memberId]);

            this.showMemberDetailsModal(member, payments);
        } catch (error) {
            console.error('Error viewing member:', error);
            UIUtils.showNotification('خطأ في عرض بيانات العضو', 'danger');
        }
    }

    async editMember(memberId) {
        try {
            const member = await database.get('SELECT * FROM members WHERE id = ?', [memberId]);
            if (!member) {
                UIUtils.showNotification('العضو غير موجود', 'warning');
                return;
            }

            const modal = this.createMemberModal('edit', member);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        } catch (error) {
            console.error('Error editing member:', error);
            UIUtils.showNotification('خطأ في تحميل بيانات العضو للتعديل', 'danger');
        }
    }

    async deleteMember(memberId) {
        try {
            const member = await database.get('SELECT * FROM members WHERE id = ?', [memberId]);
            if (!member) {
                UIUtils.showNotification('العضو غير موجود', 'warning');
                return;
            }

            const confirmed = await UIUtils.showConfirmDialog(
                'تأكيد الحذف',
                `هل أنت متأكد من حذف العضو "${member.full_name}"؟\nسيتم حذف جميع المدفوعات المرتبطة بهذا العضو أيضاً.`,
                'حذف',
                'إلغاء'
            );

            if (confirmed) {
                await database.run('DELETE FROM members WHERE id = ?', [memberId]);
                UIUtils.showNotification('تم حذف العضو بنجاح', 'success');
                await this.loadMembers();
            }
        } catch (error) {
            console.error('Error deleting member:', error);
            UIUtils.showNotification('خطأ في حذف العضو', 'danger');
        }
    }

    createMemberModal(mode, member = null) {
        const isEdit = mode === 'edit';
        const modalId = `member-modal-${Date.now()}`;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-person-${isEdit ? 'gear' : 'plus'}"></i>
                            ${isEdit ? 'تعديل بيانات العضو' : 'إضافة عضو جديد'}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="member-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="membership-id" class="form-label">رقم العضوية *</label>
                                        <input type="text" class="form-control" id="membership-id"
                                               value="${member?.membership_id || UIUtils.generateMembershipId()}"
                                               ${isEdit ? 'readonly' : ''} required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="full-name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="full-name"
                                               value="${member?.full_name || ''}" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone"
                                               value="${member?.phone || ''}"
                                               placeholder="05xxxxxxxx">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="join-date" class="form-label">تاريخ الانضمام *</label>
                                        <input type="date" class="form-control" id="join-date"
                                               value="${member?.join_date || moment().format('YYYY-MM-DD')}" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">حالة العضوية</label>
                                        <select class="form-select" id="status">
                                            <option value="active" ${member?.status === 'active' ? 'selected' : ''}>نشط</option>
                                            <option value="inactive" ${member?.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" rows="2"
                                          placeholder="العنوان الكامل...">${member?.address || ''}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" rows="3"
                                          placeholder="أي ملاحظات إضافية...">${member?.notes || ''}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="window.MembersManager.saveMember('${mode}', ${member?.id || 'null'}, '${modalId}')">
                            <i class="bi bi-check-lg"></i>
                            ${isEdit ? 'حفظ التعديلات' : 'إضافة العضو'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });

        return modal;
    }

    async saveMember(mode, memberId, modalId) {
        try {
            const form = document.getElementById('member-form');
            const formData = new FormData(form);

            // Get form values
            const memberData = {
                membership_id: document.getElementById('membership-id').value.trim(),
                full_name: document.getElementById('full-name').value.trim(),
                phone: ValidationUtils.cleanPhone(document.getElementById('phone').value.trim()),
                address: document.getElementById('address').value.trim(),
                join_date: document.getElementById('join-date').value,
                status: document.getElementById('status').value,
                notes: document.getElementById('notes').value.trim()
            };

            // Validate form
            const validation = this.validateMemberForm(memberData, mode, memberId);
            if (!validation.isValid) {
                this.showFormErrors(validation.errors);
                return;
            }

            UIUtils.showLoading();

            if (mode === 'add') {
                await database.run(`
                    INSERT INTO members (membership_id, full_name, phone, address, join_date, status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                `, [
                    memberData.membership_id,
                    memberData.full_name,
                    memberData.phone || null,
                    memberData.address || null,
                    memberData.join_date,
                    memberData.status,
                    memberData.notes || null
                ]);

                UIUtils.showNotification('تم إضافة العضو بنجاح', 'success');
            } else {
                await database.run(`
                    UPDATE members
                    SET full_name = ?, phone = ?, address = ?, join_date = ?, status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                `, [
                    memberData.full_name,
                    memberData.phone || null,
                    memberData.address || null,
                    memberData.join_date,
                    memberData.status,
                    memberData.notes || null,
                    memberId
                ]);

                UIUtils.showNotification('تم تحديث بيانات العضو بنجاح', 'success');
            }

            // Close modal and refresh data
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            modal.hide();

            await this.loadMembers();

        } catch (error) {
            console.error('Error saving member:', error);
            UIUtils.showNotification('خطأ في حفظ بيانات العضو', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    validateMemberForm(data, mode, memberId) {
        const errors = {};
        let isValid = true;

        // Validate required fields
        if (!data.membership_id) {
            errors.membership_id = 'رقم العضوية مطلوب';
            isValid = false;
        }

        if (!data.full_name) {
            errors.full_name = 'الاسم الكامل مطلوب';
            isValid = false;
        }

        if (!data.join_date) {
            errors.join_date = 'تاريخ الانضمام مطلوب';
            isValid = false;
        }

        // Validate phone number if provided
        if (data.phone) {
            const phoneError = ValidationUtils.validatePhone(data.phone);
            if (phoneError) {
                errors.phone = phoneError;
                isValid = false;
            }
        }

        // Validate membership ID uniqueness (for new members or when changing ID)
        if (mode === 'add' || (mode === 'edit' && data.membership_id !== this.members.find(m => m.id === memberId)?.membership_id)) {
            const existingMember = this.members.find(m => m.membership_id === data.membership_id && m.id !== memberId);
            if (existingMember) {
                errors.membership_id = 'رقم العضوية موجود مسبقاً';
                isValid = false;
            }
        }

        return { isValid, errors };
    }

    showFormErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

        // Show new errors
        Object.keys(errors).forEach(field => {
            const input = document.getElementById(field.replace('_', '-'));
            if (input) {
                input.classList.add('is-invalid');
                const feedback = input.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = errors[field];
                }
            }
        });
    }

    showMemberDetailsModal(member, payments) {
        const modalId = `member-details-modal-${Date.now()}`;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-person-circle"></i>
                            تفاصيل العضو: ${member.full_name}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <!-- Member Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="bi bi-person-badge"></i>
                                            معلومات العضو
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>رقم العضوية:</strong></td>
                                                <td>${member.membership_id}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>الاسم الكامل:</strong></td>
                                                <td>${member.full_name}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>رقم الهاتف:</strong></td>
                                                <td>${member.phone ? ValidationUtils.formatPhone(member.phone) : '-'}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>العنوان:</strong></td>
                                                <td>${member.address || '-'}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ الانضمام:</strong></td>
                                                <td>${DateUtils.formatDate(member.join_date)}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>الحالة:</strong></td>
                                                <td>${UIUtils.formatStatusBadge(member.status)}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>ملاحظات:</strong></td>
                                                <td>${member.notes || '-'}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Summary -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="bi bi-credit-card"></i>
                                            ملخص المدفوعات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="border rounded p-3 mb-3">
                                                    <h4 class="text-primary">${NumberUtils.formatCurrency(payments.reduce((sum, p) => sum + p.amount, 0))}</h4>
                                                    <small class="text-muted">إجمالي المدفوعات</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-3 mb-3">
                                                    <h4 class="text-info">${payments.length}</h4>
                                                    <small class="text-muted">عدد المدفوعات</small>
                                                </div>
                                            </div>
                                        </div>

                                        ${payments.length > 0 ? `
                                            <div class="border rounded p-3">
                                                <strong>آخر دفعة:</strong><br>
                                                <span class="text-success">${NumberUtils.formatCurrency(payments[0].amount)}</span><br>
                                                <small class="text-muted">${DateUtils.formatDate(payments[0].payment_date)}</small>
                                            </div>
                                        ` : `
                                            <div class="text-center text-muted">
                                                <i class="bi bi-credit-card-2-front fs-1"></i>
                                                <p>لا توجد مدفوعات مسجلة</p>
                                            </div>
                                        `}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment History -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="bi bi-clock-history"></i>
                                            سجل المدفوعات
                                        </h6>
                                        <button class="btn btn-sm btn-primary" onclick="window.PaymentsManager?.showAddPaymentModal(${member.id})">
                                            <i class="bi bi-plus"></i>
                                            إضافة دفعة
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        ${payments.length > 0 ? `
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>التاريخ</th>
                                                            <th>المبلغ</th>
                                                            <th>النوع</th>
                                                            <th>طريقة الدفع</th>
                                                            <th>رقم المرجع</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        ${payments.map(payment => `
                                                            <tr>
                                                                <td>${DateUtils.formatDate(payment.payment_date)}</td>
                                                                <td><strong class="text-success">${NumberUtils.formatCurrency(payment.amount)}</strong></td>
                                                                <td>${payment.category_name}</td>
                                                                <td>${this.getPaymentMethodText(payment.payment_method)}</td>
                                                                <td>${payment.reference_number || '-'}</td>
                                                            </tr>
                                                        `).join('')}
                                                    </tbody>
                                                </table>
                                            </div>
                                        ` : `
                                            <div class="text-center text-muted py-4">
                                                <i class="bi bi-credit-card-2-front fs-1"></i>
                                                <p>لا توجد مدفوعات مسجلة لهذا العضو</p>
                                            </div>
                                        `}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="window.MembersManager.editMember(${member.id}); bootstrap.Modal.getInstance(document.getElementById('${modalId}')).hide();">
                            <i class="bi bi-pencil"></i>
                            تعديل البيانات
                        </button>
                        <button type="button" class="btn btn-info" onclick="window.MembersManager.printMemberStatement(${member.id})">
                            <i class="bi bi-printer"></i>
                            طباعة كشف الحساب
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    getPaymentMethodText(method) {
        const methods = {
            'cash': 'نقداً',
            'bank_transfer': 'تحويل بنكي',
            'check': 'شيك',
            'card': 'بطاقة'
        };
        return methods[method] || method;
    }

    async printMemberStatement(memberId) {
        try {
            const member = await database.get('SELECT * FROM members WHERE id = ?', [memberId]);
            const payments = await database.all(`
                SELECT p.*, rc.name as category_name
                FROM payments p
                JOIN revenue_categories rc ON p.category_id = rc.id
                WHERE p.member_id = ?
                ORDER BY p.payment_date DESC
            `, [memberId]);

            // Create printable content
            const printContent = `
                <div class="text-center mb-4">
                    <h2>كشف حساب العضو</h2>
                    <h4>${member.full_name}</h4>
                    <p>رقم العضوية: ${member.membership_id}</p>
                </div>

                <div class="row mb-4">
                    <div class="col-6">
                        <strong>معلومات العضو:</strong><br>
                        الهاتف: ${member.phone || '-'}<br>
                        تاريخ الانضمام: ${DateUtils.formatDate(member.join_date)}<br>
                        الحالة: ${member.status === 'active' ? 'نشط' : 'غير نشط'}
                    </div>
                    <div class="col-6">
                        <strong>ملخص المدفوعات:</strong><br>
                        إجمالي المدفوعات: ${NumberUtils.formatCurrency(payments.reduce((sum, p) => sum + p.amount, 0))}<br>
                        عدد المدفوعات: ${payments.length}
                    </div>
                </div>

                ${payments.length > 0 ? `
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>النوع</th>
                                <th>طريقة الدفع</th>
                                <th>رقم المرجع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${payments.map(payment => `
                                <tr>
                                    <td>${DateUtils.formatDate(payment.payment_date)}</td>
                                    <td>${NumberUtils.formatCurrency(payment.amount)}</td>
                                    <td>${payment.category_name}</td>
                                    <td>${this.getPaymentMethodText(payment.payment_method)}</td>
                                    <td>${payment.reference_number || '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                ` : '<p class="text-center">لا توجد مدفوعات مسجلة</p>'}
            `;

            // Create a temporary element for printing
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = printContent;
            tempDiv.id = 'member-statement-print';
            document.body.appendChild(tempDiv);

            // Print
            UIUtils.printContent('member-statement-print', `كشف حساب - ${member.full_name}`);

            // Clean up
            setTimeout(() => {
                document.body.removeChild(tempDiv);
            }, 1000);

        } catch (error) {
            console.error('Error printing member statement:', error);
            UIUtils.showNotification('خطأ في طباعة كشف الحساب', 'danger');
        }
    }

    exportMembers() {
        const exportData = this.filteredMembers.map(member => ({
            'رقم العضوية': member.membership_id,
            'الاسم الكامل': member.full_name,
            'رقم الهاتف': member.phone || '',
            'العنوان': member.address || '',
            'تاريخ الانضمام': DateUtils.formatDate(member.join_date),
            'الحالة': member.status === 'active' ? 'نشط' : 'غير نشط',
            'إجمالي المدفوعات': member.total_paid,
            'عدد المدفوعات': member.payment_count,
            'آخر دفعة': member.last_payment_date ? DateUtils.formatDate(member.last_payment_date) : ''
        }));

        UIUtils.exportToCSV(exportData, `members-${moment().format('YYYY-MM-DD')}.csv`);
    }

    // Print functions
    printMembersList() {
        console.log('🖨️ Printing members list...');

        try {
            const title = 'قائمة الأعضاء';
            const subtitle = `إجمالي الأعضاء: ${this.members.length} - النشطون: ${this.members.filter(m => m.status === 'active').length}`;

            // Create printable content
            const printContent = this.createPrintableMembersList();

            return window.PrintManager.printContent(title, subtitle, printContent);

        } catch (error) {
            console.error('❌ Print members list failed:', error);
            UIUtils.showNotification('فشل في طباعة قائمة الأعضاء: ' + error.message, 'danger');
            return false;
        }
    }

    createPrintableMembersList() {
        const totalMembers = this.members.length;
        const activeMembers = this.members.filter(m => m.status === 'active').length;
        const inactiveMembers = this.members.filter(m => m.status === 'inactive').length;
        const suspendedMembers = this.members.filter(m => m.status === 'suspended').length;

        return `
            <!-- Statistics Summary -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <h4>${totalMembers}</h4>
                        <p>إجمالي الأعضاء</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h4>${activeMembers}</h4>
                        <p>الأعضاء النشطون</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h4>${inactiveMembers}</h4>
                        <p>الأعضاء غير النشطين</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h4>${suspendedMembers}</h4>
                        <p>الأعضاء المعلقون</p>
                    </div>
                </div>
            </div>

            <!-- Members Table -->
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>رقم العضوية</th>
                            <th>الاسم الكامل</th>
                            <th>الهاتف</th>
                            <th>تاريخ الانضمام</th>
                            <th>الحالة</th>
                            <th>إجمالي المدفوعات</th>
                            <th>عدد المدفوعات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.members.map(member => `
                            <tr>
                                <td><strong>${member.membership_id}</strong></td>
                                <td>${member.full_name}</td>
                                <td>${member.phone || '-'}</td>
                                <td>${DateUtils.formatDate(member.join_date)}</td>
                                <td>
                                    <span class="badge ${this.getStatusBadgeClass(member.status)}">
                                        ${this.getStatusText(member.status)}
                                    </span>
                                </td>
                                <td>${NumberUtils.formatCurrency(member.total_paid || 0)}</td>
                                <td>${member.payment_count || 0}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            ${this.members.length === 0 ? `
                <div class="text-center py-5">
                    <i class="bi bi-people fs-1 text-muted"></i>
                    <h4 class="mt-3 text-muted">لا يوجد أعضاء</h4>
                    <p class="text-muted">لم يتم تسجيل أي أعضاء بعد</p>
                </div>
            ` : ''}
        `;
    }

    getStatusText(status) {
        const statuses = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'suspended': 'معلق'
        };
        return statuses[status] || status;
    }

    getStatusBadgeClass(status) {
        const classes = {
            'active': 'bg-success',
            'inactive': 'bg-warning',
            'suspended': 'bg-danger'
        };
        return classes[status] || 'bg-secondary';
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.MembersManager = new MembersManager();
        console.log('MembersManager initialized successfully');

        // Try to load members if database is ready
        setTimeout(() => {
            if (window.database && window.database.isReady) {
                console.log('Database is ready, loading members...');
                window.MembersManager.loadMembers().catch(error => {
                    console.log('Initial load failed, will retry when database is ready');
                });
            }
        }, 1000);

    } catch (error) {
        console.error('Error initializing MembersManager:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة نظام إدارة الأعضاء', 'danger');
        }
    }
});

// Add database ready listener
window.addEventListener('database-ready', () => {
    console.log('Database ready, MembersManager can now load data');
    if (window.MembersManager) {
        setTimeout(() => {
            window.MembersManager.loadMembers().catch(error => {
                console.error('Error loading members after database ready:', error);
            });
        }, 500);
    }
});
