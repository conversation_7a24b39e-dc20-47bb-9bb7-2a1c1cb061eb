// Download Chart.js locally - تحميل مكتبة Chart.js محلياً
// This script downloads Chart.js and saves it locally

console.log('📊 Starting Chart.js download...');

async function downloadChartJS() {
    try {
        console.log('🌐 Downloading Chart.js from CDN...');
        
        const chartJsUrl = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
        
        // Use fetch to download
        const response = await fetch(chartJsUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const chartJsContent = await response.text();
        
        console.log('💾 Saving Chart.js locally...');
        
        // Create a blob and download link
        const blob = new Blob([chartJsContent], { type: 'application/javascript' });
        const url = window.URL.createObjectURL(blob);
        
        // Create download link
        const a = document.createElement('a');
        a.href = url;
        a.download = 'chart.min.js';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        console.log('✅ Chart.js downloaded successfully!');
        console.log('📁 Save the downloaded file to: src/renderer/js/chart.min.js');
        
        return true;
        
    } catch (error) {
        console.error('❌ Error downloading Chart.js:', error);
        
        // Show alternative solution
        console.log('🔧 Alternative solution:');
        console.log('1. Download Chart.js manually from: https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js');
        console.log('2. Save it as: src/renderer/js/chart.min.js');
        console.log('3. Update index.html to use local file');
        
        return false;
    }
}

// Function to update index.html to use local Chart.js
function updateIndexToUseLocalChart() {
    console.log('📝 Instructions to use local Chart.js:');
    console.log('');
    console.log('1. Replace this line in src/renderer/index.html:');
    console.log('   <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>');
    console.log('');
    console.log('2. With this line:');
    console.log('   <script src="js/chart.min.js"></script>');
    console.log('');
    console.log('3. Make sure the chart.min.js file is in src/renderer/js/ folder');
}

// Function to test if Chart.js is working
function testChartJS() {
    console.log('🧪 Testing Chart.js availability...');
    
    if (typeof Chart !== 'undefined') {
        console.log('✅ Chart.js is available!');
        console.log('📊 Chart.js version:', Chart.version || 'Unknown');
        
        // Test creating a simple chart
        try {
            const testCanvas = document.createElement('canvas');
            testCanvas.id = 'test-chart';
            testCanvas.style.display = 'none';
            document.body.appendChild(testCanvas);
            
            const testChart = new Chart(testCanvas, {
                type: 'bar',
                data: {
                    labels: ['Test'],
                    datasets: [{
                        label: 'Test Data',
                        data: [1],
                        backgroundColor: '#007bff'
                    }]
                },
                options: {
                    responsive: false,
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
            
            testChart.destroy();
            document.body.removeChild(testCanvas);
            
            console.log('✅ Chart.js functionality test passed!');
            return true;
            
        } catch (error) {
            console.error('❌ Chart.js functionality test failed:', error);
            return false;
        }
        
    } else {
        console.log('❌ Chart.js is not available');
        console.log('🔧 Using fallback chart system instead');
        return false;
    }
}

// Function to fix chart issues
async function fixChartIssues() {
    console.log('🔧 Starting chart issues fix...');
    
    try {
        // Test current Chart.js status
        const chartWorking = testChartJS();
        
        if (chartWorking) {
            console.log('✅ Chart.js is working properly');
            return true;
        }
        
        // Try to download Chart.js
        console.log('📥 Attempting to download Chart.js...');
        const downloadSuccess = await downloadChartJS();
        
        if (downloadSuccess) {
            console.log('✅ Chart.js download completed');
            updateIndexToUseLocalChart();
        } else {
            console.log('⚠️ Chart.js download failed, using fallback system');
        }
        
        // Show current status
        console.log('');
        console.log('📊 Current Chart Status:');
        console.log('- Chart.js Available:', typeof Chart !== 'undefined');
        console.log('- Fallback System:', typeof Chart !== 'undefined' ? 'Not needed' : 'Active');
        console.log('- Reports Working:', 'Yes (with fallback if needed)');
        
        return true;
        
    } catch (error) {
        console.error('❌ Error fixing chart issues:', error);
        return false;
    }
}

// Make functions available globally
if (typeof window !== 'undefined') {
    window.downloadChartJS = downloadChartJS;
    window.testChartJS = testChartJS;
    window.fixChartIssues = fixChartIssues;
    window.updateIndexToUseLocalChart = updateIndexToUseLocalChart;
}

// Auto-run test
if (typeof window !== 'undefined') {
    console.log('🎯 Chart.js fix functions available:');
    console.log('- downloadChartJS()');
    console.log('- testChartJS()');
    console.log('- fixChartIssues()');
    console.log('- updateIndexToUseLocalChart()');
    console.log('');
    console.log('🚀 To fix all chart issues: fixChartIssues()');
    console.log('🧪 To test Chart.js: testChartJS()');
    
    // Auto-test after a delay
    setTimeout(() => {
        testChartJS();
    }, 1000);
}
