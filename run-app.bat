@echo off
chcp 65001 >nul
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الديوان                      ║
echo ║                  Dewan Management System                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check Node.js
echo [1/5] 🔍 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت - يرجى تثبيته من https://nodejs.org/
    start https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM Install dependencies if needed
echo [2/5] 📦 فحص التبعيات...
if not exist "node_modules\electron" (
    echo 📥 تثبيت التبعيات...
    npm install --silent
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت التبعيات
        npm cache clean --force
        npm install
    )
)
echo ✅ التبعيات جاهزة

REM Clean temporary files
echo [3/5] 🧹 تنظيف الملفات المؤقتة...
if exist "%USERPROFILE%\dewan-data\*.db-journal" del "%USERPROFILE%\dewan-data\*.db-journal" >nul 2>&1
if exist "%USERPROFILE%\dewan-data\*.db-wal" del "%USERPROFILE%\dewan-data\*.db-wal" >nul 2>&1
if exist "%USERPROFILE%\dewan-data\*.db-shm" del "%USERPROFILE%\dewan-data\*.db-shm" >nul 2>&1
echo ✅ تم التنظيف

REM Setup database if needed
echo [4/5] 🗄️ إعداد قاعدة البيانات...
if not exist "%USERPROFILE%\dewan-data\dewan.db" (
    echo 🔧 إنشاء قاعدة البيانات...
    if not exist "%USERPROFILE%\dewan-data" mkdir "%USERPROFILE%\dewan-data"
    node -e "console.log('Database will be created automatically')" >nul 2>&1
)
echo ✅ قاعدة البيانات جاهزة

REM Start application
echo [5/5] 🚀 تشغيل التطبيق...
echo.
echo 📝 ملاحظات:
echo • إذا ظهر خطأ قاعدة البيانات: اضغط F12 وشغل fixDatabaseIssue()
echo • للتشخيص: اضغط F12 وشغل runDiagnostics()
echo • للمساعدة: راجع ملف troubleshoot.md
echo.
echo ⏳ جاري التشغيل...

npm start

if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في التشغيل
    echo 🔧 تشغيل الإصلاح التلقائي...
    call quick-fix.bat
)

echo.
echo 👋 شكراً لاستخدام نظام إدارة الديوان
pause
