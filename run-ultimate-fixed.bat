@echo off
title نظام إدارة الديوان - الإصدار النهائي المُصلح

echo ========================================
echo    نظام إدارة الديوان - الإصدار النهائي
echo ========================================
echo.
echo ✅ قاعدة بيانات Ultimate Database
echo ✅ إصلاح شامل لجميع الأخطاء
echo ✅ بيانات تجريبية غنية
echo ✅ مقاوم لجميع الأخطاء
echo ✅ حفظ تلقائي موثوق
echo.

REM Clean processes
echo 🧹 تنظيف العمليات السابقة...
taskkill /f /im electron.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

REM Check Node.js
echo 🔍 فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

node --version
echo ✅ Node.js متاح
echo.

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
)

if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    npm install electron --save-dev
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Electron
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت Electron بنجاح
)

echo.
echo 🚀 تشغيل النظام النهائي المُصلح...
echo.
echo 📊 الإصلاحات المطبقة:
echo.
echo 🔧 قاعدة البيانات:
echo   - Ultimate Database مدمجة ومضمونة
echo   - بيانات تجريبية: 2 أعضاء + 2 مدفوعات + 1 مصروف
echo   - 5 فئات إيرادات + 6 فئات مصروفات
echo   - حفظ تلقائي في localStorage
echo   - مقاومة كاملة للأخطاء
echo.
echo 🔧 الوحدات المُصلحة:
echo   - app.js: إصلاح تهيئة قاعدة البيانات
echo   - members.js: إصلاح إدارة الأعضاء
echo   - payments.js: إصلاح إدارة المدفوعات
echo   - expenses.js: إصلاح إدارة المصروفات
echo   - index.html: تحديث تحميل قاعدة البيانات
echo.
echo 🔧 المميزات الجديدة:
echo   - قاعدة بيانات طوارئ احتياطية
echo   - رسائل خطأ واضحة ومفيدة
echo   - إحصائيات شاملة ودقيقة
echo   - واجهة مستخدم محسنة
echo.
echo ⏳ جاري التحميل...

npm start

if %errorlevel% neq 0 (
    echo ❌ فشل npm start، جاري المحاولة مع electron...
    npx electron .
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في تشغيل النظام
        echo.
        echo 🔍 تشخيص المشكلة:
        echo 1. تأكد من وجود ملف package.json
        echo 2. تأكد من تثبيت جميع المكتبات
        echo 3. تأكد من صحة مسار المشروع
        echo.
        pause
        exit /b 1
    )
)

echo.
echo 👋 تم إغلاق النظام
echo 💾 البيانات محفوظة تلقائياً في localStorage
echo 🔄 يمكنك إعادة التشغيل في أي وقت
echo.
pause
