#!/bin/bash

echo "========================================"
echo "   نظام إدارة الديوان - سطح المكتب"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}📦 $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js غير مثبت على النظام"
    echo "يرجى تثبيت Node.js من: https://nodejs.org"
    echo ""
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

print_success "Node.js مثبت - الإصدار: $(node --version)"
echo ""

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_error "npm غير متاح"
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

print_success "npm متاح - الإصدار: $(npm --version)"
echo ""

# Check if package.json exists
if [ ! -f "package.json" ]; then
    print_error "ملف package.json غير موجود"
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

print_success "ملف package.json موجود"
echo ""

# Check if node_modules exists, if not install dependencies
if [ ! -d "node_modules" ]; then
    print_info "تثبيت المكتبات المطلوبة..."
    echo "هذا قد يستغرق بضع دقائق في المرة الأولى..."
    echo ""
    
    npm install
    if [ $? -ne 0 ]; then
        print_error "فشل في تثبيت المكتبات"
        read -p "اضغط Enter للمتابعة..."
        exit 1
    fi
    
    print_success "تم تثبيت المكتبات بنجاح"
    echo ""
else
    print_success "المكتبات مثبتة مسبقاً"
    echo ""
fi

# Check if Electron is installed
if [ ! -d "node_modules/electron" ]; then
    print_info "تثبيت Electron..."
    npm install electron --save-dev
    if [ $? -ne 0 ]; then
        print_error "فشل في تثبيت Electron"
        read -p "اضغط Enter للمتابعة..."
        exit 1
    fi
    print_success "تم تثبيت Electron بنجاح"
    echo ""
fi

# Create database directory if it doesn't exist
if [ ! -d "database" ]; then
    print_info "إنشاء مجلد قاعدة البيانات..."
    mkdir -p database
    print_success "تم إنشاء مجلد قاعدة البيانات"
    echo ""
fi

# Check if database file exists
if [ ! -f "database/dewan.db" ]; then
    print_info "إنشاء قاعدة البيانات..."
    if [ -f "create-database.js" ]; then
        node create-database.js
        if [ $? -ne 0 ]; then
            print_warning "تحذير: فشل في إنشاء قاعدة البيانات"
            echo "سيتم إنشاؤها تلقائياً عند تشغيل التطبيق"
        else
            print_success "تم إنشاء قاعدة البيانات بنجاح"
        fi
    else
        print_warning "ملف إنشاء قاعدة البيانات غير موجود"
        echo "سيتم إنشاؤها تلقائياً عند تشغيل التطبيق"
    fi
    echo ""
fi

echo "🚀 تشغيل تطبيق نظام إدارة الديوان..."
echo ""
echo "للخروج من التطبيق، أغلق النافذة أو اضغط Ctrl+C هنا"
echo ""

# Start the Electron app
npm start

if [ $? -ne 0 ]; then
    echo ""
    print_error "فشل في تشغيل التطبيق"
    echo ""
    echo "الأخطاء المحتملة:"
    echo "- تأكد من وجود ملف src/main.js"
    echo "- تأكد من صحة ملف package.json"
    echo "- تأكد من تثبيت جميع المكتبات المطلوبة"
    echo ""
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

echo ""
echo "👋 تم إغلاق التطبيق"
read -p "اضغط Enter للمتابعة..."
