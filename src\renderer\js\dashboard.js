// Dashboard management

class DashboardManager {
    constructor() {
        this.chart = null;
        this.init();
    }

    init() {
        // Set current date
        this.updateCurrentDate();
        
        // Load dashboard data
        this.loadData();
        
        // Set up auto-refresh
        setInterval(() => {
            this.updateCurrentDate();
            this.loadData();
        }, 300000); // Refresh every 5 minutes
    }

    updateCurrentDate() {
        const currentDateElement = document.getElementById('current-date');
        if (currentDateElement) {
            currentDateElement.textContent = DateUtils.getCurrentDateArabic();
        }
    }

    async loadData() {
        try {
            await Promise.all([
                this.loadStatistics(),
                this.loadFinancialChart(),
                this.loadRecentActivities()
            ]);
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            UIUtils.showNotification('خطأ في تحميل بيانات لوحة التحكم', 'danger');
        }
    }

    async loadStatistics() {
        try {
            // Get total members
            const totalMembers = await database.get(
                'SELECT COUNT(*) as count FROM members'
            );
            document.getElementById('total-members').textContent = 
                NumberUtils.formatNumber(totalMembers.count);

            // Get active members
            const activeMembers = await database.get(
                'SELECT COUNT(*) as count FROM members WHERE status = "active"'
            );
            document.getElementById('active-members').textContent = 
                NumberUtils.formatNumber(activeMembers.count);

            // Get monthly revenue
            const currentMonth = moment().format('YYYY-MM');
            const monthlyRevenue = await database.get(`
                SELECT COALESCE(SUM(amount), 0) as total 
                FROM payments 
                WHERE strftime('%Y-%m', payment_date) = ?
            `, [currentMonth]);
            document.getElementById('monthly-revenue').textContent = 
                NumberUtils.formatCurrency(monthlyRevenue.total);

            // Get overdue amount
            const overdueAmount = await this.calculateOverdueAmount();
            document.getElementById('overdue-amount').textContent = 
                NumberUtils.formatCurrency(overdueAmount);

        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    async calculateOverdueAmount() {
        try {
            // Get subscription amount from settings
            const subscriptionSetting = await database.get(
                'SELECT value FROM settings WHERE key = "monthly_subscription"'
            );
            const monthlySubscription = parseFloat(subscriptionSetting?.value || 100);

            // Get reminder days from settings
            const reminderDaysSetting = await database.get(
                'SELECT value FROM settings WHERE key = "reminder_days"'
            );
            const reminderDays = parseInt(reminderDaysSetting?.value || 5);

            // Calculate overdue for each active member
            const activeMembers = await database.all(
                'SELECT id, join_date FROM members WHERE status = "active"'
            );

            let totalOverdue = 0;

            for (const member of activeMembers) {
                const memberOverdue = await this.calculateMemberOverdue(
                    member.id, 
                    member.join_date, 
                    monthlySubscription, 
                    reminderDays
                );
                totalOverdue += memberOverdue;
            }

            return totalOverdue;
        } catch (error) {
            console.error('Error calculating overdue amount:', error);
            return 0;
        }
    }

    async calculateMemberOverdue(memberId, joinDate, monthlySubscription, reminderDays) {
        try {
            // Get total payments for this member
            const totalPaid = await database.get(`
                SELECT COALESCE(SUM(amount), 0) as total 
                FROM payments 
                WHERE member_id = ?
            `, [memberId]);

            // Calculate months since joining
            const monthsSinceJoining = moment().diff(moment(joinDate), 'months') + 1;
            
            // Calculate expected total
            const expectedTotal = monthsSinceJoining * monthlySubscription;
            
            // Calculate overdue (if any)
            const overdue = Math.max(0, expectedTotal - totalPaid.total);
            
            // Check if it's actually overdue (considering grace period)
            const lastPayment = await database.get(`
                SELECT payment_date 
                FROM payments 
                WHERE member_id = ? 
                ORDER BY payment_date DESC 
                LIMIT 1
            `, [memberId]);

            if (lastPayment) {
                const daysSinceLastPayment = moment().diff(moment(lastPayment.payment_date), 'days');
                if (daysSinceLastPayment <= (30 + reminderDays)) {
                    return 0; // Not overdue yet
                }
            }

            return overdue;
        } catch (error) {
            console.error('Error calculating member overdue:', error);
            return 0;
        }
    }

    async loadFinancialChart() {
        try {
            // Get last 6 months data
            const months = [];
            const revenues = [];
            const expenses = [];

            for (let i = 5; i >= 0; i--) {
                const month = moment().subtract(i, 'months');
                const monthKey = month.format('YYYY-MM');
                const monthName = month.format('MMM YYYY');

                months.push(monthName);

                // Get revenue for this month
                const revenue = await database.get(`
                    SELECT COALESCE(SUM(amount), 0) as total 
                    FROM payments 
                    WHERE strftime('%Y-%m', payment_date) = ?
                `, [monthKey]);
                revenues.push(revenue.total);

                // Get expenses for this month
                const expense = await database.get(`
                    SELECT COALESCE(SUM(amount), 0) as total 
                    FROM expenses 
                    WHERE strftime('%Y-%m', expense_date) = ?
                `, [monthKey]);
                expenses.push(expense.total);
            }

            this.renderFinancialChart(months, revenues, expenses);
        } catch (error) {
            console.error('Error loading financial chart data:', error);
        }
    }

    renderFinancialChart(months, revenues, expenses) {
        const ctx = document.getElementById('financial-chart');
        if (!ctx) return;

        // Destroy existing chart
        if (this.chart) {
            this.chart.destroy();
        }

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [
                    {
                        label: 'الإيرادات',
                        data: revenues,
                        borderColor: '#198754',
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'المصروفات',
                        data: expenses,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            font: {
                                family: 'Noto Sans Arabic'
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + 
                                       NumberUtils.formatCurrency(context.parsed.y);
                            }
                        },
                        titleFont: {
                            family: 'Noto Sans Arabic'
                        },
                        bodyFont: {
                            family: 'Noto Sans Arabic'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return NumberUtils.formatCurrency(value);
                            },
                            font: {
                                family: 'Noto Sans Arabic'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Noto Sans Arabic'
                            }
                        }
                    }
                }
            }
        });
    }

    async loadRecentActivities() {
        try {
            const activities = [];

            // Get recent payments
            const recentPayments = await database.all(`
                SELECT p.amount, p.payment_date, m.full_name, 'payment' as type
                FROM payments p
                JOIN members m ON p.member_id = m.id
                ORDER BY p.created_at DESC
                LIMIT 5
            `);

            recentPayments.forEach(payment => {
                activities.push({
                    type: 'payment',
                    icon: 'bi-credit-card',
                    color: 'text-success',
                    title: 'دفعة جديدة',
                    description: `${payment.full_name} - ${NumberUtils.formatCurrency(payment.amount)}`,
                    date: DateUtils.formatDate(payment.payment_date),
                    timestamp: payment.payment_date
                });
            });

            // Get recent expenses
            const recentExpenses = await database.all(`
                SELECT e.amount, e.expense_date, e.description, 'expense' as type
                FROM expenses e
                ORDER BY e.created_at DESC
                LIMIT 5
            `);

            recentExpenses.forEach(expense => {
                activities.push({
                    type: 'expense',
                    icon: 'bi-receipt',
                    color: 'text-danger',
                    title: 'مصروف جديد',
                    description: `${expense.description} - ${NumberUtils.formatCurrency(expense.amount)}`,
                    date: DateUtils.formatDate(expense.expense_date),
                    timestamp: expense.expense_date
                });
            });

            // Get recent members
            const recentMembers = await database.all(`
                SELECT full_name, join_date, 'member' as type
                FROM members
                ORDER BY created_at DESC
                LIMIT 3
            `);

            recentMembers.forEach(member => {
                activities.push({
                    type: 'member',
                    icon: 'bi-person-plus',
                    color: 'text-primary',
                    title: 'عضو جديد',
                    description: member.full_name,
                    date: DateUtils.formatDate(member.join_date),
                    timestamp: member.join_date
                });
            });

            // Sort by timestamp and take latest 10
            activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            const latestActivities = activities.slice(0, 10);

            this.renderRecentActivities(latestActivities);
        } catch (error) {
            console.error('Error loading recent activities:', error);
        }
    }

    renderRecentActivities(activities) {
        const container = document.getElementById('recent-activities');
        if (!container) return;

        if (activities.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">لا توجد أنشطة حديثة</p>';
            return;
        }

        container.innerHTML = activities.map(activity => `
            <div class="list-group-item border-0 px-0">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="bi ${activity.icon} ${activity.color} fs-4"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">${activity.title}</h6>
                        <p class="mb-1 text-muted small">${activity.description}</p>
                        <small class="text-muted">${activity.date}</small>
                    </div>
                </div>
            </div>
        `).join('');
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.DashboardManager = new DashboardManager();
});
