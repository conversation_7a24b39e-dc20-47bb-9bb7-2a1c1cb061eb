@echo off
chcp 65001 >nul
echo ========================================
echo    إصلاح شامل للأخطاء - Complete Fix
echo ========================================
echo.

echo 🔧 بدء الإصلاح الشامل...
echo.

REM Step 1: Check Node.js
echo 📋 الخطوة 1: فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 🌐 فتح موقع التحميل...
    start https://nodejs.org/
    echo يرجى تثبيت Node.js وإعادة تشغيل هذا الملف
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM Step 2: Clean everything
echo.
echo 📋 الخطوة 2: تنظيف شامل...
echo 🧹 حذف node_modules...
if exist "node_modules" rmdir /s /q "node_modules" >nul 2>&1

echo 🧹 تنظيف npm cache...
npm cache clean --force >nul 2>&1

echo 🧹 حذف قاعدة البيانات القديمة...
if exist "%USERPROFILE%\dewan-data" (
    rmdir /s /q "%USERPROFILE%\dewan-data" >nul 2>&1
)

echo 🧹 حذف الملفات المؤقتة...
if exist "*.log" del "*.log" >nul 2>&1
if exist "npm-debug.log*" del "npm-debug.log*" >nul 2>&1

echo ✅ تم التنظيف

REM Step 3: Reinstall dependencies
echo.
echo 📋 الخطوة 3: إعادة تثبيت التبعيات...
echo 📦 تثبيت التبعيات...
npm install

if %errorlevel% neq 0 (
    echo ❌ خطأ في تثبيت التبعيات
    echo 🔧 محاولة حل بديل...
    
    REM Try with different registry
    npm install --registry https://registry.npmjs.org/
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        echo يرجى التحقق من:
        echo - اتصال الإنترنت
        echo - إعدادات Firewall
        echo - إعدادات Proxy
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت التبعيات بنجاح

REM Step 4: Create database
echo.
echo 📋 الخطوة 4: إنشاء قاعدة البيانات...
echo 🗄️ إنشاء قاعدة البيانات الجديدة...
node reset-database.js

if %errorlevel% neq 0 (
    echo ⚠️ تحذير: مشكلة في إنشاء قاعدة البيانات
    echo سيتم إنشاؤها تلقائياً عند التشغيل
)

echo ✅ تم إعداد قاعدة البيانات

REM Step 5: Verify installation
echo.
echo 📋 الخطوة 5: فحص التثبيت...

echo 🔍 فحص الملفات المطلوبة...
if not exist "src\main.js" (
    echo ❌ ملف main.js مفقود
    goto :error
)

if not exist "src\renderer\index.html" (
    echo ❌ ملف index.html مفقود
    goto :error
)

if not exist "package.json" (
    echo ❌ ملف package.json مفقود
    goto :error
)

echo ✅ جميع الملفات موجودة

echo 🔍 فحص التبعيات...
npm list sqlite3 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ sqlite3 غير مثبت
    npm install sqlite3
)

npm list electron >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ electron غير مثبت
    npm install electron
)

echo ✅ التبعيات سليمة

REM Step 6: Start application
echo.
echo 📋 الخطوة 6: تشغيل التطبيق...
echo 🚀 بدء تشغيل نظام إدارة الديوان...
echo.
echo 📝 ملاحظات مهمة:
echo - إذا ظهر خطأ "قاعدة البيانات غير متاحة"، اضغط F12 وشغل: fixDatabaseIssue()
echo - إذا ظهر خطأ "innerHTML"، اضغط F12 وشغل: runDiagnostics()
echo - للمساعدة، اضغط F12 وشغل: quickFix()
echo.

npm start

if %errorlevel% neq 0 (
    goto :error
)

echo.
echo 🎉 تم تشغيل التطبيق بنجاح!
goto :end

:error
echo.
echo ❌ حدث خطأ أثناء الإصلاح
echo.
echo 🔧 حلول إضافية:
echo 1. تأكد من تثبيت Node.js بشكل صحيح
echo 2. أعد تشغيل الكمبيوتر
echo 3. شغل Command Prompt كمدير
echo 4. تحقق من اتصال الإنترنت
echo.
echo 📞 للدعم الفني، راجع ملف troubleshoot.md
echo.

:end
pause
