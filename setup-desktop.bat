@echo off
title إعداد نظام إدارة الديوان

echo ========================================
echo      إعداد نظام إدارة الديوان
echo ========================================
echo.
echo هذا الملف سيقوم بإعداد التطبيق للمرة الأولى
echo.

REM Check if Node.js is installed
echo 🔍 فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo.
    echo يرجى اتباع الخطوات التالية:
    echo 1. اذهب إلى: https://nodejs.org
    echo 2. حمل النسخة LTS (الموصى بها)
    echo 3. ثبت Node.js
    echo 4. أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

node --version
echo ✅ Node.js مثبت بنجاح
echo.

REM Check npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

npm --version
echo ✅ npm متاح
echo.

REM Install dependencies
echo 📦 تثبيت المكتبات المطلوبة...
echo هذا قد يستغرق بضع دقائق...
echo.

npm install

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    echo.
    echo حاول الحلول التالية:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. شغل الأمر: npm cache clean --force
    echo 3. أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

echo ✅ تم تثبيت جميع المكتبات بنجاح
echo.

REM Create necessary directories
echo 📁 إنشاء المجلدات المطلوبة...

if not exist "database" (
    mkdir database
    echo ✅ تم إنشاء مجلد database
)

if not exist "assets" (
    mkdir assets
    echo ✅ تم إنشاء مجلد assets
)

if not exist "dist" (
    mkdir dist
    echo ✅ تم إنشاء مجلد dist
)

echo.

REM Test the application
echo 🧪 اختبار التطبيق...
echo.

timeout /t 2 /nobreak >nul

echo ========================================
echo ✅ تم إعداد التطبيق بنجاح!
echo ========================================
echo.
echo الخطوات التالية:
echo.
echo 1. لتشغيل التطبيق: start-desktop.bat
echo 2. لبناء التطبيق: build-desktop.bat
echo 3. لتثبيت مكتبات إضافية: install-dependencies.bat
echo.
echo ملفات مفيدة:
echo - README.md: دليل الاستخدام الكامل
echo - package.json: إعدادات المشروع
echo.

set /p choice="هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 تشغيل التطبيق...
    call start-desktop.bat
) else (
    echo.
    echo 👍 يمكنك تشغيل التطبيق لاحقاً باستخدام start-desktop.bat
    echo.
    pause
)
