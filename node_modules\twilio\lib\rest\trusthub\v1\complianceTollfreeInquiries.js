"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trusthub
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplianceTollfreeInquiriesInstance = exports.ComplianceTollfreeInquiriesListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
function ComplianceTollfreeInquiriesListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/ComplianceInquiries/Tollfree/Initialize`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["tollfreePhoneNumber"] === null ||
            params["tollfreePhoneNumber"] === undefined) {
            throw new Error("Required parameter \"params['tollfreePhoneNumber']\" missing.");
        }
        if (params["notificationEmail"] === null ||
            params["notificationEmail"] === undefined) {
            throw new Error("Required parameter \"params['notificationEmail']\" missing.");
        }
        let data = {};
        data["TollfreePhoneNumber"] = params["tollfreePhoneNumber"];
        data["NotificationEmail"] = params["notificationEmail"];
        if (params["businessName"] !== undefined)
            data["BusinessName"] = params["businessName"];
        if (params["businessWebsite"] !== undefined)
            data["BusinessWebsite"] = params["businessWebsite"];
        if (params["useCaseCategories"] !== undefined)
            data["UseCaseCategories"] = serialize.map(params["useCaseCategories"], (e) => e);
        if (params["useCaseSummary"] !== undefined)
            data["UseCaseSummary"] = params["useCaseSummary"];
        if (params["productionMessageSample"] !== undefined)
            data["ProductionMessageSample"] = params["productionMessageSample"];
        if (params["optInImageUrls"] !== undefined)
            data["OptInImageUrls"] = serialize.map(params["optInImageUrls"], (e) => e);
        if (params["optInType"] !== undefined)
            data["OptInType"] = params["optInType"];
        if (params["messageVolume"] !== undefined)
            data["MessageVolume"] = params["messageVolume"];
        if (params["businessStreetAddress"] !== undefined)
            data["BusinessStreetAddress"] = params["businessStreetAddress"];
        if (params["businessStreetAddress2"] !== undefined)
            data["BusinessStreetAddress2"] = params["businessStreetAddress2"];
        if (params["businessCity"] !== undefined)
            data["BusinessCity"] = params["businessCity"];
        if (params["businessStateProvinceRegion"] !== undefined)
            data["BusinessStateProvinceRegion"] =
                params["businessStateProvinceRegion"];
        if (params["businessPostalCode"] !== undefined)
            data["BusinessPostalCode"] = params["businessPostalCode"];
        if (params["businessCountry"] !== undefined)
            data["BusinessCountry"] = params["businessCountry"];
        if (params["additionalInformation"] !== undefined)
            data["AdditionalInformation"] = params["additionalInformation"];
        if (params["businessContactFirstName"] !== undefined)
            data["BusinessContactFirstName"] = params["businessContactFirstName"];
        if (params["businessContactLastName"] !== undefined)
            data["BusinessContactLastName"] = params["businessContactLastName"];
        if (params["businessContactEmail"] !== undefined)
            data["BusinessContactEmail"] = params["businessContactEmail"];
        if (params["businessContactPhone"] !== undefined)
            data["BusinessContactPhone"] = params["businessContactPhone"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ComplianceTollfreeInquiriesInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ComplianceTollfreeInquiriesListInstance = ComplianceTollfreeInquiriesListInstance;
class ComplianceTollfreeInquiriesInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.inquiryId = payload.inquiry_id;
        this.inquirySessionToken = payload.inquiry_session_token;
        this.registrationId = payload.registration_id;
        this.url = payload.url;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            inquiryId: this.inquiryId,
            inquirySessionToken: this.inquirySessionToken,
            registrationId: this.registrationId,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ComplianceTollfreeInquiriesInstance = ComplianceTollfreeInquiriesInstance;
