@echo off
chcp 65001 >nul
cls

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 تشغيل نهائي محسن                      ║
echo ║                  نظام إدارة الديوان                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص شامل للنظام...

REM Check Node.js
echo [1/6] 📋 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 🌐 فتح موقع التحميل...
    start https://nodejs.org/
    echo.
    echo بعد تثبيت Node.js:
    echo 1. أعد تشغيل الكمبيوتر
    echo 2. شغل هذا الملف مرة أخرى
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM Check dependencies
echo [2/6] 📦 فحص التبعيات...
if not exist "node_modules\electron" (
    echo 📥 تثبيت التبعيات...
    npm install --silent
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت التبعيات
        echo 🔧 محاولة الإصلاح...
        npm cache clean --force
        rmdir /s /q node_modules >nul 2>&1
        npm install
        if %errorlevel% neq 0 (
            echo ❌ فشل في تثبيت التبعيات
            pause
            exit /b 1
        )
    )
)
echo ✅ التبعيات جاهزة

REM Clean temporary files
echo [3/6] 🧹 تنظيف الملفات المؤقتة...
if exist "%USERPROFILE%\dewan-data\*.db-journal" del "%USERPROFILE%\dewan-data\*.db-journal" >nul 2>&1
if exist "%USERPROFILE%\dewan-data\*.db-wal" del "%USERPROFILE%\dewan-data\*.db-wal" >nul 2>&1
if exist "%USERPROFILE%\dewan-data\*.db-shm" del "%USERPROFILE%\dewan-data\*.db-shm" >nul 2>&1
if exist "*.log" del "*.log" >nul 2>&1
echo ✅ تم التنظيف

REM Check core files
echo [4/6] 📁 فحص الملفات الأساسية...
if not exist "src\main.js" (
    echo ❌ ملف main.js مفقود
    goto :error
)
if not exist "src\renderer\index.html" (
    echo ❌ ملف index.html مفقود
    goto :error
)
if not exist "src\renderer\js\database.js" (
    echo ❌ ملف database.js مفقود
    goto :error
)
if not exist "src\renderer\js\members.js" (
    echo ❌ ملف members.js مفقود
    goto :error
)
echo ✅ جميع الملفات موجودة

REM Setup database directory
echo [5/6] 🗄️ إعداد قاعدة البيانات...
if not exist "%USERPROFILE%\dewan-data" (
    mkdir "%USERPROFILE%\dewan-data"
    echo ✅ تم إنشاء مجلد قاعدة البيانات
)
echo ✅ قاعدة البيانات جاهزة

REM Final check
echo [6/6] ✅ فحص نهائي...
npm list electron >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Electron غير مثبت بشكل صحيح
    npm install electron
)
echo ✅ جميع المتطلبات متوفرة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🎉 النظام جاهز                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📝 ملاحظات مهمة:
echo • إذا ظهر خطأ "وحدة قاعدة البيانات غير محملة":
echo   اضغط F12 في المتصفح وشغل: fixModuleLoading()
echo.
echo • إذا ظهر خطأ "قاعدة البيانات غير متاحة":
echo   اضغط F12 في المتصفح وشغل: fixDatabaseIssue()
echo.
echo • إذا ظهر خطأ "تحميل بيانات المدفوعات":
echo   اضغط F12 في المتصفح وشغل: fixPaymentsLoading()
echo.
echo • إذا ظهر خطأ "تحميل بيانات المصروفات":
echo   اضغط F12 في المتصفح وشغل: fixExpensesLoading()
echo.
echo • إذا كانت التقارير لا تعمل:
echo   اضغط F12 في المتصفح وشغل: fixReportsLoading()
echo.
echo • إذا كان الواتساب لا يعمل:
echo   اضغط F12 في المتصفح وشغل: fixWhatsAppLoading()
echo.
echo • للتشخيص الشامل:
echo   اضغط F12 في المتصفح وشغل: runDiagnostics()
echo.
echo • للمساعدة: راجع ملف troubleshoot.md
echo.

echo 🚀 تشغيل نظام إدارة الديوان...
echo.

npm start

if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في التشغيل
    echo 🔧 تشغيل الإصلاح الطارئ...
    call fix-all-errors.bat
    goto :end
)

echo.
echo 🎉 تم تشغيل التطبيق بنجاح!
echo 📱 يمكنك الآن استخدام نظام إدارة الديوان
goto :end

:error
echo.
echo ❌ خطأ في إعداد النظام
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من تحميل جميع ملفات المشروع
echo 2. شغل first-time-setup.bat للإعداد الكامل
echo 3. تحقق من أذونات الملفات
echo.

:end
pause
