@echo off
chcp 65001 >nul
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              الإعداد الأولي - First Time Setup              ║
echo ║                    نظام إدارة الديوان                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 مرحباً بك في نظام إدارة الديوان
echo.
echo هذا الإعداد سيقوم بـ:
echo • فحص وتثبيت Node.js إذا لزم الأمر
echo • تثبيت جميع التبعيات المطلوبة
echo • إنشاء قاعدة البيانات
echo • تشغيل التطبيق لأول مرة
echo.
pause

REM Check Node.js
echo 📋 الخطوة 1: فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo 🔧 تثبيت Node.js مطلوب للمتابعة
    echo سيتم فتح موقع التحميل...
    echo.
    echo بعد التثبيت:
    echo 1. أعد تشغيل الكمبيوتر
    echo 2. شغل هذا الملف مرة أخرى
    echo.
    start https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
node --version
npm --version
echo.

REM Clean installation
echo 📋 الخطوة 2: تنظيف التثبيت السابق...
if exist "node_modules" (
    echo 🧹 حذف node_modules القديم...
    rmdir /s /q "node_modules" >nul 2>&1
)

if exist "%USERPROFILE%\dewan-data" (
    echo 🧹 حذف قاعدة البيانات القديمة...
    rmdir /s /q "%USERPROFILE%\dewan-data" >nul 2>&1
)

echo 🧹 تنظيف npm cache...
npm cache clean --force >nul 2>&1

echo ✅ تم التنظيف
echo.

REM Install dependencies
echo 📋 الخطوة 3: تثبيت التبعيات...
echo 📦 تحميل وتثبيت الحزم المطلوبة...
echo هذا قد يستغرق بضع دقائق...
echo.

npm install

if %errorlevel% neq 0 (
    echo ❌ خطأ في تثبيت التبعيات
    echo.
    echo 🔧 محاولة حل المشكلة...
    npm install --registry https://registry.npmjs.org/
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        echo.
        echo يرجى التحقق من:
        echo • اتصال الإنترنت
        echo • إعدادات Firewall
        echo • إعدادات Proxy
        echo.
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت التبعيات بنجاح
echo.

REM Create database directory
echo 📋 الخطوة 4: إعداد قاعدة البيانات...
if not exist "%USERPROFILE%\dewan-data" (
    mkdir "%USERPROFILE%\dewan-data"
    echo ✅ تم إنشاء مجلد قاعدة البيانات
)

echo 🗄️ قاعدة البيانات ستُنشأ تلقائياً عند التشغيل
echo.

REM Verify installation
echo 📋 الخطوة 5: فحص التثبيت...
echo 🔍 فحص الملفات الأساسية...

if not exist "src\main.js" (
    echo ❌ ملف main.js مفقود
    goto :error
)

if not exist "src\renderer\index.html" (
    echo ❌ ملف index.html مفقود
    goto :error
)

if not exist "package.json" (
    echo ❌ ملف package.json مفقود
    goto :error
)

echo ✅ جميع الملفات موجودة

echo 🔍 فحص التبعيات الأساسية...
npm list electron >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Electron غير مثبت بشكل صحيح
    goto :error
)

npm list sqlite3 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ SQLite3 غير مثبت بشكل صحيح
    goto :error
)

echo ✅ جميع التبعيات سليمة
echo.

REM Success
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🎉 تم الإعداد بنجاح                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ✅ نظام إدارة الديوان جاهز للاستخدام
echo.
echo 📋 للتشغيل في المستقبل:
echo • انقر نقراً مزدوجاً على start.bat
echo • أو شغل run-app.bat
echo.
echo 🚀 تشغيل التطبيق الآن...
echo.

call run-app.bat
goto :end

:error
echo.
echo ❌ حدث خطأ في الإعداد
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من اتصال الإنترنت
echo 2. شغل Command Prompt كمدير
echo 3. أعد تشغيل الكمبيوتر وحاول مرة أخرى
echo.

:end
pause
