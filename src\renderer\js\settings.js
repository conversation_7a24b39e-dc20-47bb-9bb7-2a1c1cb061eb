// Settings management module

class SettingsManager {
    constructor() {
        this.settings = {};
        this.revenueCategories = [];
        this.expenseCategories = [];
        this.users = [];
        this.currentTab = 'general';
        
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
    }

    setupEventListeners() {
        // Tab navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-bs-toggle="tab"]')) {
                this.currentTab = e.target.getAttribute('data-bs-target').replace('#', '');
            }
        });
    }

    async loadSettings() {
        try {
            UIUtils.showLoading();

            // Wait for database module to be loaded
            await this.waitForDatabaseModule();

            // Check if database is available
            if (!window.database || !window.database.db) {
                throw new Error('قاعدة البيانات غير متاحة');
            }

            // Load settings page content if not already loaded
            await this.loadSettingsPageContent();

            // Fetch all data with error handling
            try {
                await Promise.all([
                    this.fetchSettings(),
                    this.fetchCategories(),
                    this.fetchUsers()
                ]);
            } catch (dbError) {
                console.error('Database fetch error:', dbError);
                // Try individual fetches to identify the problem
                try {
                    await this.fetchSettings();
                    console.log('✅ Settings fetched successfully');
                } catch (e) {
                    console.error('❌ Error fetching settings:', e);
                    this.settings = {};
                }

                try {
                    await this.fetchCategories();
                    console.log('✅ Categories fetched successfully');
                } catch (e) {
                    console.error('❌ Error fetching categories:', e);
                    this.revenueCategories = [];
                    this.expenseCategories = [];
                }

                try {
                    await this.fetchUsers();
                    console.log('✅ Users fetched successfully');
                } catch (e) {
                    console.error('❌ Error fetching users:', e);
                    this.users = [];
                }
            }

            // Render all tabs
            this.renderGeneralSettings();
            this.renderCategories();
            this.renderUsers();
            this.renderBackupSettings();

        } catch (error) {
            console.error('Error loading settings:', error);
            UIUtils.showNotification(`خطأ في تحميل الإعدادات: ${error.message}`, 'danger');

            // Show empty state
            this.settings = {};
            this.revenueCategories = [];
            this.expenseCategories = [];
            this.users = [];
            this.renderGeneralSettings();
            this.renderCategories();
            this.renderUsers();
            this.renderBackupSettings();
        } finally {
            UIUtils.hideLoading();
        }
    }

    async waitForDatabaseModule() {
        console.log('🔍 Waiting for database module (Settings)...');

        // If already loaded, return immediately
        if (window.database && window.databaseModuleLoaded) {
            console.log('✅ Database module already loaded (Settings)');
            return;
        }

        // Wait for database module to load
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Database module loading timeout (Settings)'));
            }, 10000); // 10 second timeout

            const checkDatabase = () => {
                if (window.database && window.databaseModuleLoaded) {
                    clearTimeout(timeout);
                    console.log('✅ Database module loaded successfully (Settings)');
                    resolve();
                } else {
                    setTimeout(checkDatabase, 100);
                }
            };

            // Also listen for the database module loaded event
            window.addEventListener('database-module-loaded', () => {
                clearTimeout(timeout);
                console.log('✅ Database module loaded via event (Settings)');
                resolve();
            }, { once: true });

            checkDatabase();
        });
    }

    async fetchSettings() {
        const settingsArray = await database.all('SELECT key, value, description FROM settings ORDER BY key');
        this.settings = {};
        settingsArray.forEach(setting => {
            this.settings[setting.key] = setting;
        });
    }

    async fetchCategories() {
        this.revenueCategories = await database.all(`
            SELECT * FROM revenue_categories ORDER BY name
        `);
        
        this.expenseCategories = await database.all(`
            SELECT * FROM expense_categories ORDER BY name
        `);
    }

    async fetchUsers() {
        this.users = await database.all(`
            SELECT id, username, full_name, role, is_active, last_login, created_at
            FROM users ORDER BY created_at DESC
        `);
    }

    async loadSettingsPageContent() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        const pageElement = document.getElementById('settings-page');
        if (!pageElement) {
            console.error('Settings page element not found, available elements:',
                Array.from(document.querySelectorAll('[id]')).map(el => el.id));
            throw new Error('عنصر صفحة الإعدادات غير موجود في HTML');
        }

        if (pageElement.innerHTML.trim()) {
            console.log('Settings page content already loaded');
            return;
        }

        pageElement.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="bi bi-gear"></i>
                        إعدادات النظام
                    </h2>
                </div>
            </div>

            <!-- Settings Tabs -->
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="settings-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" 
                                    data-bs-target="#general" type="button" role="tab">
                                <i class="bi bi-gear"></i>
                                الإعدادات العامة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="categories-tab" data-bs-toggle="tab" 
                                    data-bs-target="#categories" type="button" role="tab">
                                <i class="bi bi-tags"></i>
                                إدارة الفئات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="users-tab" data-bs-toggle="tab" 
                                    data-bs-target="#users" type="button" role="tab">
                                <i class="bi bi-people"></i>
                                إدارة المستخدمين
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="backup-tab" data-bs-toggle="tab" 
                                    data-bs-target="#backup" type="button" role="tab">
                                <i class="bi bi-shield-check"></i>
                                النسخ الاحتياطي
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="about-tab" data-bs-toggle="tab" 
                                    data-bs-target="#about" type="button" role="tab">
                                <i class="bi bi-info-circle"></i>
                                حول البرنامج
                            </button>
                        </li>
                    </ul>
                </div>
                
                <div class="card-body">
                    <div class="tab-content" id="settings-tab-content">
                        <!-- General Settings Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel">
                            <div id="general-settings-content">
                                <!-- General settings will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- Categories Tab -->
                        <div class="tab-pane fade" id="categories" role="tabpanel">
                            <div id="categories-content">
                                <!-- Categories will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- Users Tab -->
                        <div class="tab-pane fade" id="users" role="tabpanel">
                            <div id="users-content">
                                <!-- Users will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- Backup Tab -->
                        <div class="tab-pane fade" id="backup" role="tabpanel">
                            <div id="backup-content">
                                <!-- Backup settings will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- About Tab -->
                        <div class="tab-pane fade" id="about" role="tabpanel">
                            <div class="text-center py-5">
                                <i class="bi bi-building fs-1 text-primary mb-3"></i>
                                <h3>نظام إدارة الديوان</h3>
                                <p class="lead">نسخة 1.0.0</p>
                                <p class="text-muted">
                                    نظام شامل لإدارة عضوية ومالية المنظمات المجتمعية<br>
                                    مطور باستخدام Electron مع واجهة عربية كاملة
                                </p>
                                
                                <div class="row mt-4">
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h5>المطور</h5>
                                                <p class="mb-0">فريق التطوير</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h5>الترخيص</h5>
                                                <p class="mb-0">MIT License</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h5>تاريخ الإصدار</h5>
                                                <p class="mb-0">${moment().format('YYYY-MM-DD')}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button class="btn btn-outline-primary me-2" onclick="window.SettingsManager.checkForUpdates()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        البحث عن تحديثات
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.SettingsManager.showSystemInfo()">
                                        <i class="bi bi-info-square"></i>
                                        معلومات النظام
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Set up event listeners after content is loaded
        setTimeout(() => this.setupEventListeners(), 100);
    }

    renderGeneralSettings() {
        const container = document.getElementById('general-settings-content');
        if (!container) return;

        container.innerHTML = `
            <form id="general-settings-form">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="bi bi-building"></i>
                            معلومات المنظمة
                        </h5>
                        
                        <div class="mb-3">
                            <label for="organization-name" class="form-label">اسم المنظمة</label>
                            <input type="text" class="form-control" id="organization-name" 
                                   value="${this.settings.organization_name?.value || ''}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="monthly-subscription" class="form-label">قيمة الاشتراك الشهري</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="monthly-subscription" 
                                       value="${this.settings.monthly_subscription?.value || '100'}" 
                                       step="0.01" min="0">
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="currency" class="form-label">العملة</label>
                            <input type="text" class="form-control" id="currency" 
                                   value="${this.settings.currency?.value || 'ر.س'}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="reminder-days" class="form-label">عدد أيام التذكير قبل الاستحقاق</label>
                            <input type="number" class="form-control" id="reminder-days" 
                                   value="${this.settings.reminder_days?.value || '5'}" 
                                   min="1" max="30">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="bi bi-calendar"></i>
                            إعدادات التاريخ والوقت
                        </h5>
                        
                        <div class="mb-3">
                            <label for="date-format" class="form-label">تنسيق التاريخ</label>
                            <select class="form-select" id="date-format">
                                <option value="DD/MM/YYYY" ${this.settings.date_format?.value === 'DD/MM/YYYY' ? 'selected' : ''}>DD/MM/YYYY</option>
                                <option value="MM/DD/YYYY" ${this.settings.date_format?.value === 'MM/DD/YYYY' ? 'selected' : ''}>MM/DD/YYYY</option>
                                <option value="YYYY-MM-DD" ${this.settings.date_format?.value === 'YYYY-MM-DD' ? 'selected' : ''}>YYYY-MM-DD</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="backup-frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                            <select class="form-select" id="backup-frequency">
                                <option value="daily" ${this.settings.backup_frequency?.value === 'daily' ? 'selected' : ''}>يومياً</option>
                                <option value="weekly" ${this.settings.backup_frequency?.value === 'weekly' ? 'selected' : ''}>أسبوعياً</option>
                                <option value="monthly" ${this.settings.backup_frequency?.value === 'monthly' ? 'selected' : ''}>شهرياً</option>
                                <option value="manual" ${this.settings.backup_frequency?.value === 'manual' ? 'selected' : ''}>يدوياً فقط</option>
                            </select>
                        </div>
                        
                        <h5 class="mb-3 mt-4">
                            <i class="bi bi-bell"></i>
                            إعدادات التنبيهات
                        </h5>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable-notifications" 
                                       ${this.settings.enable_notifications?.value === '1' ? 'checked' : ''}>
                                <label class="form-check-label" for="enable-notifications">
                                    تفعيل التنبيهات
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable-sound-notifications" 
                                       ${this.settings.enable_sound_notifications?.value === '1' ? 'checked' : ''}>
                                <label class="form-check-label" for="enable-sound-notifications">
                                    تفعيل الأصوات
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-end mt-4">
                    <button type="button" class="btn btn-primary" onclick="window.SettingsManager.saveGeneralSettings()">
                        <i class="bi bi-check-lg"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </form>
        `;
    }

    async saveGeneralSettings() {
        try {
            UIUtils.showLoading();

            const settings = [
                ['organization_name', document.getElementById('organization-name').value],
                ['monthly_subscription', document.getElementById('monthly-subscription').value],
                ['currency', document.getElementById('currency').value],
                ['reminder_days', document.getElementById('reminder-days').value],
                ['date_format', document.getElementById('date-format').value],
                ['backup_frequency', document.getElementById('backup-frequency').value],
                ['enable_notifications', document.getElementById('enable-notifications').checked ? '1' : '0'],
                ['enable_sound_notifications', document.getElementById('enable-sound-notifications').checked ? '1' : '0']
            ];

            for (const [key, value] of settings) {
                await database.run(`
                    INSERT OR REPLACE INTO settings (key, value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                `, [key, value]);
            }

            UIUtils.showNotification('تم حفظ الإعدادات العامة بنجاح', 'success');
            await this.fetchSettings();

        } catch (error) {
            console.error('Error saving general settings:', error);
            UIUtils.showNotification('خطأ في حفظ الإعدادات', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    renderCategories() {
        const container = document.getElementById('categories-content');
        if (!container) return;

        container.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>
                            <i class="bi bi-cash-coin"></i>
                            فئات الإيرادات
                        </h5>
                        <button class="btn btn-sm btn-primary" onclick="window.SettingsManager.showAddCategoryModal('revenue')">
                            <i class="bi bi-plus"></i>
                            إضافة فئة
                        </button>
                    </div>

                    <div class="list-group">
                        ${this.revenueCategories.map(category => `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">${category.name}</h6>
                                    <small class="text-muted">${category.description || 'لا يوجد وصف'}</small>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-secondary" onclick="window.SettingsManager.editCategory('revenue', ${category.id})" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="window.SettingsManager.deleteCategory('revenue', ${category.id})" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>
                            <i class="bi bi-receipt"></i>
                            فئات المصروفات
                        </h5>
                        <button class="btn btn-sm btn-primary" onclick="window.SettingsManager.showAddCategoryModal('expense')">
                            <i class="bi bi-plus"></i>
                            إضافة فئة
                        </button>
                    </div>

                    <div class="list-group">
                        ${this.expenseCategories.map(category => `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">${category.name}</h6>
                                    <small class="text-muted">${category.description || 'لا يوجد وصف'}</small>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-secondary" onclick="window.SettingsManager.editCategory('expense', ${category.id})" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="window.SettingsManager.deleteCategory('expense', ${category.id})" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    renderUsers() {
        const container = document.getElementById('users-content');
        if (!container) return;

        container.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>
                    <i class="bi bi-people"></i>
                    إدارة المستخدمين
                </h5>
                <button class="btn btn-primary" onclick="window.SettingsManager.showAddUserModal()">
                    <i class="bi bi-person-plus"></i>
                    إضافة مستخدم
                </button>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>آخر دخول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.users.map(user => `
                            <tr>
                                <td><strong>${user.username}</strong></td>
                                <td>${user.full_name}</td>
                                <td>${this.getRoleText(user.role)}</td>
                                <td>${user.is_active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>'}</td>
                                <td>${user.last_login ? DateUtils.formatDate(user.last_login) : 'لم يدخل بعد'}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary" onclick="window.SettingsManager.editUser(${user.id})" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        ${user.username !== 'admin' ? `
                                            <button class="btn btn-outline-danger" onclick="window.SettingsManager.deleteUser(${user.id})" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        ` : ''}
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    renderBackupSettings() {
        const container = document.getElementById('backup-content');
        if (!container) return;

        container.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">
                        <i class="bi bi-shield-check"></i>
                        النسخ الاحتياطي
                    </h5>

                    <div class="card">
                        <div class="card-body">
                            <h6>إنشاء نسخة احتياطية يدوية</h6>
                            <p class="text-muted">إنشاء نسخة احتياطية فورية من قاعدة البيانات</p>
                            <button class="btn btn-primary" onclick="window.SettingsManager.createBackup()">
                                <i class="bi bi-download"></i>
                                إنشاء نسخة احتياطية
                            </button>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-body">
                            <h6>استعادة من نسخة احتياطية</h6>
                            <p class="text-muted">استعادة البيانات من ملف نسخة احتياطية</p>
                            <input type="file" class="form-control mb-2" id="backup-file" accept=".db">
                            <button class="btn btn-warning" onclick="window.SettingsManager.restoreBackup()">
                                <i class="bi bi-upload"></i>
                                استعادة النسخة الاحتياطية
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <h5 class="mb-3">
                        <i class="bi bi-gear"></i>
                        صيانة النظام
                    </h5>

                    <div class="card">
                        <div class="card-body">
                            <h6>تحسين قاعدة البيانات</h6>
                            <p class="text-muted">تحسين أداء قاعدة البيانات وتنظيف البيانات المؤقتة</p>
                            <button class="btn btn-info" onclick="window.SettingsManager.optimizeDatabase()">
                                <i class="bi bi-arrow-clockwise"></i>
                                تحسين قاعدة البيانات
                            </button>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-body">
                            <h6>مسح البيانات المؤقتة</h6>
                            <p class="text-muted">مسح الملفات المؤقتة والسجلات القديمة</p>
                            <button class="btn btn-secondary" onclick="window.SettingsManager.clearTempData()">
                                <i class="bi bi-trash"></i>
                                مسح البيانات المؤقتة
                            </button>
                        </div>
                    </div>

                    <div class="card mt-3 border-danger">
                        <div class="card-body">
                            <h6 class="text-danger">إعادة تعيين النظام</h6>
                            <p class="text-muted">حذف جميع البيانات وإعادة النظام لحالته الأولى</p>
                            <button class="btn btn-danger" onclick="window.SettingsManager.resetSystem()">
                                <i class="bi bi-exclamation-triangle"></i>
                                إعادة تعيين النظام
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getRoleText(role) {
        const roles = {
            'admin': 'مدير',
            'user': 'مستخدم',
            'viewer': 'مشاهد'
        };
        return roles[role] || role;
    }

    showAddCategoryModal(type) {
        const modalId = `add-category-modal-${Date.now()}`;
        const isRevenue = type === 'revenue';

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-${isRevenue ? 'cash-coin' : 'receipt'}"></i>
                            إضافة فئة ${isRevenue ? 'إيراد' : 'مصروف'} جديدة
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="category-form">
                            <div class="mb-3">
                                <label for="category-name" class="form-label">اسم الفئة *</label>
                                <input type="text" class="form-control" id="category-name" required>
                            </div>

                            <div class="mb-3">
                                <label for="category-description" class="form-label">الوصف</label>
                                <textarea class="form-control" id="category-description" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="window.SettingsManager.saveCategory('${type}', null, '${modalId}')">
                            <i class="bi bi-check-lg"></i>
                            إضافة الفئة
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    async saveCategory(type, categoryId, modalId) {
        try {
            const name = document.getElementById('category-name').value.trim();
            const description = document.getElementById('category-description').value.trim();

            if (!name) {
                UIUtils.showNotification('اسم الفئة مطلوب', 'warning');
                return;
            }

            UIUtils.showLoading();

            const tableName = type === 'revenue' ? 'revenue_categories' : 'expense_categories';

            if (categoryId) {
                // Update existing category
                await database.run(`
                    UPDATE ${tableName}
                    SET name = ?, description = ?
                    WHERE id = ?
                `, [name, description, categoryId]);
            } else {
                // Add new category
                await database.run(`
                    INSERT INTO ${tableName} (name, description)
                    VALUES (?, ?)
                `, [name, description]);
            }

            UIUtils.showNotification('تم حفظ الفئة بنجاح', 'success');

            // Close modal and refresh data
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            modal.hide();

            await this.fetchCategories();
            this.renderCategories();

        } catch (error) {
            console.error('Error saving category:', error);
            UIUtils.showNotification('خطأ في حفظ الفئة', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    async deleteCategory(type, categoryId) {
        try {
            const tableName = type === 'revenue' ? 'revenue_categories' : 'expense_categories';
            const category = await database.get(`SELECT name FROM ${tableName} WHERE id = ?`, [categoryId]);

            if (!category) {
                UIUtils.showNotification('الفئة غير موجودة', 'warning');
                return;
            }

            const confirmed = await UIUtils.showConfirmDialog(
                'تأكيد الحذف',
                `هل أنت متأكد من حذف فئة "${category.name}"؟\nسيتم تعطيل الفئة بدلاً من حذفها للحفاظ على البيانات المرتبطة.`,
                'تعطيل',
                'إلغاء'
            );

            if (confirmed) {
                await database.run(`UPDATE ${tableName} SET is_active = 0 WHERE id = ?`, [categoryId]);
                UIUtils.showNotification('تم تعطيل الفئة بنجاح', 'success');

                await this.fetchCategories();
                this.renderCategories();
            }
        } catch (error) {
            console.error('Error deleting category:', error);
            UIUtils.showNotification('خطأ في حذف الفئة', 'danger');
        }
    }

    async createBackup() {
        try {
            const { ipcRenderer } = require('electron');
            const result = await ipcRenderer.invoke('show-save-dialog', {
                title: 'حفظ النسخة الاحتياطية',
                defaultPath: `dewan-backup-${moment().format('YYYY-MM-DD-HH-mm')}.db`,
                filters: [
                    { name: 'Database Files', extensions: ['db'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                UIUtils.showLoading();
                await database.backup(result.filePath);
                UIUtils.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }
        } catch (error) {
            console.error('Backup error:', error);
            UIUtils.showNotification('خطأ في إنشاء النسخة الاحتياطية', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    checkForUpdates() {
        UIUtils.showNotification('سيتم تطوير ميزة التحديثات قريباً', 'info');
    }

    showSystemInfo() {
        const systemInfo = window.app?.getSystemInfo() || {};

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">معلومات النظام</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>نسخة التطبيق:</strong></td>
                                <td>${systemInfo.version || '1.0.0'}</td>
                            </tr>
                            <tr>
                                <td><strong>المنصة:</strong></td>
                                <td>${systemInfo.platform || 'غير معروف'}</td>
                            </tr>
                            <tr>
                                <td><strong>نسخة Node.js:</strong></td>
                                <td>${systemInfo.nodeVersion || 'غير معروف'}</td>
                            </tr>
                            <tr>
                                <td><strong>نسخة Electron:</strong></td>
                                <td>${systemInfo.electronVersion || 'غير معروف'}</td>
                            </tr>
                            <tr>
                                <td><strong>نسخة Chrome:</strong></td>
                                <td>${systemInfo.chromeVersion || 'غير معروف'}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    // Placeholder methods for future implementation
    editCategory(type, categoryId) {
        UIUtils.showNotification('سيتم تطوير تعديل الفئات قريباً', 'info');
    }

    showAddUserModal() {
        UIUtils.showNotification('سيتم تطوير إدارة المستخدمين قريباً', 'info');
    }

    editUser(userId) {
        UIUtils.showNotification('سيتم تطوير تعديل المستخدمين قريباً', 'info');
    }

    deleteUser(userId) {
        UIUtils.showNotification('سيتم تطوير حذف المستخدمين قريباً', 'info');
    }

    restoreBackup() {
        UIUtils.showNotification('سيتم تطوير استعادة النسخ الاحتياطية قريباً', 'info');
    }

    optimizeDatabase() {
        UIUtils.showNotification('سيتم تطوير تحسين قاعدة البيانات قريباً', 'info');
    }

    clearTempData() {
        UIUtils.showNotification('سيتم تطوير مسح البيانات المؤقتة قريباً', 'info');
    }

    resetSystem() {
        UIUtils.showNotification('سيتم تطوير إعادة تعيين النظام قريباً', 'info');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.SettingsManager = new SettingsManager();
        console.log('SettingsManager initialized successfully');

        // Try to load settings if database is ready
        setTimeout(() => {
            if (window.database && window.database.isReady) {
                console.log('Database is ready, loading settings...');
                window.SettingsManager.loadSettings().catch(error => {
                    console.log('Initial settings load failed, will retry when database is ready');
                });
            }
        }, 1000);

    } catch (error) {
        console.error('Error initializing SettingsManager:', error);
        if (window.UIUtils) {
            window.UIUtils.showNotification('خطأ في تهيئة نظام الإعدادات', 'danger');
        }
    }
});

// Add database ready listener
window.addEventListener('database-ready', () => {
    console.log('Database ready, SettingsManager can now load data');
    if (window.SettingsManager) {
        setTimeout(() => {
            window.SettingsManager.loadSettings().catch(error => {
                console.error('Error loading settings after database ready:', error);
            });
        }, 500);
    }
});
