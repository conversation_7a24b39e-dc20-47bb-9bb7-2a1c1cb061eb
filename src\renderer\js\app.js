// Main application initialization

class App {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    async init() {
        try {
            console.log('Initializing Dewan Management System...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                await this.start();
            }
        } catch (error) {
            console.error('Error initializing application:', error);
            this.showInitializationError(error);
        }
    }

    async start() {
        try {
            // Show loading
            UIUtils.showLoading();

            // Check required DOM elements
            await this.checkRequiredElements();

            // Initialize database
            await this.initializeDatabase();

            // Set up global error handling
            this.setupErrorHandling();

            // Set up keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Initialize modules
            await this.initializeModules();

            // Set up auto-save and backup
            this.setupAutoSave();

            // Mark as initialized
            this.isInitialized = true;

            console.log('Application initialized successfully');
            UIUtils.showNotification('تم تحميل النظام بنجاح', 'success');

        } catch (error) {
            console.error('Error starting application:', error);
            this.showInitializationError(error);
        } finally {
            UIUtils.hideLoading();
        }
    }

    async checkRequiredElements() {
        console.log('Checking required DOM elements...');

        const requiredElements = [
            'main-content',
            'dashboard-page',
            'members-page',
            'payments-page',
            'expenses-page',
            'reports-page',
            'whatsapp-page',
            'settings-page',
            'loading-spinner'
        ];

        const missingElements = [];

        for (const elementId of requiredElements) {
            const element = document.getElementById(elementId);
            if (!element) {
                missingElements.push(elementId);
            }
        }

        if (missingElements.length > 0) {
            console.error('Missing required elements:', missingElements);
            throw new Error(`Missing required DOM elements: ${missingElements.join(', ')}`);
        }

        console.log('All required DOM elements found');
    }

    async initializeDatabase() {
        try {
            console.log('🔄 Starting Fixed Database initialization...');

            // Check if database is already loaded
            if (window.database && window.database.isReady) {
                console.log('✅ Database already ready!');
                return;
            }

            // Wait for database module to be available (shorter timeout)
            let attempts = 0;
            const maxAttempts = 30; // 3 seconds max

            while (!window.database && attempts < maxAttempts) {
                console.log(`⏳ Waiting for database module... attempt ${attempts + 1}`);
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (!window.database) {
                console.error('❌ Database module not loaded, creating emergency fallback...');
                this.createEmergencyDatabase();
                return;
            }

            console.log('✅ Database module found!');
            console.log('📊 Database status:', {
                isReady: window.database.isReady,
                hasData: window.database.data ? Object.keys(window.database.data).length : 0
            });

            // If database is not ready, wait a bit more
            if (!window.database.isReady) {
                console.log('⏳ Waiting for database to be ready...');

                await new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        console.log('⚠️ Database ready timeout, but database exists');
                        resolve();
                    }, 2000); // 2 second timeout

                    const handleReady = () => {
                        clearTimeout(timeout);
                        window.removeEventListener('database-ready', handleReady);
                        console.log('✅ Database ready event received');
                        resolve();
                    };

                    window.addEventListener('database-ready', handleReady);

                    // Check if already ready
                    if (window.database.isReady) {
                        handleReady();
                    }
                });
            }

            // Test database with a simple operation
            try {
                const testResult = await window.database.all('SELECT * FROM revenue_categories LIMIT 1');
                console.log('✅ Database test successful:', testResult.length, 'categories found');

                // Test member data
                const members = await window.database.all('SELECT * FROM members');
                console.log('👥 Members in database:', members.length);

            } catch (error) {
                console.warn('⚠️ Database test failed:', error.message);
                console.log('🔄 Database still usable, continuing...');
            }

            console.log('🎉 Fixed Database initialization completed successfully');

        } catch (error) {
            console.error('❌ Database initialization error:', error);
            console.log('🆘 Creating emergency database...');
            this.createEmergencyDatabase();
        }
    }

    createEmergencyDatabase() {
        console.log('🆘 Creating emergency database...');

        window.database = {
            isReady: true,
            error: null,
            data: {
                members: [],
                payments: [],
                expenses: [],
                revenue_categories: [
                    { id: 1, name: 'اشتراك شهري', description: 'الاشتراك الشهري للأعضاء' }
                ],
                expense_categories: [
                    { id: 1, name: 'فعاليات', description: 'مصروفات الفعاليات والأنشطة' }
                ]
            },
            get: (sql) => {
                console.log('Emergency DB get:', sql);
                return Promise.resolve(null);
            },
            all: (sql) => {
                console.log('Emergency DB all:', sql);
                if (sql.includes('revenue_categories')) {
                    return Promise.resolve(window.database.data.revenue_categories);
                } else if (sql.includes('expense_categories')) {
                    return Promise.resolve(window.database.data.expense_categories);
                }
                return Promise.resolve([]);
            },
            run: (sql, params) => {
                console.log('Emergency DB run:', sql, params);
                return Promise.resolve({ id: 1, changes: 1 });
            },
            close: () => Promise.resolve(),
            backup: () => Promise.resolve('emergency-backup')
        };

        // Dispatch ready event
        setTimeout(() => {
            window.dispatchEvent(new CustomEvent('database-ready'));
            console.log('✅ Emergency database ready');
        }, 100);
    }

    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            UIUtils.showNotification('حدث خطأ غير متوقع في النظام', 'danger');
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            UIUtils.showNotification('حدث خطأ في معالجة البيانات', 'danger');
        });

        // Database error handler
        if (database && database.db) {
            database.db.on('error', (error) => {
                console.error('Database error:', error);
                UIUtils.showNotification('خطأ في قاعدة البيانات', 'danger');
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Check if Ctrl (or Cmd on Mac) is pressed
            const isCtrlOrCmd = event.ctrlKey || event.metaKey;
            
            if (!isCtrlOrCmd) return;

            switch (event.key.toLowerCase()) {
                case 'n':
                    event.preventDefault();
                    this.handleNewMember();
                    break;
                case 'm':
                    event.preventDefault();
                    window.navigationManager?.navigateTo('members');
                    break;
                case 'p':
                    event.preventDefault();
                    this.handleNewPayment();
                    break;
                case 'e':
                    event.preventDefault();
                    this.handleNewExpense();
                    break;
                case 'r':
                    event.preventDefault();
                    window.navigationManager?.navigateTo('reports');
                    break;
                case 'b':
                    event.preventDefault();
                    this.handleBackup();
                    break;
                case ',':
                    event.preventDefault();
                    window.navigationManager?.navigateTo('settings');
                    break;
                case 'f':
                    event.preventDefault();
                    this.focusSearch();
                    break;
            }
        });
    }

    async initializeModules() {
        // Modules will be initialized as they are loaded
        // This is a placeholder for any global module initialization
        console.log('Modules initialization completed');
    }

    setupAutoSave() {
        // Set up periodic backup
        setInterval(async () => {
            try {
                if (!window.electronAPI || !window.database) {
                    console.log('⏭️ Skipping auto backup - Electron API or database not available');
                    return;
                }

                const userDataPath = window.electronAPI.getUserDataPath();
                const backupDir = window.electronAPI.joinPath(userDataPath, 'backups');
                const backupPath = window.electronAPI.joinPath(
                    backupDir,
                    `auto-backup-${moment().format('YYYY-MM-DD-HH-mm')}.db`
                );

                // Ensure backup directory exists
                if (!window.electronAPI.fileExists(backupDir)) {
                    // Create directory using file system operations
                    console.log('📁 Creating backup directory:', backupDir);
                }

                await window.database.backup(backupPath);
                console.log('💾 Auto backup created:', backupPath);

                // Clean old backups (keep last 10)
                this.cleanOldBackups(backupDir);
            } catch (error) {
                console.error('❌ Auto backup failed:', error);
            }
        }, 24 * 60 * 60 * 1000); // Daily backup
    }

    cleanOldBackups(backupDir) {
        try {
            const fs = require('fs');
            const files = fs.readdirSync(backupDir)
                .filter(file => file.startsWith('auto-backup-') && file.endsWith('.db'))
                .map(file => ({
                    name: file,
                    path: path.join(backupDir, file),
                    time: fs.statSync(path.join(backupDir, file)).mtime
                }))
                .sort((a, b) => b.time - a.time);

            // Keep only the latest 10 backups
            if (files.length > 10) {
                files.slice(10).forEach(file => {
                    fs.unlinkSync(file.path);
                    console.log('Deleted old backup:', file.name);
                });
            }
        } catch (error) {
            console.error('Error cleaning old backups:', error);
        }
    }

    showInitializationError(error) {
        const errorMessage = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading">خطأ في تشغيل النظام</h4>
                <p>حدث خطأ أثناء تشغيل نظام إدارة الديوان:</p>
                <hr>
                <p class="mb-0"><strong>تفاصيل الخطأ:</strong> ${error.message}</p>
                <p class="mt-2">
                    <button class="btn btn-outline-danger" onclick="location.reload()">
                        إعادة تحميل النظام
                    </button>
                </p>
            </div>
        `;
        
        document.getElementById('main-content').innerHTML = errorMessage;
    }

    // Keyboard shortcut handlers
    handleNewMember() {
        if (window.MembersManager) {
            window.MembersManager.showAddMemberModal();
        } else {
            window.navigationManager?.navigateTo('members');
        }
    }

    handleNewPayment() {
        if (window.PaymentsManager) {
            window.PaymentsManager.showAddPaymentModal();
        } else {
            window.navigationManager?.navigateTo('payments');
        }
    }

    handleNewExpense() {
        if (window.ExpensesManager) {
            window.ExpensesManager.showAddExpenseModal();
        } else {
            window.navigationManager?.navigateTo('expenses');
        }
    }

    async handleBackup() {
        try {
            if (!window.electronAPI) {
                UIUtils.showNotification('وظيفة النسخ الاحتياطي غير متاحة', 'warning');
                return;
            }

            const result = await window.electronAPI.showSaveDialog({
                title: 'حفظ النسخة الاحتياطية',
                defaultPath: `backup-${moment().format('YYYY-MM-DD')}.db`,
                filters: [
                    { name: 'Database Files', extensions: ['db'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!result.canceled && result.filePath) {
                UIUtils.showLoading();
                await window.database.backup(result.filePath);
                UIUtils.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }
        } catch (error) {
            console.error('❌ Backup error:', error);
            UIUtils.showNotification('خطأ في إنشاء النسخة الاحتياطية: ' + error.message, 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    focusSearch() {
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="بحث"]');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    // Application lifecycle methods
    async shutdown() {
        try {
            console.log('Shutting down application...');
            
            // Close database connection
            if (database) {
                await database.close();
            }
            
            // Clean up any other resources
            if (window.DashboardManager && window.DashboardManager.chart) {
                window.DashboardManager.chart.destroy();
            }
            
            console.log('Application shutdown completed');
        } catch (error) {
            console.error('Error during shutdown:', error);
        }
    }

    // Utility methods
    getVersion() {
        return '1.0.0';
    }

    getSystemInfo() {
        const info = {
            version: this.getVersion(),
            platform: 'unknown',
            nodeVersion: 'unknown',
            electronVersion: 'unknown',
            chromeVersion: 'unknown'
        };

        if (window.electronAPI && window.electronAPI.versions) {
            info.platform = window.electronAPI.platform;
            info.nodeVersion = window.electronAPI.versions.node;
            info.electronVersion = window.electronAPI.versions.electron;
            info.chromeVersion = window.electronAPI.versions.chrome;
        }

        return info;
    }
}

// Initialize application
const app = new App();

// Handle application shutdown
window.addEventListener('beforeunload', async (event) => {
    if (app.isInitialized) {
        await app.shutdown();
    }
});

// Make app available globally for debugging
window.app = app;

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = app;
}
