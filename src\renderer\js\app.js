// Main application initialization

class App {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    async init() {
        try {
            console.log('Initializing Dewan Management System...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                await this.start();
            }
        } catch (error) {
            console.error('Error initializing application:', error);
            this.showInitializationError(error);
        }
    }

    async start() {
        try {
            // Show loading
            UIUtils.showLoading();

            // Initialize database
            await this.initializeDatabase();

            // Set up global error handling
            this.setupErrorHandling();

            // Set up keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Initialize modules
            await this.initializeModules();

            // Set up auto-save and backup
            this.setupAutoSave();

            // Mark as initialized
            this.isInitialized = true;

            console.log('Application initialized successfully');
            UIUtils.showNotification('تم تحميل النظام بنجاح', 'success');

        } catch (error) {
            console.error('Error starting application:', error);
            this.showInitializationError(error);
        } finally {
            UIUtils.hideLoading();
        }
    }

    async initializeDatabase() {
        try {
            // Database is already initialized in database.js
            // Just verify it's working
            const testQuery = await database.get('SELECT COUNT(*) as count FROM sqlite_master WHERE type="table"');
            console.log(`Database initialized with ${testQuery.count} tables`);
        } catch (error) {
            throw new Error(`Database initialization failed: ${error.message}`);
        }
    }

    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            UIUtils.showNotification('حدث خطأ غير متوقع في النظام', 'danger');
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            UIUtils.showNotification('حدث خطأ في معالجة البيانات', 'danger');
        });

        // Database error handler
        if (database && database.db) {
            database.db.on('error', (error) => {
                console.error('Database error:', error);
                UIUtils.showNotification('خطأ في قاعدة البيانات', 'danger');
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Check if Ctrl (or Cmd on Mac) is pressed
            const isCtrlOrCmd = event.ctrlKey || event.metaKey;
            
            if (!isCtrlOrCmd) return;

            switch (event.key.toLowerCase()) {
                case 'n':
                    event.preventDefault();
                    this.handleNewMember();
                    break;
                case 'm':
                    event.preventDefault();
                    window.navigationManager?.navigateTo('members');
                    break;
                case 'p':
                    event.preventDefault();
                    this.handleNewPayment();
                    break;
                case 'e':
                    event.preventDefault();
                    this.handleNewExpense();
                    break;
                case 'r':
                    event.preventDefault();
                    window.navigationManager?.navigateTo('reports');
                    break;
                case 'b':
                    event.preventDefault();
                    this.handleBackup();
                    break;
                case ',':
                    event.preventDefault();
                    window.navigationManager?.navigateTo('settings');
                    break;
                case 'f':
                    event.preventDefault();
                    this.focusSearch();
                    break;
            }
        });
    }

    async initializeModules() {
        // Modules will be initialized as they are loaded
        // This is a placeholder for any global module initialization
        console.log('Modules initialization completed');
    }

    setupAutoSave() {
        // Set up periodic backup
        setInterval(async () => {
            try {
                const backupPath = path.join(
                    require('electron').remote.app.getPath('userData'),
                    'backups',
                    `auto-backup-${moment().format('YYYY-MM-DD-HH-mm')}.db`
                );
                
                // Ensure backup directory exists
                const backupDir = path.dirname(backupPath);
                if (!require('fs').existsSync(backupDir)) {
                    require('fs').mkdirSync(backupDir, { recursive: true });
                }

                await database.backup(backupPath);
                console.log('Auto backup created:', backupPath);
                
                // Clean old backups (keep last 10)
                this.cleanOldBackups(backupDir);
            } catch (error) {
                console.error('Auto backup failed:', error);
            }
        }, 24 * 60 * 60 * 1000); // Daily backup
    }

    cleanOldBackups(backupDir) {
        try {
            const fs = require('fs');
            const files = fs.readdirSync(backupDir)
                .filter(file => file.startsWith('auto-backup-') && file.endsWith('.db'))
                .map(file => ({
                    name: file,
                    path: path.join(backupDir, file),
                    time: fs.statSync(path.join(backupDir, file)).mtime
                }))
                .sort((a, b) => b.time - a.time);

            // Keep only the latest 10 backups
            if (files.length > 10) {
                files.slice(10).forEach(file => {
                    fs.unlinkSync(file.path);
                    console.log('Deleted old backup:', file.name);
                });
            }
        } catch (error) {
            console.error('Error cleaning old backups:', error);
        }
    }

    showInitializationError(error) {
        const errorMessage = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading">خطأ في تشغيل النظام</h4>
                <p>حدث خطأ أثناء تشغيل نظام إدارة الديوان:</p>
                <hr>
                <p class="mb-0"><strong>تفاصيل الخطأ:</strong> ${error.message}</p>
                <p class="mt-2">
                    <button class="btn btn-outline-danger" onclick="location.reload()">
                        إعادة تحميل النظام
                    </button>
                </p>
            </div>
        `;
        
        document.getElementById('main-content').innerHTML = errorMessage;
    }

    // Keyboard shortcut handlers
    handleNewMember() {
        if (window.MembersManager) {
            window.MembersManager.showAddMemberModal();
        } else {
            window.navigationManager?.navigateTo('members');
        }
    }

    handleNewPayment() {
        if (window.PaymentsManager) {
            window.PaymentsManager.showAddPaymentModal();
        } else {
            window.navigationManager?.navigateTo('payments');
        }
    }

    handleNewExpense() {
        if (window.ExpensesManager) {
            window.ExpensesManager.showAddExpenseModal();
        } else {
            window.navigationManager?.navigateTo('expenses');
        }
    }

    async handleBackup() {
        try {
            const { ipcRenderer } = require('electron');
            const result = await ipcRenderer.invoke('show-save-dialog', {
                title: 'حفظ النسخة الاحتياطية',
                defaultPath: `backup-${moment().format('YYYY-MM-DD')}.db`,
                filters: [
                    { name: 'Database Files', extensions: ['db'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                UIUtils.showLoading();
                await database.backup(result.filePath);
                UIUtils.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }
        } catch (error) {
            console.error('Backup error:', error);
            UIUtils.showNotification('خطأ في إنشاء النسخة الاحتياطية', 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    focusSearch() {
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="بحث"]');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    // Application lifecycle methods
    async shutdown() {
        try {
            console.log('Shutting down application...');
            
            // Close database connection
            if (database) {
                await database.close();
            }
            
            // Clean up any other resources
            if (window.DashboardManager && window.DashboardManager.chart) {
                window.DashboardManager.chart.destroy();
            }
            
            console.log('Application shutdown completed');
        } catch (error) {
            console.error('Error during shutdown:', error);
        }
    }

    // Utility methods
    getVersion() {
        return '1.0.0';
    }

    getSystemInfo() {
        return {
            version: this.getVersion(),
            platform: process.platform,
            nodeVersion: process.version,
            electronVersion: process.versions.electron,
            chromeVersion: process.versions.chrome
        };
    }
}

// Initialize application
const app = new App();

// Handle application shutdown
window.addEventListener('beforeunload', async (event) => {
    if (app.isInitialized) {
        await app.shutdown();
    }
});

// Make app available globally for debugging
window.app = app;

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = app;
}
