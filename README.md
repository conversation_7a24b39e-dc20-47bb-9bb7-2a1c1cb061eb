# نظام إدارة الديوان - Dewan Management System

نظام شامل لإدارة عضوية ومالية المنظمات المجتمعية باستخدام Electron مع واجهة عربية كاملة.

## المتطلبات الأساسية

### 1. تثبيت Node.js
- قم بتحميل وتثبيت Node.js من الموقع الرسمي: https://nodejs.org/
- اختر النسخة LTS (Long Term Support)
- تأكد من تثبيت npm مع Node.js

### 2. التحقق من التثبيت
```bash
node --version
npm --version
```

## تثبيت وتشغيل التطبيق

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. تشغيل التطبيق في وضع التطوير
```bash
npm run dev
```

### 3. تشغيل التطبيق العادي
```bash
npm start
```

### 4. بناء التطبيق للتوزيع
```bash
npm run build-win
```

## الميزات الرئيسية

### 1. إدارة الأعضاء
- إضافة أعضاء جدد مع معلومات كاملة
- تعديل بيانات الأعضاء الموجودين
- حذف الأعضاء مع تأكيد
- البحث في الأعضاء بالاسم أو الهاتف أو رقم العضوية
- تتبع حالة العضوية (نشط/غير نشط)
- ربط الأعضاء بسجل المدفوعات والمتأخرات

### 2. إدارة الإيرادات
- تسجيل المدفوعات الجديدة
- دعم أنواع إيرادات متعددة (اشتراك شهري، تبرعات، دعم فعاليات)
- البحث في المدفوعات حسب العضو أو التاريخ أو النوع
- تقارير إيرادات مفصلة لفترات محددة
- تتبع تواريخ الاستحقاق والمتأخرات

### 3. إدارة المصروفات
- تسجيل المصروفات مع التصنيف والوصف
- دعم فئات مصروفات متعددة (فعاليات، خدمات، دعم حالات، فواتير)
- إرفاق صور أو ملفات (إيصالات/فواتير)
- تقارير مصروفات مفصلة
- مقارنة الإيرادات مع المصروفات برسوم بيانية

### 4. نظام التقارير
- تقارير مالية شهرية وسنوية
- كشوف حسابات الأعضاء الفردية
- تقرير المتأخرات
- تقرير الأعضاء المتأخرين في الدفع
- رسوم بيانية تفاعلية لأداء النظام المالي
- تصدير التقارير إلى PDF و Excel

### 5. تكامل الواتساب
- رسائل تأكيد دفع تلقائية
- تذكيرات دفع شهرية قبل الاستحقاق
- رسائل جماعية للاجتماعات والفعاليات
- تكامل مع WhatsApp Business API أو Twilio

### 6. إعدادات النظام
- إدارة فئات الإيرادات والمصروفات
- إدارة المستخدمين (دعم متعدد المستخدمين مع الأدوار)
- نسخ احتياطي تلقائي مع جدولة
- إعدادات اللغة والعملة وتنسيق التاريخ
- أدوات تكوين وصيانة قاعدة البيانات

## المواصفات التقنية

- **الإطار**: Electron للتوافق عبر المنصات
- **الواجهة**: Bootstrap 5 مع دعم RTL كامل للعربية
- **قاعدة البيانات**: SQLite محلية مع إمكانيات النسخ الاحتياطي
- **التصميم**: متجاوب يعمل على أحجام شاشات مختلفة
- **الشبكة**: إمكانية المشاركة الشبكية للوصول متعدد الأجهزة
- **التوزيع**: حزمة تثبيت Windows (.exe)

## اختصارات لوحة المفاتيح

- `Ctrl+N`: إضافة عضو جديد
- `Ctrl+M`: قائمة الأعضاء
- `Ctrl+P`: تسجيل دفعة جديدة
- `Ctrl+E`: تسجيل مصروف جديد
- `Ctrl+R`: التقارير المالية
- `Ctrl+B`: إنشاء نسخة احتياطية
- `Ctrl+,`: الإعدادات
- `Ctrl+F`: التركيز على البحث

## هيكل المشروع

```
dewan/
├── src/
│   ├── main.js                 # العملية الرئيسية لـ Electron
│   └── renderer/
│       ├── index.html          # الواجهة الرئيسية
│       ├── css/
│       │   └── styles.css      # الأنماط المخصصة
│       └── js/
│           ├── app.js          # التطبيق الرئيسي
│           ├── database.js     # إدارة قاعدة البيانات
│           ├── utils.js        # الوظائف المساعدة
│           ├── navigation.js   # نظام التنقل
│           └── dashboard.js    # لوحة التحكم
├── assets/
│   └── icon.png               # أيقونة التطبيق
├── package.json               # تبعيات المشروع
└── README.md                  # هذا الملف
```

## الأمان وحماية البيانات

- تشفير قاعدة البيانات المحلية
- نسخ احتياطي تلقائي مجدول
- سجل تدقيق لجميع المعاملات المالية
- التحقق من صحة البيانات
- إدارة أذونات المستخدمين

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

## ملاحظات التطوير

### إضافة ميزات جديدة
1. أنشئ ملف JavaScript جديد في مجلد `src/renderer/js/`
2. أضف الوحدة إلى `index.html`
3. قم بتحديث نظام التنقل إذا لزم الأمر

### تخصيص الواجهة
- عدّل ملف `src/renderer/css/styles.css` للتخصيصات البصرية
- استخدم متغيرات Bootstrap CSS للتناسق
- تأكد من دعم RTL في جميع التخصيصات

### إضافة جداول قاعدة بيانات جديدة
- عدّل دالة `createTables()` في `database.js`
- أضف الفهارس المناسبة للأداء
- قم بتحديث دالة `insertDefaultData()` إذا لزم الأمر
