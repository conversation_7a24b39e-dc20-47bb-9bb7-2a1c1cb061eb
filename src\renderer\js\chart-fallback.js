// Chart.js Fallback - نسخة مبسطة للرسوم البيانية
// Simple charts implementation when Chart.js is not available

console.log('📊 Loading Chart.js fallback...');

// Simple Chart implementation
class SimpleChart {
    constructor(ctx, config) {
        this.ctx = ctx;
        this.config = config;
        this.canvas = ctx.canvas || ctx;
        this.type = config.type;
        this.data = config.data;
        this.options = config.options || {};
        
        this.render();
    }
    
    render() {
        if (!this.canvas) return;
        
        // Clear canvas
        const parent = this.canvas.parentElement;
        if (!parent) return;
        
        // Create HTML-based chart
        switch (this.type) {
            case 'line':
                this.renderLineChart(parent);
                break;
            case 'bar':
                this.renderBarChart(parent);
                break;
            case 'doughnut':
            case 'pie':
                this.renderPieChart(parent);
                break;
            default:
                this.renderFallbackMessage(parent);
        }
    }
    
    renderLine<PERSON>hart(parent) {
        const { labels, datasets } = this.data;
        
        let html = '<div class="simple-chart line-chart">';
        html += '<div class="chart-title">الرسم البياني الخطي</div>';
        
        // Create simple line representation
        html += '<div class="chart-container">';
        html += '<div class="chart-legend">';
        datasets.forEach((dataset, index) => {
            const color = dataset.borderColor || this.getColor(index);
            html += `<div class="legend-item">
                <span class="legend-color" style="background-color: ${color}"></span>
                <span class="legend-label">${dataset.label}</span>
            </div>`;
        });
        html += '</div>';
        
        // Simple data table
        html += '<div class="chart-data-table">';
        html += '<table class="table table-sm">';
        html += '<thead><tr><th>التاريخ</th>';
        datasets.forEach(dataset => {
            html += `<th>${dataset.label}</th>`;
        });
        html += '</tr></thead><tbody>';
        
        labels.forEach((label, index) => {
            html += `<tr><td>${label}</td>`;
            datasets.forEach(dataset => {
                const value = dataset.data[index] || 0;
                html += `<td>${this.formatValue(value)}</td>`;
            });
            html += '</tr>';
        });
        
        html += '</tbody></table></div>';
        html += '</div></div>';
        
        parent.innerHTML = html;
    }
    
    renderBarChart(parent) {
        const { labels, datasets } = this.data;
        
        let html = '<div class="simple-chart bar-chart">';
        html += '<div class="chart-title">الرسم البياني العمودي</div>';
        
        // Create simple bar representation
        html += '<div class="chart-container">';
        html += '<div class="chart-legend">';
        datasets.forEach((dataset, index) => {
            const color = dataset.backgroundColor || this.getColor(index);
            html += `<div class="legend-item">
                <span class="legend-color" style="background-color: ${color}"></span>
                <span class="legend-label">${dataset.label}</span>
            </div>`;
        });
        html += '</div>';
        
        // Simple bar representation using progress bars
        html += '<div class="chart-bars">';
        labels.forEach((label, index) => {
            html += `<div class="bar-group">`;
            html += `<div class="bar-label">${label}</div>`;
            
            datasets.forEach((dataset, datasetIndex) => {
                const value = dataset.data[index] || 0;
                const maxValue = Math.max(...dataset.data);
                const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;
                const color = dataset.backgroundColor || this.getColor(datasetIndex);
                
                html += `<div class="bar-item">
                    <div class="bar-info">
                        <span class="bar-dataset">${dataset.label}</span>
                        <span class="bar-value">${this.formatValue(value)}</span>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" style="width: ${percentage}%; background-color: ${color}"></div>
                    </div>
                </div>`;
            });
            
            html += '</div>';
        });
        html += '</div>';
        html += '</div></div>';
        
        parent.innerHTML = html;
    }
    
    renderPieChart(parent) {
        const { labels, datasets } = this.data;
        const dataset = datasets[0];
        const data = dataset.data;
        const total = data.reduce((sum, value) => sum + value, 0);
        
        let html = '<div class="simple-chart pie-chart">';
        html += '<div class="chart-title">الرسم البياني الدائري</div>';
        
        html += '<div class="chart-container">';
        html += '<div class="pie-segments">';
        
        labels.forEach((label, index) => {
            const value = data[index] || 0;
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
            const color = dataset.backgroundColor?.[index] || this.getColor(index);
            
            html += `<div class="pie-segment">
                <div class="segment-info">
                    <span class="segment-color" style="background-color: ${color}"></span>
                    <span class="segment-label">${label}</span>
                    <span class="segment-value">${this.formatValue(value)} (${percentage}%)</span>
                </div>
                <div class="progress" style="height: 15px;">
                    <div class="progress-bar" style="width: ${percentage}%; background-color: ${color}"></div>
                </div>
            </div>`;
        });
        
        html += '</div></div></div>';
        
        parent.innerHTML = html;
    }
    
    renderFallbackMessage(parent) {
        parent.innerHTML = `
            <div class="alert alert-info text-center">
                <i class="bi bi-info-circle fs-1"></i>
                <h5 class="mt-3">الرسم البياني غير متاح</h5>
                <p>يتم عرض البيانات في شكل جدول بدلاً من الرسم البياني</p>
            </div>
        `;
    }
    
    formatValue(value) {
        if (typeof value === 'number') {
            return value.toLocaleString('ar-JO', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 3
            });
        }
        return value;
    }
    
    getColor(index) {
        const colors = [
            '#198754', '#dc3545', '#0d6efd', '#ffc107', '#6f42c1',
            '#fd7e14', '#20c997', '#e83e8c', '#6c757d', '#343a40'
        ];
        return colors[index % colors.length];
    }
    
    destroy() {
        // Cleanup if needed
        if (this.canvas && this.canvas.parentElement) {
            this.canvas.parentElement.innerHTML = '';
        }
    }
}

// Create Chart global if not available
if (typeof Chart === 'undefined') {
    console.log('📊 Chart.js not found, using fallback implementation');
    window.Chart = SimpleChart;
} else {
    console.log('📊 Chart.js is available');
}

// Add CSS for simple charts
const chartCSS = `
<style>
.simple-chart {
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: white;
    margin: 10px 0;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
    color: #495057;
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 15px;
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    display: inline-block;
}

.legend-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.chart-data-table {
    max-height: 300px;
    overflow-y: auto;
}

.chart-bars {
    max-height: 400px;
    overflow-y: auto;
}

.bar-group {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 5px;
}

.bar-label {
    font-weight: bold;
    margin-bottom: 8px;
    color: #495057;
}

.bar-item {
    margin-bottom: 8px;
}

.bar-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 3px;
    font-size: 0.9rem;
}

.bar-dataset {
    color: #6c757d;
}

.bar-value {
    font-weight: bold;
    color: #495057;
}

.pie-segments {
    max-height: 400px;
    overflow-y: auto;
}

.pie-segment {
    margin-bottom: 10px;
    padding: 8px;
    border: 1px solid #e9ecef;
    border-radius: 5px;
}

.segment-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.segment-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.segment-label {
    flex: 1;
    color: #495057;
}

.segment-value {
    font-weight: bold;
    color: #495057;
}
</style>
`;

// Inject CSS
if (typeof document !== 'undefined') {
    document.head.insertAdjacentHTML('beforeend', chartCSS);
}

console.log('✅ Chart fallback system loaded successfully');
