// Navigation system for the application

class NavigationManager {
    constructor() {
        this.currentPage = 'dashboard';
        this.pages = {
            dashboard: {
                title: 'لوحة التحكم',
                icon: 'bi-speedometer2',
                loader: () => this.loadDashboard()
            },
            members: {
                title: 'الأعضاء',
                icon: 'bi-people',
                loader: () => this.loadMembers()
            },
            payments: {
                title: 'المدفوعات',
                icon: 'bi-credit-card',
                loader: () => this.loadPayments()
            },
            expenses: {
                title: 'المصروفات',
                icon: 'bi-receipt',
                loader: () => this.loadExpenses()
            },
            reports: {
                title: 'التقارير',
                icon: 'bi-graph-up',
                loader: () => this.loadReports()
            },
            whatsapp: {
                title: 'واتساب',
                icon: 'bi-whatsapp',
                loader: () => this.loadWhatsApp()
            },
            settings: {
                title: 'الإعدادات',
                icon: 'bi-gear',
                loader: () => this.loadSettings()
            }
        };
        
        this.init();
    }

    init() {
        // Set up navigation event listeners
        document.querySelectorAll('[data-page]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = e.currentTarget.getAttribute('data-page');
                this.navigateTo(page);
            });
        });

        // Set up menu event listeners
        this.setupMenuListeners();

        // Load initial page
        this.navigateTo('dashboard');
    }

    setupMenuListeners() {
        const { ipcRenderer } = require('electron');

        // Menu event listeners
        ipcRenderer.on('menu-backup', () => this.handleBackup());
        ipcRenderer.on('menu-import', () => this.handleImport());
        ipcRenderer.on('menu-export', () => this.handleExport());
        ipcRenderer.on('menu-settings', () => this.navigateTo('settings'));
        ipcRenderer.on('menu-new-member', () => this.showNewMemberModal());
        ipcRenderer.on('menu-members-list', () => this.navigateTo('members'));
        ipcRenderer.on('menu-new-payment', () => this.showNewPaymentModal());
        ipcRenderer.on('menu-new-expense', () => this.showNewExpenseModal());
        ipcRenderer.on('menu-financial-reports', () => this.navigateTo('reports'));
        ipcRenderer.on('menu-monthly-report', () => this.showMonthlyReport());
        ipcRenderer.on('menu-annual-report', () => this.showAnnualReport());
        ipcRenderer.on('menu-overdue-report', () => this.showOverdueReport());
    }

    async navigateTo(page) {
        if (!this.pages[page]) {
            console.error(`Page ${page} not found`);
            return;
        }

        // Update navigation state
        this.updateNavigation(page);
        
        // Show loading
        UIUtils.showLoading();

        try {
            // Hide all pages
            document.querySelectorAll('.page').forEach(p => {
                p.style.display = 'none';
                p.classList.remove('active');
            });

            // Load page content
            await this.pages[page].loader();

            // Show the target page
            const pageElement = document.getElementById(`${page}-page`);
            if (pageElement) {
                pageElement.style.display = 'block';
                pageElement.classList.add('active');
            }

            this.currentPage = page;
        } catch (error) {
            console.error(`Error loading page ${page}:`, error);
            UIUtils.showNotification(`خطأ في تحميل الصفحة: ${error.message}`, 'danger');
        } finally {
            UIUtils.hideLoading();
        }
    }

    updateNavigation(page) {
        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-page') === page) {
                link.classList.add('active');
            }
        });

        // Update page title
        const pageInfo = this.pages[page];
        if (pageInfo) {
            document.title = `${pageInfo.title} - نظام إدارة الديوان`;
        }
    }

    // Page loaders
    async loadDashboard() {
        // Dashboard content is already in HTML, just update data
        if (window.DashboardManager) {
            await window.DashboardManager.loadData();
        }
    }

    async loadMembers() {
        const pageElement = document.getElementById('members-page');
        if (!pageElement.innerHTML.trim()) {
            pageElement.innerHTML = await this.loadPageTemplate('members');
        }
        
        if (window.MembersManager) {
            await window.MembersManager.loadMembers();
        }
    }

    async loadPayments() {
        const pageElement = document.getElementById('payments-page');
        if (!pageElement.innerHTML.trim()) {
            pageElement.innerHTML = await this.loadPageTemplate('payments');
        }
        
        if (window.PaymentsManager) {
            await window.PaymentsManager.loadPayments();
        }
    }

    async loadExpenses() {
        const pageElement = document.getElementById('expenses-page');
        if (!pageElement.innerHTML.trim()) {
            pageElement.innerHTML = await this.loadPageTemplate('expenses');
        }
        
        if (window.ExpensesManager) {
            await window.ExpensesManager.loadExpenses();
        }
    }

    async loadReports() {
        const pageElement = document.getElementById('reports-page');
        if (!pageElement.innerHTML.trim()) {
            pageElement.innerHTML = await this.loadPageTemplate('reports');
        }
        
        if (window.ReportsManager) {
            await window.ReportsManager.loadReports();
        }
    }

    async loadWhatsApp() {
        const pageElement = document.getElementById('whatsapp-page');
        if (!pageElement.innerHTML.trim()) {
            pageElement.innerHTML = await this.loadPageTemplate('whatsapp');
        }
        
        if (window.WhatsAppManager) {
            await window.WhatsAppManager.loadMessages();
        }
    }

    async loadSettings() {
        const pageElement = document.getElementById('settings-page');
        if (!pageElement.innerHTML.trim()) {
            pageElement.innerHTML = await this.loadPageTemplate('settings');
        }
        
        if (window.SettingsManager) {
            await window.SettingsManager.loadSettings();
        }
    }

    async loadPageTemplate(pageName) {
        // This would load page templates from separate HTML files
        // For now, we'll return basic templates
        const templates = {
            members: `
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="bi bi-people"></i>
                            إدارة الأعضاء
                        </h2>
                    </div>
                </div>
                <div id="members-content">
                    <!-- Members content will be loaded here -->
                </div>
            `,
            payments: `
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="bi bi-credit-card"></i>
                            إدارة المدفوعات
                        </h2>
                    </div>
                </div>
                <div id="payments-content">
                    <!-- Payments content will be loaded here -->
                </div>
            `,
            expenses: `
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="bi bi-receipt"></i>
                            إدارة المصروفات
                        </h2>
                    </div>
                </div>
                <div id="expenses-content">
                    <!-- Expenses content will be loaded here -->
                </div>
            `,
            reports: `
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="bi bi-graph-up"></i>
                            التقارير المالية
                        </h2>
                    </div>
                </div>
                <div id="reports-content">
                    <!-- Reports content will be loaded here -->
                </div>
            `,
            whatsapp: `
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="bi bi-whatsapp"></i>
                            إدارة الواتساب
                        </h2>
                    </div>
                </div>
                <div id="whatsapp-content">
                    <!-- WhatsApp content will be loaded here -->
                </div>
            `,
            settings: `
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="bi bi-gear"></i>
                            إعدادات النظام
                        </h2>
                    </div>
                </div>
                <div id="settings-content">
                    <!-- Settings content will be loaded here -->
                </div>
            `
        };

        return templates[pageName] || '<div>الصفحة غير متوفرة</div>';
    }

    // Menu handlers
    async handleBackup() {
        try {
            const { ipcRenderer } = require('electron');
            const result = await ipcRenderer.invoke('show-save-dialog', {
                title: 'حفظ النسخة الاحتياطية',
                defaultPath: `backup-${moment().format('YYYY-MM-DD')}.db`,
                filters: [
                    { name: 'Database Files', extensions: ['db'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                await database.backup(result.filePath);
                UIUtils.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }
        } catch (error) {
            console.error('Backup error:', error);
            UIUtils.showNotification('خطأ في إنشاء النسخة الاحتياطية', 'danger');
        }
    }

    async handleImport() {
        try {
            const { ipcRenderer } = require('electron');
            const result = await ipcRenderer.invoke('show-open-dialog', {
                title: 'استيراد البيانات',
                filters: [
                    { name: 'CSV Files', extensions: ['csv'] },
                    { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
                    { name: 'All Files', extensions: ['*'] }
                ],
                properties: ['openFile']
            });

            if (!result.canceled && result.filePaths.length > 0) {
                // Handle import logic here
                UIUtils.showNotification('سيتم تطوير ميزة الاستيراد قريباً', 'info');
            }
        } catch (error) {
            console.error('Import error:', error);
            UIUtils.showNotification('خطأ في استيراد البيانات', 'danger');
        }
    }

    async handleExport() {
        try {
            const { ipcRenderer } = require('electron');
            const result = await ipcRenderer.invoke('show-save-dialog', {
                title: 'تصدير البيانات',
                defaultPath: `export-${moment().format('YYYY-MM-DD')}.csv`,
                filters: [
                    { name: 'CSV Files', extensions: ['csv'] },
                    { name: 'Excel Files', extensions: ['xlsx'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                // Handle export logic here
                UIUtils.showNotification('سيتم تطوير ميزة التصدير قريباً', 'info');
            }
        } catch (error) {
            console.error('Export error:', error);
            UIUtils.showNotification('خطأ في تصدير البيانات', 'danger');
        }
    }

    showNewMemberModal() {
        if (window.MembersManager) {
            window.MembersManager.showAddMemberModal();
        } else {
            this.navigateTo('members');
        }
    }

    showNewPaymentModal() {
        if (window.PaymentsManager) {
            window.PaymentsManager.showAddPaymentModal();
        } else {
            this.navigateTo('payments');
        }
    }

    showNewExpenseModal() {
        if (window.ExpensesManager) {
            window.ExpensesManager.showAddExpenseModal();
        } else {
            this.navigateTo('expenses');
        }
    }

    showMonthlyReport() {
        this.navigateTo('reports');
        // Additional logic to show monthly report
    }

    showAnnualReport() {
        this.navigateTo('reports');
        // Additional logic to show annual report
    }

    showOverdueReport() {
        this.navigateTo('reports');
        // Additional logic to show overdue report
    }
}

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.navigationManager = new NavigationManager();
});
