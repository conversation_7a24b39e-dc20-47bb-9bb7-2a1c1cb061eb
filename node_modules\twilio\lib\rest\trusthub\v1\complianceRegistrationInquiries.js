"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trusthub
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplianceRegistrationInquiriesInstance = exports.ComplianceRegistrationInquiriesListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
function ComplianceRegistrationInquiriesListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/ComplianceInquiries/Registration/RegulatoryCompliance/GB/Initialize`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["endUserType"] === null || params["endUserType"] === undefined) {
            throw new Error("Required parameter \"params['endUserType']\" missing.");
        }
        if (params["phoneNumberType"] === null ||
            params["phoneNumberType"] === undefined) {
            throw new Error("Required parameter \"params['phoneNumberType']\" missing.");
        }
        let data = {};
        data["EndUserType"] = params["endUserType"];
        data["PhoneNumberType"] = params["phoneNumberType"];
        if (params["businessIdentityType"] !== undefined)
            data["BusinessIdentityType"] = params["businessIdentityType"];
        if (params["businessRegistrationAuthority"] !== undefined)
            data["BusinessRegistrationAuthority"] =
                params["businessRegistrationAuthority"];
        if (params["businessLegalName"] !== undefined)
            data["BusinessLegalName"] = params["businessLegalName"];
        if (params["notificationEmail"] !== undefined)
            data["NotificationEmail"] = params["notificationEmail"];
        if (params["acceptedNotificationReceipt"] !== undefined)
            data["AcceptedNotificationReceipt"] = serialize.bool(params["acceptedNotificationReceipt"]);
        if (params["businessRegistrationNumber"] !== undefined)
            data["BusinessRegistrationNumber"] = params["businessRegistrationNumber"];
        if (params["businessWebsiteUrl"] !== undefined)
            data["BusinessWebsiteUrl"] = params["businessWebsiteUrl"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["authorizedRepresentative1FirstName"] !== undefined)
            data["AuthorizedRepresentative1FirstName"] =
                params["authorizedRepresentative1FirstName"];
        if (params["authorizedRepresentative1LastName"] !== undefined)
            data["AuthorizedRepresentative1LastName"] =
                params["authorizedRepresentative1LastName"];
        if (params["authorizedRepresentative1Phone"] !== undefined)
            data["AuthorizedRepresentative1Phone"] =
                params["authorizedRepresentative1Phone"];
        if (params["authorizedRepresentative1Email"] !== undefined)
            data["AuthorizedRepresentative1Email"] =
                params["authorizedRepresentative1Email"];
        if (params["authorizedRepresentative1DateOfBirth"] !== undefined)
            data["AuthorizedRepresentative1DateOfBirth"] =
                params["authorizedRepresentative1DateOfBirth"];
        if (params["addressStreet"] !== undefined)
            data["AddressStreet"] = params["addressStreet"];
        if (params["addressStreetSecondary"] !== undefined)
            data["AddressStreetSecondary"] = params["addressStreetSecondary"];
        if (params["addressCity"] !== undefined)
            data["AddressCity"] = params["addressCity"];
        if (params["addressSubdivision"] !== undefined)
            data["AddressSubdivision"] = params["addressSubdivision"];
        if (params["addressPostalCode"] !== undefined)
            data["AddressPostalCode"] = params["addressPostalCode"];
        if (params["addressCountryCode"] !== undefined)
            data["AddressCountryCode"] = params["addressCountryCode"];
        if (params["emergencyAddressStreet"] !== undefined)
            data["EmergencyAddressStreet"] = params["emergencyAddressStreet"];
        if (params["emergencyAddressStreetSecondary"] !== undefined)
            data["EmergencyAddressStreetSecondary"] =
                params["emergencyAddressStreetSecondary"];
        if (params["emergencyAddressCity"] !== undefined)
            data["EmergencyAddressCity"] = params["emergencyAddressCity"];
        if (params["emergencyAddressSubdivision"] !== undefined)
            data["EmergencyAddressSubdivision"] =
                params["emergencyAddressSubdivision"];
        if (params["emergencyAddressPostalCode"] !== undefined)
            data["EmergencyAddressPostalCode"] = params["emergencyAddressPostalCode"];
        if (params["emergencyAddressCountryCode"] !== undefined)
            data["EmergencyAddressCountryCode"] =
                params["emergencyAddressCountryCode"];
        if (params["useAddressAsEmergencyAddress"] !== undefined)
            data["UseAddressAsEmergencyAddress"] = serialize.bool(params["useAddressAsEmergencyAddress"]);
        if (params["fileName"] !== undefined)
            data["FileName"] = params["fileName"];
        if (params["file"] !== undefined)
            data["File"] = params["file"];
        if (params["firstName"] !== undefined)
            data["FirstName"] = params["firstName"];
        if (params["lastName"] !== undefined)
            data["LastName"] = params["lastName"];
        if (params["dateOfBirth"] !== undefined)
            data["DateOfBirth"] = params["dateOfBirth"];
        if (params["individualEmail"] !== undefined)
            data["IndividualEmail"] = params["individualEmail"];
        if (params["individualPhone"] !== undefined)
            data["IndividualPhone"] = params["individualPhone"];
        if (params["isIsvEmbed"] !== undefined)
            data["IsIsvEmbed"] = serialize.bool(params["isIsvEmbed"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ComplianceRegistrationInquiriesInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ComplianceRegistrationInquiriesListInstance = ComplianceRegistrationInquiriesListInstance;
class ComplianceRegistrationInquiriesInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.inquiryId = payload.inquiry_id;
        this.inquirySessionToken = payload.inquiry_session_token;
        this.registrationId = payload.registration_id;
        this.url = payload.url;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            inquiryId: this.inquiryId,
            inquirySessionToken: this.inquirySessionToken,
            registrationId: this.registrationId,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ComplianceRegistrationInquiriesInstance = ComplianceRegistrationInquiriesInstance;
